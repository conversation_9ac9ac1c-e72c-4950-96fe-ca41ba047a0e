import{r,a as Ft}from"./vendor-746985b6.js";import{u as lt,L as St,a as Ut,b as Ht,N as _e,B as Bt,R as qt,c as ge}from"./router-93f2cf72.js";(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const c of document.querySelectorAll('link[rel="modulepreload"]'))m(c);new MutationObserver(c=>{for(const n of c)if(n.type==="childList")for(const h of n.addedNodes)h.tagName==="LINK"&&h.rel==="modulepreload"&&m(h)}).observe(document,{childList:!0,subtree:!0});function i(c){const n={};return c.integrity&&(n.integrity=c.integrity),c.referrerPolicy&&(n.referrerPolicy=c.referrerPolicy),c.crossOrigin==="use-credentials"?n.credentials="include":c.crossOrigin==="anonymous"?n.credentials="omit":n.credentials="same-origin",n}function m(c){if(c.ep)return;c.ep=!0;const n=i(c);fetch(c.href,n)}})();var Rt={exports:{}},Qe={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Wt=r,Jt=Symbol.for("react.element"),Gt=Symbol.for("react.fragment"),Kt=Object.prototype.hasOwnProperty,Qt=Wt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Yt={key:!0,ref:!0,__self:!0,__source:!0};function jt(a,s,i){var m,c={},n=null,h=null;i!==void 0&&(n=""+i),s.key!==void 0&&(n=""+s.key),s.ref!==void 0&&(h=s.ref);for(m in s)Kt.call(s,m)&&!Yt.hasOwnProperty(m)&&(c[m]=s[m]);if(a&&a.defaultProps)for(m in s=a.defaultProps,s)c[m]===void 0&&(c[m]=s[m]);return{$$typeof:Jt,type:a,key:n,ref:h,props:c,_owner:Qt.current}}Qe.Fragment=Gt;Qe.jsx=jt;Qe.jsxs=jt;Rt.exports=Qe;var ot=Rt.exports;const xe=ot.Fragment,e=ot.jsx,t=ot.jsxs;var Mt,pt=Ft;Mt=pt.createRoot,pt.hydrateRoot;let Zt={data:""},Xt=a=>typeof window=="object"?((a?a.querySelector("#_goober"):window._goober)||Object.assign((a||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:a||Zt,ea=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,ta=/\/\*[^]*?\*\/|  +/g,yt=/\n+/g,we=(a,s)=>{let i="",m="",c="";for(let n in a){let h=a[n];n[0]=="@"?n[1]=="i"?i=n+" "+h+";":m+=n[1]=="f"?we(h,n):n+"{"+we(h,n[1]=="k"?"":s)+"}":typeof h=="object"?m+=we(h,s?s.replace(/([^,])+/g,y=>n.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,k=>/&/.test(k)?k.replace(/&/g,y):y?y+" "+k:k)):n):h!=null&&(n=/^--/.test(n)?n:n.replace(/[A-Z]/g,"-$&").toLowerCase(),c+=we.p?we.p(n,h):n+":"+h+";")}return i+(s&&c?s+"{"+c+"}":c)+m},pe={},$t=a=>{if(typeof a=="object"){let s="";for(let i in a)s+=i+$t(a[i]);return s}return a},aa=(a,s,i,m,c)=>{let n=$t(a),h=pe[n]||(pe[n]=(k=>{let x=0,_=11;for(;x<k.length;)_=101*_+k.charCodeAt(x++)>>>0;return"go"+_})(n));if(!pe[h]){let k=n!==a?a:(x=>{let _,S,b=[{}];for(;_=ea.exec(x.replace(ta,""));)_[4]?b.shift():_[3]?(S=_[3].replace(yt," ").trim(),b.unshift(b[0][S]=b[0][S]||{})):b[0][_[1]]=_[2].replace(yt," ").trim();return b[0]})(a);pe[h]=we(c?{["@keyframes "+h]:k}:k,i?"":"."+h)}let y=i&&pe.g?pe.g:null;return i&&(pe.g=pe[h]),((k,x,_,S)=>{S?x.data=x.data.replace(S,k):x.data.indexOf(k)===-1&&(x.data=_?k+x.data:x.data+k)})(pe[h],s,m,y),h},ra=(a,s,i)=>a.reduce((m,c,n)=>{let h=s[n];if(h&&h.call){let y=h(i),k=y&&y.props&&y.props.className||/^go/.test(y)&&y;h=k?"."+k:y&&typeof y=="object"?y.props?"":we(y,""):y===!1?"":y}return m+c+(h??"")},"");function Ye(a){let s=this||{},i=a.call?a(s.p):a;return aa(i.unshift?i.raw?ra(i,[].slice.call(arguments,1),s.p):i.reduce((m,c)=>Object.assign(m,c&&c.call?c(s.p):c),{}):i,Xt(s.target),s.g,s.o,s.k)}let Tt,tt,at;Ye.bind({g:1});let be=Ye.bind({k:1});function sa(a,s,i,m){we.p=s,Tt=a,tt=i,at=m}function ke(a,s){let i=this||{};return function(){let m=arguments;function c(n,h){let y=Object.assign({},n),k=y.className||c.className;i.p=Object.assign({theme:tt&&tt()},y),i.o=/ *go\d+/.test(k),y.className=Ye.apply(i,m)+(k?" "+k:""),s&&(y.ref=h);let x=a;return a[0]&&(x=y.as||a,delete y.as),at&&x[0]&&at(y),Tt(x,y)}return s?s(c):c}}var na=a=>typeof a=="function",qe=(a,s)=>na(a)?a(s):a,la=(()=>{let a=0;return()=>(++a).toString()})(),It=(()=>{let a;return()=>{if(a===void 0&&typeof window<"u"){let s=matchMedia("(prefers-reduced-motion: reduce)");a=!s||s.matches}return a}})(),oa=20,At=(a,s)=>{switch(s.type){case 0:return{...a,toasts:[s.toast,...a.toasts].slice(0,oa)};case 1:return{...a,toasts:a.toasts.map(n=>n.id===s.toast.id?{...n,...s.toast}:n)};case 2:let{toast:i}=s;return At(a,{type:a.toasts.find(n=>n.id===i.id)?1:0,toast:i});case 3:let{toastId:m}=s;return{...a,toasts:a.toasts.map(n=>n.id===m||m===void 0?{...n,dismissed:!0,visible:!1}:n)};case 4:return s.toastId===void 0?{...a,toasts:[]}:{...a,toasts:a.toasts.filter(n=>n.id!==s.toastId)};case 5:return{...a,pausedAt:s.time};case 6:let c=s.time-(a.pausedAt||0);return{...a,pausedAt:void 0,toasts:a.toasts.map(n=>({...n,pauseDuration:n.pauseDuration+c}))}}},He=[],Ce={toasts:[],pausedAt:void 0},je=a=>{Ce=At(Ce,a),He.forEach(s=>{s(Ce)})},ia={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},ca=(a={})=>{let[s,i]=r.useState(Ce),m=r.useRef(Ce);r.useEffect(()=>(m.current!==Ce&&i(Ce),He.push(i),()=>{let n=He.indexOf(i);n>-1&&He.splice(n,1)}),[]);let c=s.toasts.map(n=>{var h,y,k;return{...a,...a[n.type],...n,removeDelay:n.removeDelay||((h=a[n.type])==null?void 0:h.removeDelay)||(a==null?void 0:a.removeDelay),duration:n.duration||((y=a[n.type])==null?void 0:y.duration)||(a==null?void 0:a.duration)||ia[n.type],style:{...a.style,...(k=a[n.type])==null?void 0:k.style,...n.style}}});return{...s,toasts:c}},da=(a,s="blank",i)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:s,ariaProps:{role:"status","aria-live":"polite"},message:a,pauseDuration:0,...i,id:(i==null?void 0:i.id)||la()}),ze=a=>(s,i)=>{let m=da(s,a,i);return je({type:2,toast:m}),m.id},Y=(a,s)=>ze("blank")(a,s);Y.error=ze("error");Y.success=ze("success");Y.loading=ze("loading");Y.custom=ze("custom");Y.dismiss=a=>{je({type:3,toastId:a})};Y.remove=a=>je({type:4,toastId:a});Y.promise=(a,s,i)=>{let m=Y.loading(s.loading,{...i,...i==null?void 0:i.loading});return typeof a=="function"&&(a=a()),a.then(c=>{let n=s.success?qe(s.success,c):void 0;return n?Y.success(n,{id:m,...i,...i==null?void 0:i.success}):Y.dismiss(m),c}).catch(c=>{let n=s.error?qe(s.error,c):void 0;n?Y.error(n,{id:m,...i,...i==null?void 0:i.error}):Y.dismiss(m)}),a};var ma=(a,s)=>{je({type:1,toast:{id:a,height:s}})},ha=()=>{je({type:5,time:Date.now()})},De=new Map,ua=1e3,ga=(a,s=ua)=>{if(De.has(a))return;let i=setTimeout(()=>{De.delete(a),je({type:4,toastId:a})},s);De.set(a,i)},pa=a=>{let{toasts:s,pausedAt:i}=ca(a);r.useEffect(()=>{if(i)return;let n=Date.now(),h=s.map(y=>{if(y.duration===1/0)return;let k=(y.duration||0)+y.pauseDuration-(n-y.createdAt);if(k<0){y.visible&&Y.dismiss(y.id);return}return setTimeout(()=>Y.dismiss(y.id),k)});return()=>{h.forEach(y=>y&&clearTimeout(y))}},[s,i]);let m=r.useCallback(()=>{i&&je({type:6,time:Date.now()})},[i]),c=r.useCallback((n,h)=>{let{reverseOrder:y=!1,gutter:k=8,defaultPosition:x}=h||{},_=s.filter(N=>(N.position||x)===(n.position||x)&&N.height),S=_.findIndex(N=>N.id===n.id),b=_.filter((N,j)=>j<S&&N.visible).length;return _.filter(N=>N.visible).slice(...y?[b+1]:[0,b]).reduce((N,j)=>N+(j.height||0)+k,0)},[s]);return r.useEffect(()=>{s.forEach(n=>{if(n.dismissed)ga(n.id,n.removeDelay);else{let h=De.get(n.id);h&&(clearTimeout(h),De.delete(n.id))}})},[s]),{toasts:s,handlers:{updateHeight:ma,startPause:ha,endPause:m,calculateOffset:c}}},ya=be`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,xa=be`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ba=be`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,fa=ke("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${a=>a.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${ya} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${xa} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${a=>a.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${ba} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,va=be`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,wa=ke("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${a=>a.secondary||"#e0e0e0"};
  border-right-color: ${a=>a.primary||"#616161"};
  animation: ${va} 1s linear infinite;
`,Na=be`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,ka=be`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,_a=ke("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${a=>a.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Na} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${ka} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${a=>a.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,Ca=ke("div")`
  position: absolute;
`,Sa=ke("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,Ra=be`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ja=ke("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${Ra} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,Ma=({toast:a})=>{let{icon:s,type:i,iconTheme:m}=a;return s!==void 0?typeof s=="string"?r.createElement(ja,null,s):s:i==="blank"?null:r.createElement(Sa,null,r.createElement(wa,{...m}),i!=="loading"&&r.createElement(Ca,null,i==="error"?r.createElement(fa,{...m}):r.createElement(_a,{...m})))},$a=a=>`
0% {transform: translate3d(0,${a*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,Ta=a=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${a*-150}%,-1px) scale(.6); opacity:0;}
`,Ia="0%{opacity:0;} 100%{opacity:1;}",Aa="0%{opacity:1;} 100%{opacity:0;}",Da=ke("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,Pa=ke("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,La=(a,s)=>{let i=a.includes("top")?1:-1,[m,c]=It()?[Ia,Aa]:[$a(i),Ta(i)];return{animation:s?`${be(m)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${be(c)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},Ea=r.memo(({toast:a,position:s,style:i,children:m})=>{let c=a.height?La(a.position||s||"top-center",a.visible):{opacity:0},n=r.createElement(Ma,{toast:a}),h=r.createElement(Pa,{...a.ariaProps},qe(a.message,a));return r.createElement(Da,{className:a.className,style:{...c,...i,...a.style}},typeof m=="function"?m({icon:n,message:h}):r.createElement(r.Fragment,null,n,h))});sa(r.createElement);var Oa=({id:a,className:s,style:i,onHeightUpdate:m,children:c})=>{let n=r.useCallback(h=>{if(h){let y=()=>{let k=h.getBoundingClientRect().height;m(a,k)};y(),new MutationObserver(y).observe(h,{subtree:!0,childList:!0,characterData:!0})}},[a,m]);return r.createElement("div",{ref:n,className:s,style:i},c)},Va=(a,s)=>{let i=a.includes("top"),m=i?{top:0}:{bottom:0},c=a.includes("center")?{justifyContent:"center"}:a.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:It()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${s*(i?1:-1)}px)`,...m,...c}},za=Ye`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,Fe=16,Fa=({reverseOrder:a,position:s="top-center",toastOptions:i,gutter:m,children:c,containerStyle:n,containerClassName:h})=>{let{toasts:y,handlers:k}=pa(i);return r.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:Fe,left:Fe,right:Fe,bottom:Fe,pointerEvents:"none",...n},className:h,onMouseEnter:k.startPause,onMouseLeave:k.endPause},y.map(x=>{let _=x.position||s,S=k.calculateOffset(x,{reverseOrder:a,gutter:m,defaultPosition:s}),b=Va(_,S);return r.createElement(Oa,{id:x.id,key:x.id,onHeightUpdate:k.updateHeight,className:x.visible?za:"",style:b},x.type==="custom"?qe(x.message,x):c?c(x):r.createElement(Ea,{toast:x,position:_}))}))};const Ua=()=>{const[a,s]=r.useState(""),[i,m]=r.useState(""),[c,n]=r.useState(!1),[h,y]=r.useState(!1),[k,x]=r.useState(""),_=lt(),{login:S}=le();return e("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:t("div",{className:"max-w-md w-full space-y-8",children:[t("div",{children:[e("div",{className:"flex justify-center",children:e("div",{className:"h-12 w-12 bg-blue-600 rounded-lg flex items-center justify-center",children:e("span",{className:"text-white font-bold text-lg",children:"CF"})})}),e("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to ChatFlow Pro"}),t("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",e(St,{to:"/register",className:"font-medium text-blue-600 hover:text-blue-500",children:"create a new account"})]}),t("div",{className:"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[e("p",{className:"text-sm text-blue-700 font-medium mb-2 text-center",children:"🧪 Test Credentials"}),t("div",{className:"text-xs text-blue-600 space-y-1 text-center",children:[t("div",{children:[e("strong",{children:"Email:"})," <EMAIL>"]}),t("div",{children:[e("strong",{children:"Password:"})," testpass123"]}),t("div",{className:"text-blue-500 mt-2",children:["Or try ",e("strong",{children:"<EMAIL>"})," / ",e("strong",{children:"admin123"})," for admin access"]})]})]})]}),t("form",{className:"mt-8 space-y-6",onSubmit:async N=>{N.preventDefault(),y(!0),x("");try{await S({email:a,password:i})&&_("/chat")}catch(j){x(j.message||"Login failed")}finally{y(!1)}},children:[k&&e("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg",children:k}),t("div",{className:"space-y-4",children:[t("div",{children:[e("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),e("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:a,onChange:N=>s(N.target.value),className:"mt-1 w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your email"})]}),t("div",{children:[e("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),t("div",{className:"mt-1 relative",children:[e("input",{id:"password",name:"password",type:c?"text":"password",autoComplete:"current-password",required:!0,value:i,onChange:N=>m(N.target.value),className:"w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your password"}),e("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>n(!c),children:c?e("span",{className:"h-5 w-5 text-gray-400",children:"🙈"}):e("span",{className:"h-5 w-5 text-gray-400",children:"👁️"})})]})]})]}),t("div",{className:"flex items-center justify-between",children:[t("div",{className:"flex items-center",children:[e("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),e("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-900",children:"Remember me"})]}),e("div",{className:"text-sm",children:e("button",{type:"button",onClick:()=>{alert("Password reset functionality coming soon!")},className:"font-medium text-blue-600 hover:text-blue-500",children:"Forgot your password?"})})]}),e("div",{children:e("button",{type:"submit",disabled:h,className:"w-full flex justify-center items-center bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50",children:h?t(xe,{children:[e("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Signing in..."]}):"Sign in"})}),t("div",{className:"mt-6",children:[t("div",{className:"relative",children:[e("div",{className:"absolute inset-0 flex items-center",children:e("div",{className:"w-full border-t border-gray-300"})}),e("div",{className:"relative flex justify-center text-sm",children:e("span",{className:"px-2 bg-gray-50 text-gray-500",children:"Or continue with"})})]}),t("div",{className:"mt-6 space-y-3",children:[t("button",{type:"button",onClick:()=>{const N={id:"demo_user",username:"demo_user",email:"<EMAIL>",display_name:"Demo User",is_vip:!1,is_staff:!1};localStorage.setItem("chatflow_user",JSON.stringify(N)),localStorage.setItem("chatflow_demo_mode","true"),_("/chat")},className:"w-full inline-flex justify-center py-3 px-4 border border-green-300 rounded-lg shadow-sm bg-gradient-to-r from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700 text-white font-medium transition-all duration-300 transform hover:scale-105",children:[e("span",{className:"mr-2",children:"⚡"}),"Try Demo Mode",e("span",{className:"ml-2",children:"🚀"})]}),t("div",{className:"grid grid-cols-2 gap-3",children:[t("button",{type:"button",onClick:()=>{alert("Google OAuth integration coming soon!")},className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",children:[e("span",{className:"mr-2",children:"🔍"}),"Google"]}),t("button",{type:"button",onClick:()=>{alert("GitHub OAuth integration coming soon!")},className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",children:[e("span",{className:"mr-2",children:"🐙"}),"GitHub"]})]})]})]})]})]})})},Ha=()=>{const[a,s]=r.useState({username:"",email:"",displayName:"",password:"",confirmPassword:""}),[i,m]=r.useState(!1),[c,n]=r.useState(!1),[h,y]=r.useState(!1),[k,x]=r.useState(""),_=lt(),{register:S}=le(),b=j=>{s({...a,[j.target.name]:j.target.value})};return e("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:t("div",{className:"max-w-md w-full space-y-8",children:[t("div",{children:[e("div",{className:"flex justify-center",children:e("div",{className:"h-12 w-12 bg-blue-600 rounded-lg flex items-center justify-center",children:e("span",{className:"text-white font-bold text-lg",children:"CF"})})}),e("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Create your account"}),t("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",e(St,{to:"/login",className:"font-medium text-blue-600 hover:text-blue-500",children:"sign in to your existing account"})]})]}),t("form",{className:"mt-8 space-y-6",onSubmit:async j=>{j.preventDefault(),y(!0),x("");try{await S(a)&&_("/chat")}catch(M){x(M.message||"Registration failed")}finally{y(!1)}},children:[k&&e("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg",children:k}),t("div",{className:"space-y-4",children:[t("div",{children:[e("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700",children:"Username"}),e("input",{id:"username",name:"username",type:"text",autoComplete:"username",required:!0,value:a.username,onChange:b,className:"mt-1 w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Choose a username"})]}),t("div",{children:[e("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),e("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:a.email,onChange:b,className:"mt-1 w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your email"})]}),t("div",{children:[e("label",{htmlFor:"displayName",className:"block text-sm font-medium text-gray-700",children:"Display Name (Optional)"}),e("input",{id:"displayName",name:"displayName",type:"text",value:a.displayName,onChange:b,className:"mt-1 w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"How others will see you"})]}),t("div",{children:[e("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),t("div",{className:"mt-1 relative",children:[e("input",{id:"password",name:"password",type:i?"text":"password",autoComplete:"new-password",required:!0,value:a.password,onChange:b,className:"w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Create a password"}),e("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>m(!i),children:i?e("span",{className:"h-5 w-5 text-gray-400",children:"🙈"}):e("span",{className:"h-5 w-5 text-gray-400",children:"👁️"})})]})]}),t("div",{children:[e("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirm Password"}),t("div",{className:"mt-1 relative",children:[e("input",{id:"confirmPassword",name:"confirmPassword",type:c?"text":"password",autoComplete:"new-password",required:!0,value:a.confirmPassword,onChange:b,className:"w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Confirm your password"}),e("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>n(!c),children:c?e("span",{className:"h-5 w-5 text-gray-400",children:"🙈"}):e("span",{className:"h-5 w-5 text-gray-400",children:"👁️"})})]})]})]}),t("div",{className:"flex items-center",children:[e("input",{id:"agree-terms",name:"agree-terms",type:"checkbox",required:!0,className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),t("label",{htmlFor:"agree-terms",className:"ml-2 block text-sm text-gray-900",children:["I agree to the"," ",e("button",{type:"button",onClick:()=>{alert("Terms of Service page coming soon!")},className:"text-blue-600 hover:text-blue-500 underline",children:"Terms of Service"})," ","and"," ",e("button",{type:"button",onClick:()=>{alert("Privacy Policy page coming soon!")},className:"text-blue-600 hover:text-blue-500 underline",children:"Privacy Policy"})]})]}),e("div",{children:e("button",{type:"submit",disabled:h,className:"w-full flex justify-center items-center bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50",children:h?t(xe,{children:[e("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Creating account..."]}):"Create account"})}),t("div",{className:"mt-6",children:[t("div",{className:"relative",children:[e("div",{className:"absolute inset-0 flex items-center",children:e("div",{className:"w-full border-t border-gray-300"})}),e("div",{className:"relative flex justify-center text-sm",children:e("span",{className:"px-2 bg-gray-50 text-gray-500",children:"Or continue with"})})]}),t("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[t("button",{type:"button",onClick:()=>{alert("Google OAuth integration coming soon!")},className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",children:[e("span",{className:"mr-2",children:"🔍"}),"Google"]}),t("button",{type:"button",onClick:()=>{alert("GitHub OAuth integration coming soon!")},className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",children:[e("span",{className:"mr-2",children:"🐙"}),"GitHub"]})]})]})]})]})})};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ba=a=>a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),qa=a=>a.replace(/^([A-Z])|[\s-_]+(\w)/g,(s,i,m)=>m?m.toUpperCase():i.toLowerCase()),xt=a=>{const s=qa(a);return s.charAt(0).toUpperCase()+s.slice(1)},Dt=(...a)=>a.filter((s,i,m)=>!!s&&s.trim()!==""&&m.indexOf(s)===i).join(" ").trim(),Wa=a=>{for(const s in a)if(s.startsWith("aria-")||s==="role"||s==="title")return!0};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Ja={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ga=r.forwardRef(({color:a="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:m,className:c="",children:n,iconNode:h,...y},k)=>r.createElement("svg",{ref:k,...Ja,width:s,height:s,stroke:a,strokeWidth:m?Number(i)*24/Number(s):i,className:Dt("lucide",c),...!n&&!Wa(y)&&{"aria-hidden":"true"},...y},[...h.map(([x,_])=>r.createElement(x,_)),...Array.isArray(n)?n:[n]]));/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const P=(a,s)=>{const i=r.forwardRef(({className:m,...c},n)=>r.createElement(Ga,{ref:n,iconNode:s,className:Dt(`lucide-${Ba(xt(a))}`,`lucide-${a}`,m),...c}));return i.displayName=xt(a),i};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ka=[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]],bt=P("activity",Ka);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qa=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],rt=P("bell",Qa);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ya=[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]],Le=P("bot",Ya);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Za=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],ft=P("calendar",Za);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xa=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],Ue=P("chart-column",Xa);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const er=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],tr=P("check",er);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ar=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],rr=P("chevron-down",ar);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sr=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],nr=P("chevron-up",sr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lr=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],Ee=P("circle-check-big",lr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const or=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]],$e=P("clock",or);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ir=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]],cr=P("copy",ir);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dr=[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]],G=P("crown",dr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mr=[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]],vt=P("dollar-sign",mr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hr=[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]],st=P("download",hr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ur=[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]],it=P("ellipsis-vertical",ur);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gr=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],ct=P("eye",gr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pr=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],yr=P("file-text",pr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xr=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],br=P("funnel",xr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fr=[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]],vr=P("gift",fr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wr=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],Pt=P("globe",wr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nr=[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]],We=P("hash",Nr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kr=[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]],Lt=P("headphones",kr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _r=[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]],Ze=P("heart",_r);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cr=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]],Sr=P("image",Cr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rr=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]],jr=P("info",Rr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mr=[["path",{d:"m5 8 6 6",key:"1wu5hv"}],["path",{d:"m4 14 6-6 2-3",key:"1k1g8d"}],["path",{d:"M2 5h12",key:"or177f"}],["path",{d:"M7 2h1",key:"1t2jsx"}],["path",{d:"m22 22-5-10-5 10",key:"don7ne"}],["path",{d:"M14 18h6",key:"1m8k6r"}]],$r=P("languages",Mr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tr=[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]],Ir=P("loader",Tr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ar=[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]],dt=P("lock",Ar);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dr=[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"m21 3-7 7",key:"1l2asr"}],["path",{d:"m3 21 7-7",key:"tjx5ai"}],["path",{d:"M9 21H3v-6",key:"wtvkvv"}]],Oe=P("maximize-2",Dr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pr=[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]],q=P("message-circle",Pr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lr=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]],ye=P("message-square",Lr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Er=[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M18.89 13.23A7.12 7.12 0 0 0 19 12v-2",key:"80xlxr"}],["path",{d:"M5 10v2a7 7 0 0 0 12 5",key:"p2k8kg"}],["path",{d:"M15 9.34V5a3 3 0 0 0-5.68-1.33",key:"1gzdoj"}],["path",{d:"M9 9v3a3 3 0 0 0 5.12 2.12",key:"r2i35w"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]],Or=P("mic-off",Er);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vr=[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]],Je=P("mic",Vr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zr=[["path",{d:"m14 10 7-7",key:"oa77jy"}],["path",{d:"M20 10h-6V4",key:"mjg0md"}],["path",{d:"m3 21 7-7",key:"tjx5ai"}],["path",{d:"M4 14h6v6",key:"rmj7iw"}]],Ve=P("minimize-2",zr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fr=[["rect",{x:"5",y:"2",width:"14",height:"20",rx:"7",key:"11ol66"}],["path",{d:"M12 6v4",key:"16clxf"}]],wt=P("mouse",Fr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ur=[["path",{d:"M13.234 20.252 21 12.3",key:"1cbrk9"}],["path",{d:"m16 6-8.414 8.586a2 2 0 0 0 0 2.828 2 2 0 0 0 2.828 0l8.414-8.586a4 4 0 0 0 0-5.656 4 4 0 0 0-5.656 0l-8.415 8.585a6 6 0 1 0 8.486 8.486",key:"1pkts6"}]],Nt=P("paperclip",Ur);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hr=[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]],Et=P("pen-line",Hr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Br=[["path",{d:"M10.1 13.9a14 14 0 0 0 3.732 2.668 1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2 18 18 0 0 1-12.728-5.272",key:"1wngk7"}],["path",{d:"M22 2 2 22",key:"y4kqgn"}],["path",{d:"M4.76 13.582A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 .244.473",key:"10hv5p"}]],et=P("phone-off",Br);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qr=[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]],Pe=P("phone",qr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wr=[["path",{d:"M12 17v5",key:"bb1du9"}],["path",{d:"M9 10.76a2 2 0 0 1-1.11 1.79l-1.78.9A2 2 0 0 0 5 15.24V16a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-.76a2 2 0 0 0-1.11-1.79l-1.78-.9A2 2 0 0 1 15 10.76V7a1 1 0 0 1 1-1 2 2 0 0 0 0-4H8a2 2 0 0 0 0 4 1 1 0 0 1 1 1z",key:"1nkz8b"}]],kt=P("pin",Wr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jr=[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]],Gr=P("play",Jr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kr=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],Ne=P("plus",Kr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qr=[["path",{d:"M4.9 19.1C1 15.2 1 8.8 4.9 4.9",key:"1vaf9d"}],["path",{d:"M7.8 16.2c-2.3-2.3-2.3-6.1 0-8.5",key:"u1ii0m"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}],["path",{d:"M16.2 7.8c2.3 2.3 2.3 6.1 0 8.5",key:"1j5fej"}],["path",{d:"M19.1 4.9C23 8.8 23 15.1 19.1 19",key:"10b0cb"}]],ie=P("radio",Qr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yr=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],Zr=P("refresh-cw",Yr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xr=[["path",{d:"M20 18v-2a4 4 0 0 0-4-4H4",key:"5vmcpk"}],["path",{d:"m9 17-5-5 5-5",key:"nvlc11"}]],_t=P("reply",Xr);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const es=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],ce=P("search",es);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ts=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]],mt=P("send",ts);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const as=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Te=P("settings",as);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rs=[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]],Ge=P("share-2",rs);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ss=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]],Se=P("shield",ss);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ns=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 14s1.5 2 4 2 4-2 4-2",key:"1y1vjs"}],["line",{x1:"9",x2:"9.01",y1:"9",y2:"9",key:"yxxnd0"}],["line",{x1:"15",x2:"15.01",y1:"9",y2:"9",key:"1p4y9e"}]],Ct=P("smile",ns);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ls=[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]],Be=P("sparkles",ls);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const os=[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]],is=P("square-pen",os);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cs=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],Re=P("star",cs);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ds=[["line",{x1:"10",x2:"14",y1:"2",y2:"2",key:"14vaq8"}],["line",{x1:"12",x2:"15",y1:"14",y2:"11",key:"17fdiu"}],["circle",{cx:"12",cy:"14",r:"8",key:"1e1u0o"}]],ms=P("timer",ds);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hs=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],ht=P("trash-2",hs);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const us=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],gs=P("trending-up",us);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ps=[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]],nt=P("triangle-alert",ps);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ys=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]],xs=P("user-plus",ys);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bs=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],fs=P("user",bs);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vs=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],K=P("users",vs);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ws=[["path",{d:"M10.66 6H14a2 2 0 0 1 2 2v2.5l5.248-3.062A.5.5 0 0 1 22 7.87v8.196",key:"w8jjjt"}],["path",{d:"M16 16a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h2",key:"1xawa7"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],Ns=P("video-off",ws);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ks=[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]],ut=P("video",ks);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _s=[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]],Cs=P("volume-2",_s);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ss=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],de=P("x",Ss);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rs=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]],Ke=P("zap",Rs),js=({rooms:a,selectedRoom:s,onRoomSelect:i,onlineUsers:m,unreadCounts:c,user:n,onShowProfile:h,onShowSettings:y})=>{var d,l;const[k,x]=r.useState(""),[_,S]=r.useState("rooms"),[b,N]=r.useState(!1),j=a.filter(o=>{var C;return o.name.toLowerCase().includes(k.toLowerCase())||((C=o.description)==null?void 0:C.toLowerCase().includes(k.toLowerCase()))}),M=o=>o.room_type==="direct"?e(q,{className:"w-4 h-4"}):o.room_type==="live"?e(ie,{className:"w-4 h-4 text-red-500"}):o.is_vip_only?e(G,{className:"w-4 h-4 text-purple-500"}):o.is_public?e(We,{className:"w-4 h-4"}):e(dt,{className:"w-4 h-4"}),E=o=>c[o]||0,v=({room:o})=>{var T;const C=(s==null?void 0:s.id)===o.id,p=E(o.id),D=p>0;return t("div",{onClick:()=>i(o),className:`flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-colors ${C?"bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300":"hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300"}`,children:[e("div",{className:"flex-shrink-0",children:M(o)}),t("div",{className:"flex-1 min-w-0",children:[t("div",{className:"flex items-center justify-between",children:[e("h3",{className:`text-sm font-medium truncate ${D?"font-bold":""}`,children:o.name}),D&&e("span",{className:"bg-blue-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center",children:p>99?"99+":p})]}),o.last_message&&t("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate mt-1",children:[(T=o.last_message.sender)==null?void 0:T.username,": ",o.last_message.content]}),t("div",{className:"flex items-center justify-between mt-1",children:[t("span",{className:"text-xs text-gray-400",children:[o.member_count," members"]}),o.last_message&&e("span",{className:"text-xs text-gray-400",children:new Date(o.last_message.created_at).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]})]})]})},w=({user:o})=>{var C;return t("div",{className:"flex items-center gap-3 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer",children:[t("div",{className:"relative",children:[e("div",{className:"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center",children:e("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:((C=o.display_name)==null?void 0:C[0])||o.username[0]})}),e("div",{className:"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800"})]}),t("div",{className:"flex-1 min-w-0",children:[e("p",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:o.display_name||o.username}),e("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:o.status||"Online"})]}),o.is_vip&&e(G,{className:"w-4 h-4 text-purple-500"})]})};return t("div",{className:"w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col h-full",children:[t("div",{className:"p-4 border-b border-gray-200 dark:border-gray-700",children:[t("div",{className:"flex items-center justify-between mb-4",children:[t("div",{className:"flex items-center gap-3",children:[e("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:e("span",{className:"text-white font-bold text-sm",children:"CF"})}),e("h1",{className:"text-lg font-bold text-gray-900 dark:text-white",children:"ChatFlow Pro"})]}),t("div",{className:"flex items-center gap-2",children:[e("button",{onClick:()=>N(!0),className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",title:"Create Room",children:e(Ne,{className:"w-5 h-5"})}),e("button",{onClick:y,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",title:"Settings",children:e(Te,{className:"w-5 h-5"})})]})]}),t("div",{className:"relative",children:[e(ce,{className:"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),e("input",{type:"text",placeholder:"Search rooms...",value:k,onChange:o=>x(o.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),t("div",{className:"flex border-b border-gray-200 dark:border-gray-700",children:[t("button",{onClick:()=>S("rooms"),className:`flex-1 flex items-center justify-center gap-2 py-3 text-sm font-medium transition-colors ${_==="rooms"?"text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"}`,children:[e(q,{className:"w-4 h-4"}),"Rooms"]}),t("button",{onClick:()=>S("users"),className:`flex-1 flex items-center justify-center gap-2 py-3 text-sm font-medium transition-colors ${_==="users"?"text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"}`,children:[e(K,{className:"w-4 h-4"}),"Online (",m.length,")"]})]}),e("div",{className:"flex-1 overflow-y-auto",children:_==="rooms"?e("div",{className:"p-4 space-y-2",children:j.length>0?j.map(o=>e(v,{room:o},o.id)):t("div",{className:"text-center py-8",children:[e(q,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),e("h3",{className:"text-sm font-medium text-gray-900 dark:text-white mb-1",children:"No rooms found"}),e("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:k?"Try a different search term":"Create your first room to get started"})]})}):e("div",{className:"p-4 space-y-2",children:m.length>0?m.map(o=>e(w,{user:o},o.id)):t("div",{className:"text-center py-8",children:[e(K,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),e("h3",{className:"text-sm font-medium text-gray-900 dark:text-white mb-1",children:"No users online"}),e("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Users will appear here when they come online"})]})})}),e("div",{className:"p-4 border-t border-gray-200 dark:border-gray-700",children:t("div",{className:"flex items-center gap-3",children:[t("div",{className:"relative",children:[e("div",{className:"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center",children:e("span",{className:"text-white font-medium",children:((d=n==null?void 0:n.display_name)==null?void 0:d[0])||((l=n==null?void 0:n.username)==null?void 0:l[0])||"U"})}),e("div",{className:"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800"})]}),t("div",{className:"flex-1 min-w-0",children:[e("p",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:(n==null?void 0:n.display_name)||(n==null?void 0:n.username)}),e("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:n!=null&&n.is_vip?"VIP Member":"Member"})]}),t("div",{className:"flex items-center gap-1",children:[(n==null?void 0:n.is_vip)&&e(G,{className:"w-4 h-4 text-purple-500"}),e("button",{onClick:h,className:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",children:e(it,{className:"w-4 h-4"})})]})]})})]})},Ms=({isOpen:a,onClose:s,onSelectResponse:i,user:m})=>{const[c,n]=r.useState([]),[h,y]=r.useState(""),[k,x]=r.useState("all"),[_,S]=r.useState(!1),[b,N]=r.useState(null),[j,M]=r.useState({title:"",content:"",category:"general",tags:[],isPrivate:!1}),E=[{id:"all",name:"All Responses",icon:ye},{id:"general",name:"General",icon:ye},{id:"support",name:"Support",icon:ye},{id:"sales",name:"Sales",icon:ye},{id:"technical",name:"Technical",icon:ye},{id:"vip",name:"VIP",icon:Re},{id:"greeting",name:"Greetings",icon:ye},{id:"closing",name:"Closing",icon:ye}],v=[{id:1,title:"Welcome Message",content:"Hello! Welcome to our chat platform. How can I assist you today?",category:"greeting",tags:["welcome","greeting"],isPrivate:!1,usageCount:45,lastUsed:new Date(Date.now()-864e5),createdBy:"System"},{id:2,title:"Technical Support",content:"I understand you're experiencing a technical issue. Let me help you resolve this. Could you please provide more details about the problem?",category:"technical",tags:["support","technical","help"],isPrivate:!1,usageCount:32,lastUsed:new Date(Date.now()-36e5),createdBy:"Support Team"},{id:3,title:"VIP Welcome",content:"Welcome to our VIP support! As a valued VIP member, you have priority access to our premium features and dedicated support team.",category:"vip",tags:["vip","welcome","premium"],isPrivate:!1,usageCount:18,lastUsed:new Date(Date.now()-72e5),createdBy:"VIP Team"},{id:4,title:"File Upload Help",content:"To upload files, simply click the paperclip icon in the chat input area. You can upload images, documents, and other files up to 10MB.",category:"support",tags:["files","upload","help"],isPrivate:!1,usageCount:28,lastUsed:new Date(Date.now()-18e5),createdBy:(m==null?void 0:m.username)||"Agent"},{id:5,title:"Video Call Instructions",content:"To start a video call, click the video camera icon in the chat header. Make sure you have granted camera and microphone permissions.",category:"technical",tags:["video","call","instructions"],isPrivate:!1,usageCount:22,lastUsed:new Date(Date.now()-54e5),createdBy:(m==null?void 0:m.username)||"Agent"},{id:6,title:"Closing Message",content:"Thank you for contacting us! If you have any other questions, please don't hesitate to reach out. Have a great day!",category:"closing",tags:["closing","thank you","goodbye"],isPrivate:!1,usageCount:67,lastUsed:new Date(Date.now()-9e5),createdBy:"System"}];r.useEffect(()=>{const g=localStorage.getItem("cannedResponses");g?n(JSON.parse(g)):(n(v),localStorage.setItem("cannedResponses",JSON.stringify(v)))},[]);const w=c.filter(g=>{const u=g.title.toLowerCase().includes(h.toLowerCase())||g.content.toLowerCase().includes(h.toLowerCase())||g.tags.some(V=>V.toLowerCase().includes(h.toLowerCase())),$=k==="all"||g.category===k;return u&&$}),d=()=>{if(!j.title.trim()||!j.content.trim())return;const g={id:Date.now(),...j,tags:j.tags.filter($=>$.trim()),usageCount:0,lastUsed:null,createdBy:(m==null?void 0:m.username)||"Agent",createdAt:new Date},u=[...c,g];n(u),localStorage.setItem("cannedResponses",JSON.stringify(u)),M({title:"",content:"",category:"general",tags:[],isPrivate:!1}),S(!1)},l=g=>{N(g),M({title:g.title,content:g.content,category:g.category,tags:g.tags,isPrivate:g.isPrivate})},o=()=>{if(!j.title.trim()||!j.content.trim())return;const g=c.map(u=>u.id===b.id?{...u,...j,tags:j.tags.filter($=>$.trim())}:u);n(g),localStorage.setItem("cannedResponses",JSON.stringify(g)),N(null),M({title:"",content:"",category:"general",tags:[],isPrivate:!1})},C=g=>{if(window.confirm("Are you sure you want to delete this response?")){const u=c.filter($=>$.id!==g);n(u),localStorage.setItem("cannedResponses",JSON.stringify(u))}},p=g=>{const u=c.map($=>$.id===g.id?{...$,usageCount:$.usageCount+1,lastUsed:new Date}:$);n(u),localStorage.setItem("cannedResponses",JSON.stringify(u)),i(g.content),s()},D=g=>{g.trim()&&!j.tags.includes(g.trim())&&M(u=>({...u,tags:[...u.tags,g.trim()]}))},T=g=>{M(u=>({...u,tags:u.tags.filter($=>$!==g)}))};return a?e("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:t("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl h-5/6 flex flex-col",children:[t("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700",children:[e("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Canned Responses"}),t("div",{className:"flex items-center space-x-2",children:[t("button",{onClick:()=>S(!0),className:"px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm flex items-center space-x-1",children:[e(Ne,{className:"w-4 h-4"}),e("span",{children:"New Response"})]}),e("button",{onClick:s,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:"×"})]})]}),t("div",{className:"flex flex-1 overflow-hidden",children:[t("div",{className:"w-64 border-r border-gray-200 dark:border-gray-700 p-4",children:[t("div",{className:"relative mb-4",children:[e(ce,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),e("input",{type:"text",placeholder:"Search responses...",value:h,onChange:g=>y(g.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"})]}),e("div",{className:"space-y-1",children:E.map(g=>{const u=g.icon,$=g.id==="all"?c.length:c.filter(V=>V.category===g.id).length;return t("button",{onClick:()=>x(g.id),className:`w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm transition-colors ${k===g.id?"bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}`,children:[t("div",{className:"flex items-center space-x-2",children:[e(u,{className:"w-4 h-4"}),e("span",{children:g.name})]}),e("span",{className:"text-xs bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded-full",children:$})]},g.id)})})]}),e("div",{className:"flex-1 flex flex-col",children:_||b?t("div",{className:"p-4 space-y-4",children:[e("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:b?"Edit Response":"Create New Response"}),t("div",{className:"grid grid-cols-2 gap-4",children:[t("div",{children:[e("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Title"}),e("input",{type:"text",value:j.title,onChange:g=>M(u=>({...u,title:g.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Response title..."})]}),t("div",{children:[e("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Category"}),e("select",{value:j.category,onChange:g=>M(u=>({...u,category:g.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:E.slice(1).map(g=>e("option",{value:g.id,children:g.name},g.id))})]})]}),t("div",{children:[e("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Content"}),e("textarea",{value:j.content,onChange:g=>M(u=>({...u,content:g.target.value})),rows:4,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Response content..."})]}),t("div",{children:[e("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Tags"}),e("div",{className:"flex flex-wrap gap-2 mb-2",children:j.tags.map((g,u)=>t("span",{className:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 px-2 py-1 rounded-full text-xs flex items-center space-x-1",children:[e("span",{children:g}),e("button",{onClick:()=>T(g),className:"text-blue-600 hover:text-blue-800",children:"×"})]},u))}),e("input",{type:"text",placeholder:"Add tags (press Enter)",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm",onKeyPress:g=>{g.key==="Enter"&&(D(g.target.value),g.target.value="")}})]}),t("div",{className:"flex items-center justify-between",children:[t("label",{className:"flex items-center space-x-2",children:[e("input",{type:"checkbox",checked:j.isPrivate,onChange:g=>M(u=>({...u,isPrivate:g.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),e("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Private (only visible to me)"})]}),t("div",{className:"flex space-x-2",children:[e("button",{onClick:()=>{S(!1),N(null),M({title:"",content:"",category:"general",tags:[],isPrivate:!1})},className:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200",children:"Cancel"}),e("button",{onClick:b?o:d,className:"px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg",children:b?"Update":"Create"})]})]})]}):t("div",{className:"flex-1 overflow-y-auto p-4",children:[e("div",{className:"space-y-3",children:w.map(g=>e("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors",children:t("div",{className:"flex items-start justify-between",children:[t("div",{className:"flex-1",children:[t("div",{className:"flex items-center space-x-2 mb-2",children:[e("h4",{className:"font-medium text-gray-900 dark:text-white",children:g.title}),e("span",{className:"text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full",children:g.category}),g.isPrivate&&e("span",{className:"text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300 px-2 py-1 rounded-full",children:"Private"})]}),e("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2 line-clamp-2",children:g.content}),t("div",{className:"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400",children:[t("div",{className:"flex items-center space-x-1",children:[e($e,{className:"w-3 h-3"}),t("span",{children:["Used ",g.usageCount," times"]})]}),g.lastUsed&&t("span",{children:["Last used ",g.lastUsed.toLocaleDateString()]}),t("span",{children:["by ",g.createdBy]})]}),g.tags.length>0&&e("div",{className:"flex flex-wrap gap-1 mt-2",children:g.tags.map((u,$)=>t("span",{className:"text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full",children:["#",u]},$))})]}),t("div",{className:"flex items-center space-x-1 ml-4",children:[e("button",{onClick:()=>p(g),className:"px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm",children:"Use"}),e("button",{onClick:()=>l(g),className:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:e(Et,{className:"w-4 h-4"})}),e("button",{onClick:()=>C(g.id),className:"p-1 text-gray-400 hover:text-red-500",children:e(ht,{className:"w-4 h-4"})})]})]})},g.id))}),w.length===0&&t("div",{className:"text-center py-8",children:[e(ye,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),e("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No responses found"}),e("p",{className:"text-gray-500 dark:text-gray-400",children:h?"Try adjusting your search terms.":"Create your first canned response to get started."})]})]})})]})]})}):null},$s=({message:a,onTranslate:s,user:i})=>{var o;const[m,c]=r.useState(!1),[n,h]=r.useState({}),[y,k]=r.useState("auto"),[x,_]=r.useState(null),[S,b]=r.useState(!1),[N,j]=r.useState(!1),M=[{code:"auto",name:"Auto Detect",flag:"🌐"},{code:"en",name:"English",flag:"🇺🇸"},{code:"es",name:"Spanish",flag:"🇪🇸"},{code:"fr",name:"French",flag:"🇫🇷"},{code:"de",name:"German",flag:"🇩🇪"},{code:"it",name:"Italian",flag:"🇮🇹"},{code:"pt",name:"Portuguese",flag:"🇵🇹"},{code:"ru",name:"Russian",flag:"🇷🇺"},{code:"ja",name:"Japanese",flag:"🇯🇵"},{code:"ko",name:"Korean",flag:"🇰🇷"},{code:"zh",name:"Chinese",flag:"🇨🇳"},{code:"ar",name:"Arabic",flag:"🇸🇦"},{code:"hi",name:"Hindi",flag:"🇮🇳"},{code:"th",name:"Thai",flag:"🇹🇭"},{code:"vi",name:"Vietnamese",flag:"🇻🇳"},{code:"nl",name:"Dutch",flag:"🇳🇱"},{code:"sv",name:"Swedish",flag:"🇸🇪"},{code:"da",name:"Danish",flag:"🇩🇰"},{code:"no",name:"Norwegian",flag:"🇳🇴"},{code:"fi",name:"Finnish",flag:"🇫🇮"}],E=async(C,p,D="auto")=>{var u;await new Promise($=>setTimeout($,1e3+Math.random()*1e3));const g=(u={"Hello, how can I help you?":{es:"Hola, ¿cómo puedo ayudarte?",fr:"Bonjour, comment puis-je vous aider?",de:"Hallo, wie kann ich Ihnen helfen?",it:"Ciao, come posso aiutarti?",pt:"Olá, como posso ajudá-lo?",ru:"Привет, как я могу вам помочь?",ja:"こんにちは、どのようにお手伝いできますか？",ko:"안녕하세요, 어떻게 도와드릴까요?",zh:"你好，我能为您做些什么？",ar:"مرحبا، كيف يمكنني مساعدتك؟",hi:"नमस्ते, मैं आपकी कैसे सहायता कर सकता हूँ?"},"Thank you for your help":{es:"Gracias por tu ayuda",fr:"Merci pour votre aide",de:"Danke für Ihre Hilfe",it:"Grazie per il tuo aiuto",pt:"Obrigado pela sua ajuda",ru:"Спасибо за вашу помощь",ja:"ご協力ありがとうございます",ko:"도움을 주셔서 감사합니다",zh:"谢谢你的帮助",ar:"شكرا لمساعدتك",hi:"आपकी सहायता के लिए धन्यवाद"}}[C])==null?void 0:u[p];return g?{translatedText:g,detectedSourceLanguage:D==="auto"?"en":D,confidence:.95}:{translatedText:`[${p.toUpperCase()}] ${C}`,detectedSourceLanguage:D==="auto"?"en":D,confidence:.85}},v=async C=>{const p={en:["the","and","is","in","to","of","a","that","it","with"],es:["el","la","de","que","y","a","en","un","es","se"],fr:["le","de","et","à","un","il","être","et","en","avoir"],de:["der","die","und","in","den","von","zu","das","mit","sich"],it:["il","di","che","e","la","per","un","in","con","del"],pt:["o","de","que","e","do","da","em","um","para","é"],ru:["в","и","не","на","я","быть","он","с","что","а"],ja:["の","に","は","を","た","が","で","て","と","し"],zh:["的","一","是","在","不","了","有","和","人","这"],ar:["في","من","إلى","على","أن","هذا","هذه","التي","الذي","كان"]},D=C.toLowerCase().split(/\s+/);let T="en",g=0;for(const[u,$]of Object.entries(p)){const V=D.filter(O=>$.includes(O)).length;V>g&&(g=V,T=u)}return T},w=async C=>{if(!(!a.content||m)){c(!0);try{let p=y;if(y==="auto"&&(p=await v(a.content),_(p)),p===C){h(T=>({...T,[C]:{text:a.content,confidence:1,isOriginal:!0}})),c(!1);return}const D=await E(a.content,C,p);h(T=>({...T,[C]:{text:D.translatedText,confidence:D.confidence,detectedSource:D.detectedSourceLanguage,isOriginal:!1}})),s&&s(a.id,C,D.translatedText)}catch(p){console.error("Translation error:",p)}finally{c(!1)}}},d=async C=>{try{await navigator.clipboard.writeText(C),j(!0),setTimeout(()=>j(!1),2e3)}catch(p){console.error("Failed to copy:",p)}},l=(C,p)=>{if("speechSynthesis"in window){const D=new SpeechSynthesisUtterance(C);D.lang=p,speechSynthesis.speak(D)}};return r.useEffect(()=>{i!=null&&i.autoTranslate&&(i!=null&&i.preferredLanguage)&&i.preferredLanguage!=="en"&&w(i.preferredLanguage)},[a.content,i==null?void 0:i.autoTranslate,i==null?void 0:i.preferredLanguage]),t("div",{className:"relative",children:[e("button",{onClick:()=>b(!S),className:"p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",title:"Translate message",children:e($r,{className:"w-4 h-4"})}),S&&t("div",{className:"absolute top-8 right-0 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 w-80 z-10",children:[t("div",{className:"flex items-center justify-between mb-3",children:[e("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Translate Message"}),e("button",{onClick:()=>b(!1),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:"×"})]}),x&&e("div",{className:"mb-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded",children:t("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:["Detected language: ",((o=M.find(C=>C.code===x))==null?void 0:o.name)||x]})}),t("div",{className:"mb-3",children:[e("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Translate to:"}),e("div",{className:"grid grid-cols-2 gap-2 max-h-32 overflow-y-auto",children:M.filter(C=>C.code!=="auto").map(C=>t("button",{onClick:()=>w(C.code),disabled:m,className:"flex items-center space-x-2 p-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors disabled:opacity-50",children:[e("span",{children:C.flag}),e("span",{className:"text-sm",children:C.name}),m&&e(Ir,{className:"w-3 h-3 animate-spin ml-auto"})]},C.code))})]}),Object.keys(n).length>0&&t("div",{className:"space-y-3",children:[e("h5",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Translations:"}),Object.entries(n).map(([C,p])=>{const D=M.find(T=>T.code===C);return t("div",{className:"border border-gray-200 dark:border-gray-600 rounded p-3",children:[t("div",{className:"flex items-center justify-between mb-2",children:[t("div",{className:"flex items-center space-x-2",children:[e("span",{children:D==null?void 0:D.flag}),e("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:D==null?void 0:D.name}),p.isOriginal&&e("span",{className:"text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 px-2 py-1 rounded",children:"Original"})]}),t("div",{className:"flex items-center space-x-1",children:[e("button",{onClick:()=>l(p.text,C),className:"p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400",title:"Listen",children:e(Cs,{className:"w-3 h-3"})}),e("button",{onClick:()=>d(p.text),className:"p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400",title:"Copy",children:N?e(tr,{className:"w-3 h-3"}):e(cr,{className:"w-3 h-3"})})]})]}),e("p",{className:"text-sm text-gray-900 dark:text-white",children:p.text}),!p.isOriginal&&t("div",{className:"mt-2 text-xs text-gray-500 dark:text-gray-400",children:["Confidence: ",Math.round(p.confidence*100),"%"]})]},C)})]}),e("div",{className:"mt-3 pt-3 border-t border-gray-200 dark:border-gray-600",children:t("div",{className:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400",children:[e("span",{children:"Quick translate:"}),e("div",{className:"flex space-x-1",children:["es","fr","de","zh"].map(C=>{const p=M.find(D=>D.code===C);return e("button",{onClick:()=>w(C),className:"p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded",title:p==null?void 0:p.name,children:p==null?void 0:p.flag},C)})})]})})]})]})},Ts=({isOpen:a,onToggle:s,user:i,room:m})=>{const[c,n]=r.useState([]),[h,y]=r.useState(""),[k,x]=r.useState(!1),[_,S]=r.useState(!1);r.useState("helpful");const b={greetings:["Hello! I'm your AI assistant. How can I help you today?","Hi there! Welcome to our chat platform. What can I do for you?","Greetings! I'm here to assist you with any questions you might have."],help:[`I can help you with:
• Navigating the chat platform
• Finding rooms and users
• Understanding VIP features
• Technical support
• General questions`,`Here are some things I can assist with:
• Room management
• User profiles
• File sharing
• Voice/video calls
• Platform features`],vip:[`VIP members enjoy exclusive benefits:
• Access to VIP-only rooms
• Priority support
• Custom emojis
• Early access to features
• Special badges`,"Our VIP program offers premium features like exclusive channels, priority support, and custom emojis. Would you like to learn more about upgrading?"],features:[`Our platform offers:
• Real-time messaging
• Voice & video calls
• File sharing
• Screen sharing
• Live events
• Group chats
• Private messaging`,"Key features include real-time chat, multimedia sharing, voice/video calls, live events, and much more!"],support:[`For technical support, you can:
• Contact our support team
• Check the help documentation
• Report bugs through the feedback system
• Join the support room`,"I'm here to help! If you need human assistance, I can connect you with our support team."],default:["I'm not sure I understand. Could you rephrase that?","That's an interesting question! Could you provide more details?","I'd be happy to help, but I need a bit more information.","Let me think about that... Could you be more specific?"]},N=[{text:"How do I join a room?",category:"help"},{text:"What are VIP features?",category:"vip"},{text:"How do I start a video call?",category:"features"},{text:"I need technical support",category:"support"},{text:"How do I upload files?",category:"help"},{text:"What's the difference between public and private rooms?",category:"help"}];r.useEffect(()=>{if(a&&c.length===0){const w={id:Date.now(),text:i!=null&&i.is_vip?`Welcome back, ${i.display_name}! As a VIP member, you have access to exclusive features. How can I assist you today?`:`Hello ${(i==null?void 0:i.display_name)||"there"}! I'm your AI assistant. How can I help you today?`,sender:"bot",timestamp:new Date,type:"text"};n([w])}},[a,i]);const j=w=>{const d=w.toLowerCase();return d.match(/hello|hi|hey|greetings|good morning|good afternoon/)?"greetings":d.match(/help|assist|support|how/)?"help":d.match(/vip|premium|exclusive|upgrade/)?"vip":d.match(/feature|function|capability|what can/)?"features":d.match(/problem|issue|bug|error|technical|support/)?"support":"default"},M=w=>{const d=b[w]||b.default;return d[Math.floor(Math.random()*d.length)]},E=async(w=h)=>{if(!w.trim())return;const d={id:Date.now(),text:w.trim(),sender:"user",timestamp:new Date,type:"text"};n(l=>[...l,d]),y(""),x(!0),setTimeout(()=>{const l=j(w),o=M(l),C={id:Date.now()+1,text:o,sender:"bot",timestamp:new Date,type:"text",category:l};n(p=>[...p,C]),x(!1),l==="help"&&setTimeout(()=>{const p={id:Date.now()+2,text:"Would you like me to connect you with a human agent for more detailed assistance?",sender:"bot",timestamp:new Date,type:"suggestion",actions:[{text:"Yes, connect me",action:"connect_agent"},{text:"No, thanks",action:"dismiss"}]};n(D=>[...D,p])},1e3)},1e3+Math.random()*2e3)},v=w=>{switch(w){case"connect_agent":const d={id:Date.now(),text:"I'm connecting you with a human agent. Please wait a moment...",sender:"bot",timestamp:new Date,type:"system"};n(l=>[...l,d]);break}};return a?t("div",{className:`fixed bottom-4 right-4 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50 transition-all duration-200 ${_?"h-14":"h-96"}`,children:[t("div",{className:"flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 bg-blue-500 text-white rounded-t-lg",children:[t("div",{className:"flex items-center space-x-2",children:[e(Le,{className:"w-5 h-5"}),e("span",{className:"font-medium",children:"AI Assistant"}),e("div",{className:"w-2 h-2 bg-green-400 rounded-full"})]}),t("div",{className:"flex items-center space-x-1",children:[e("button",{onClick:()=>S(!_),className:"p-1 hover:bg-blue-600 rounded",children:_?e(Oe,{className:"w-4 h-4"}):e(Ve,{className:"w-4 h-4"})}),e("button",{onClick:s,className:"p-1 hover:bg-blue-600 rounded",children:e(de,{className:"w-4 h-4"})})]})]}),!_&&t(xe,{children:[t("div",{className:"flex-1 overflow-y-auto p-3 space-y-3 h-64",children:[c.map(w=>e("div",{className:`flex ${w.sender==="user"?"justify-end":"justify-start"}`,children:t("div",{className:`max-w-xs px-3 py-2 rounded-lg ${w.sender==="user"?"bg-blue-500 text-white":w.type==="system"?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200":"bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white"}`,children:[e("p",{className:"text-sm whitespace-pre-wrap",children:w.text}),w.actions&&e("div",{className:"mt-2 space-y-1",children:w.actions.map((d,l)=>e("button",{onClick:()=>v(d.action),className:"block w-full text-left text-xs bg-white/20 hover:bg-white/30 px-2 py-1 rounded",children:d.text},l))}),e("div",{className:"text-xs opacity-70 mt-1",children:w.timestamp.toLocaleTimeString()})]})},w.id)),k&&e("div",{className:"flex justify-start",children:e("div",{className:"bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg",children:t("div",{className:"flex space-x-1",children:[e("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),e("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),e("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})})})]}),e("div",{className:"px-3 py-2 border-t border-gray-200 dark:border-gray-700",children:e("div",{className:"flex flex-wrap gap-1 mb-2",children:N.slice(0,3).map((w,d)=>e("button",{onClick:()=>E(w.text),className:"text-xs bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 px-2 py-1 rounded transition-colors",children:w.text},d))})}),e("div",{className:"p-3 border-t border-gray-200 dark:border-gray-700",children:t("form",{onSubmit:w=>{w.preventDefault(),E()},className:"flex space-x-2",children:[e("input",{type:"text",value:h,onChange:w=>y(w.target.value),placeholder:"Type your message...",className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),e("button",{type:"submit",disabled:!h.trim()||k,className:"px-3 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors",children:e(mt,{className:"w-4 h-4"})})]})})]})]}):e("button",{onClick:s,className:"fixed bottom-4 right-4 w-14 h-14 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-200 hover:scale-110 z-50",children:e(Le,{className:"w-6 h-6"})})},Is=({room:a,messages:s,onSendMessage:i,onTyping:m,onFileUpload:c,onVoiceMessage:n,typingUsers:h,roomMembers:y,pinnedMessages:k,replyToMessage:x,onReply:_,editingMessage:S,onEdit:b,user:N,isConnected:j,messagesEndRef:M,messageInputRef:E})=>{var oe;const[v,w]=r.useState(""),[d,l]=r.useState(!1),[o,C]=r.useState(!1),[p,D]=r.useState(null),[T,g]=r.useState(!1),[u,$]=r.useState(0),[V,O]=r.useState(!1),[F,H]=r.useState(!1);r.useState(!1);const Q=r.useRef(null),Z=r.useRef(null);r.useEffect(()=>{var R;S&&(w(S.content),(R=E.current)==null||R.focus())},[S]);const te=R=>{R.preventDefault(),v.trim()&&(S?b(null):i(v,{reply_to:x==null?void 0:x.id}),w(""),_(null))},me=R=>{w(R.target.value),m()},ae=R=>{window.confirm("Are you sure you want to delete this message?")&&i("",R,"delete")},f=R=>{i("",R,"pin")},I=R=>{var U;if(((U=R.sender)==null?void 0:U.id)!==(N==null?void 0:N.id))return null;switch(R.status){case"sending":return e("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-pulse",title:"Sending..."});case"sent":return e("div",{className:"text-gray-400 text-xs",title:"Sent",children:"✓"});case"delivered":return e("div",{className:"text-gray-400 text-xs",title:"Delivered",children:"✓✓"});case"read":return e("div",{className:"text-blue-400 text-xs",title:"Read",children:"✓✓"});default:return e("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-pulse",title:"Sending..."})}},A=R=>{const U=Array.from(R.target.files);U.length>0&&c(U),R.target.value=""},B=async()=>{try{const R=await navigator.mediaDevices.getUserMedia({audio:!0}),U=new MediaRecorder(R),he=[];U.ondataavailable=X=>he.push(X.data),U.onstop=()=>{const X=new Blob(he,{type:"audio/webm"});n(X),R.getTracks().forEach(ue=>ue.stop())},U.start(),g(!0),$(0),Z.current=setInterval(()=>{$(X=>X+1)},1e3),setTimeout(()=>{U.state==="recording"&&(U.stop(),re())},3e5)}catch(R){console.error("Failed to start recording:",R)}},re=()=>{g(!1),$(0),Z.current&&clearInterval(Z.current)},fe=R=>new Date(R).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),ve=({message:R})=>{var L,z,W,ee,se,J,Me,Ie,gt;const U=((L=R.sender)==null?void 0:L.id)===(N==null?void 0:N.id),[he,X]=r.useState(!1),ue=ne=>{console.log("React with:",ne,"to message:",R.id),X(!1)};return t("div",{id:`message-${R.id}`,className:`flex gap-3 p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 group ${U?"flex-row-reverse":""}`,children:[!U&&e("div",{className:"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0",children:e("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:((W=(z=R.sender)==null?void 0:z.display_name)==null?void 0:W[0])||((se=(ee=R.sender)==null?void 0:ee.username)==null?void 0:se[0])||"U"})}),t("div",{className:`flex-1 min-w-0 ${U?"text-right":""}`,children:[t("div",{className:`flex items-center gap-2 mb-1 ${U?"justify-end":""}`,children:[e("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:((J=R.sender)==null?void 0:J.display_name)||((Me=R.sender)==null?void 0:Me.username)}),((Ie=R.sender)==null?void 0:Ie.is_vip)&&e("span",{className:"text-xs bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 px-2 py-1 rounded-full",children:"VIP"}),e("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:fe(R.created_at)}),R.is_edited&&e("span",{className:"text-xs text-gray-400",children:"(edited)"}),I(R)]}),R.reply_to&&t("div",{className:"bg-gray-100 dark:bg-gray-700 rounded-lg p-2 mb-2 border-l-4 border-blue-500",children:[t("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:["Replying to ",(gt=R.reply_to.sender)==null?void 0:gt.username]}),e("p",{className:"text-sm text-gray-800 dark:text-gray-200 truncate",children:R.reply_to.content})]}),t("div",{className:`${U?"bg-blue-500 text-white":"bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white"} rounded-lg p-3 max-w-md ${U?"ml-auto":""}`,children:[R.message_type==="text"&&e("p",{className:"text-sm whitespace-pre-wrap break-words",children:R.content}),R.message_type==="file"&&t("div",{className:"flex items-center gap-2",children:[e(Nt,{className:"w-4 h-4"}),e("a",{href:R.file_url,download:!0,className:"text-sm underline hover:no-underline",children:R.file_name})]}),R.message_type==="image"&&t("div",{children:[e("img",{src:R.file_url,alt:"Shared image",className:"max-w-full h-auto rounded-lg"}),R.content&&e("p",{className:"text-sm mt-2",children:R.content})]}),R.message_type==="voice"&&t("div",{className:"flex items-center gap-2",children:[e(Je,{className:"w-4 h-4"}),e("audio",{controls:!0,className:"max-w-full",children:e("source",{src:R.file_url,type:"audio/webm"})})]})]}),R.reactions&&R.reactions.length>0&&e("div",{className:"flex gap-1 mt-2",children:R.reactions.map((ne,Xe)=>t("button",{onClick:()=>ue(ne.emoji),className:"bg-gray-100 dark:bg-gray-700 rounded-full px-2 py-1 text-xs hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:[ne.emoji," ",ne.count]},Xe))})]}),t("div",{className:`opacity-0 group-hover:opacity-100 transition-opacity flex items-start gap-1 ${U?"order-first":""}`,children:[e("button",{onClick:()=>X(!he),className:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",title:"React",children:e(Ct,{className:"w-4 h-4"})}),e("button",{onClick:()=>_(R),className:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",title:"Reply",children:e(_t,{className:"w-4 h-4"})}),U&&t(xe,{children:[e("button",{onClick:()=>b(R),className:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",title:"Edit",children:e(Et,{className:"w-4 h-4"})}),e("button",{onClick:()=>ae(R.id),className:"p-1 text-gray-400 hover:text-red-500 transition-colors",title:"Delete",children:e(ht,{className:"w-4 h-4"})}),e("button",{onClick:()=>f(R.id),className:`p-1 transition-colors ${R.is_pinned?"text-yellow-500":"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"}`,title:R.is_pinned?"Unpin":"Pin",children:e(kt,{className:"w-4 h-4"})})]}),e($s,{message:R,onTranslate:(ne,Xe,zt)=>{console.log("Translated message:",ne,Xe,zt)},user:N}),e("button",{onClick:()=>D(p===R.id?null:R.id),className:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",children:e(it,{className:"w-4 h-4"})})]}),he&&e("div",{className:"absolute bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-2 flex gap-1 z-10",children:["👍","❤️","😂","😮","😢","😡"].map(ne=>e("button",{onClick:()=>ue(ne),className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors",children:ne},ne))})]})};return t("div",{className:"flex flex-col h-full bg-white dark:bg-gray-800",children:[t("div",{className:"p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between",children:[t("div",{className:"flex items-center gap-3",children:[t("div",{className:"flex items-center gap-2",children:[e("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:a.name}),a.is_vip_only&&e("span",{className:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 px-2 py-1 rounded-full text-xs font-medium",children:"VIP"})]}),t("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:[y.length," members"]})]}),t("div",{className:"flex items-center gap-2",children:[e("button",{onClick:()=>{},className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",title:"Voice Call",children:e(Pe,{className:"w-5 h-5"})}),e("button",{onClick:()=>{},className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",title:"Video Call",children:e(ut,{className:"w-5 h-5"})}),e("button",{onClick:()=>C(!o),className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",title:"Members",children:e(K,{className:"w-5 h-5"})}),e("button",{onClick:()=>{},className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",title:"Room Settings",children:e(Te,{className:"w-5 h-5"})})]})]}),k.length>0&&t("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 border-b border-yellow-200 dark:border-yellow-800 p-3",children:[t("div",{className:"flex items-center gap-2 text-yellow-800 dark:text-yellow-200",children:[e(kt,{className:"w-4 h-4"}),e("span",{className:"text-sm font-medium",children:"Pinned Messages"})]}),e("div",{className:"mt-2 space-y-1",children:k.slice(0,2).map(R=>{var U;return t("p",{className:"text-sm text-yellow-700 dark:text-yellow-300 truncate",children:[(U=R.sender)==null?void 0:U.username,": ",R.content]},R.id)})})]}),e("div",{className:"flex-1 overflow-y-auto",children:s.length>0?t("div",{children:[s.map(R=>e(ve,{message:R},R.id)),h.length>0&&t("div",{className:"p-4 text-sm text-gray-500 dark:text-gray-400",children:[h.map(R=>R.username).join(", ")," ",h.length===1?"is":"are"," typing..."]}),e("div",{ref:M})]}):e("div",{className:"flex-1 flex items-center justify-center",children:t("div",{className:"text-center",children:[e("div",{className:"w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4",children:e(q,{className:"w-8 h-8 text-gray-400"})}),e("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No messages yet"}),e("p",{className:"text-gray-500 dark:text-gray-400",children:"Be the first to send a message in this room!"})]})})}),x&&t("div",{className:"p-3 bg-blue-50 dark:bg-blue-900/20 border-t border-blue-200 dark:border-blue-800",children:[t("div",{className:"flex items-center justify-between",children:[t("div",{className:"flex items-center gap-2",children:[e(_t,{className:"w-4 h-4 text-blue-600 dark:text-blue-400"}),t("span",{className:"text-sm text-blue-600 dark:text-blue-400",children:["Replying to ",(oe=x.sender)==null?void 0:oe.username]})]}),e("button",{onClick:()=>_(null),className:"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300",children:"×"})]}),e("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1 truncate",children:x.content})]}),t("div",{className:"p-4 border-t border-gray-200 dark:border-gray-700",children:[t("form",{onSubmit:te,className:"flex items-end gap-2",children:[t("div",{className:"flex-1",children:[t("div",{className:"flex items-center gap-2 mb-2",children:[e("button",{type:"button",onClick:()=>{var R;return(R=Q.current)==null?void 0:R.click()},className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",title:"Attach File",children:e(Nt,{className:"w-5 h-5"})}),e("button",{type:"button",onClick:T?re:B,className:`p-2 transition-colors ${T?"text-red-500 hover:text-red-600":"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"}`,title:T?"Stop Recording":"Voice Message",children:e(Je,{className:"w-5 h-5"})}),e("button",{type:"button",onClick:()=>H(!0),className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",title:"Canned Responses",children:e(ye,{className:"w-5 h-5"})}),e("button",{type:"button",onClick:()=>O(!V),className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",title:"AI Assistant",children:e(Le,{className:"w-5 h-5"})}),T&&t("span",{className:"text-sm text-red-500",children:["Recording... ",Math.floor(u/60),":",(u%60).toString().padStart(2,"0")]})]}),t("div",{className:"relative",children:[e("textarea",{ref:E,value:v,onChange:me,placeholder:j?"Type a message...":"Connecting...",disabled:!j,className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",rows:1,style:{minHeight:"44px",maxHeight:"120px"},onKeyDown:R=>{R.key==="Enter"&&!R.shiftKey&&(R.preventDefault(),te(R))}}),e("button",{type:"button",onClick:()=>l(!d),className:"absolute right-3 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",children:e(Ct,{className:"w-5 h-5"})})]})]}),e("button",{type:"submit",disabled:!v.trim()||!j,className:"p-3 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors",children:e(mt,{className:"w-5 h-5"})})]}),e("input",{ref:Q,type:"file",multiple:!0,onChange:A,className:"hidden",accept:"image/*,video/*,audio/*,.pdf,.doc,.docx,.txt"})]}),e(Ts,{isOpen:V,onToggle:()=>O(!V),user:N,room:a}),e(Ms,{isOpen:F,onClose:()=>H(!1),onSelectResponse:R=>{var U;w(R),(U=E.current)==null||U.focus()},user:N})]})},As=({rooms:a,onRoomSelect:s,onCreateRoom:i,user:m})=>{const[c,n]=r.useState(""),[h,y]=r.useState("all"),[k,x]=r.useState("activity"),[_,S]=r.useState(!1),[b,N]=r.useState({name:"",description:"",room_type:"public",is_vip_only:!1,max_members:100}),j=a.filter(l=>{var p;const o=l.name.toLowerCase().includes(c.toLowerCase())||((p=l.description)==null?void 0:p.toLowerCase().includes(c.toLowerCase())),C=h==="all"||h==="public"&&l.is_public||h==="private"&&!l.is_public||h==="vip"&&l.is_vip_only||h==="live"&&l.room_type==="live";return o&&C}).sort((l,o)=>{switch(k){case"activity":return new Date(o.updated_at)-new Date(l.updated_at);case"members":return o.member_count-l.member_count;case"name":return l.name.localeCompare(o.name);case"created":return new Date(o.created_at)-new Date(l.created_at);default:return 0}}),M=async l=>{l.preventDefault();try{const o=await fetch("/api/chat/rooms/",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(b)});if(o.ok){const C=await o.json();S(!1),N({name:"",description:"",room_type:"public",is_vip_only:!1,max_members:100}),i(),s(C)}else throw new Error("Failed to create room")}catch(o){console.error("Failed to create room:",o)}},E=l=>l.room_type==="direct"?e(q,{className:"w-5 h-5 text-blue-500"}):l.room_type==="live"?e(ie,{className:"w-5 h-5 text-red-500"}):l.is_vip_only?e(G,{className:"w-5 h-5 text-purple-500"}):l.is_public?e(We,{className:"w-5 h-5 text-green-500"}):e(dt,{className:"w-5 h-5 text-gray-500"}),v=l=>l.room_type==="live"?"Live":l.is_vip_only?"VIP Only":l.is_public?"Public":"Private",w=({room:l})=>{var o;return t("div",{onClick:()=>s(l),className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md hover:border-blue-300 dark:hover:border-blue-600 transition-all cursor-pointer",children:[t("div",{className:"flex items-start justify-between mb-4",children:[t("div",{className:"flex items-center gap-3",children:[E(l),t("div",{children:[e("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:l.name}),e("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${l.room_type==="live"?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":l.is_vip_only?"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200":l.is_public?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"}`,children:v(l)})]})]}),l.is_featured&&e(Re,{className:"w-5 h-5 text-yellow-500"})]}),e("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2",children:l.description||"No description available"}),t("div",{className:"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400",children:[t("div",{className:"flex items-center gap-4",children:[t("div",{className:"flex items-center gap-1",children:[e(K,{className:"w-4 h-4"}),t("span",{children:[l.member_count," members"]})]}),l.message_count&&t("div",{className:"flex items-center gap-1",children:[e(q,{className:"w-4 h-4"}),t("span",{children:[l.message_count," messages"]})]}),l.room_type==="live"&&l.current_viewers&&t("div",{className:"flex items-center gap-1",children:[e(ct,{className:"w-4 h-4"}),t("span",{children:[l.current_viewers," watching"]})]})]}),t("div",{className:"flex items-center gap-1",children:[e($e,{className:"w-4 h-4"}),e("span",{children:new Date(l.updated_at).toLocaleDateString()})]})]}),l.last_message&&e("div",{className:"mt-3 pt-3 border-t border-gray-200 dark:border-gray-700",children:t("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:["Last message: ",(o=l.last_message.sender)==null?void 0:o.username," - ",l.last_message.content.substring(0,50),"..."]})})]})},d=()=>e("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:t("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md",children:[e("h2",{className:"text-xl font-bold text-gray-900 dark:text-white mb-4",children:"Create New Room"}),t("form",{onSubmit:M,className:"space-y-4",children:[t("div",{children:[e("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Room Name"}),e("input",{type:"text",value:b.name,onChange:l=>N({...b,name:l.target.value}),className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter room name",required:!0})]}),t("div",{children:[e("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Description"}),e("textarea",{value:b.description,onChange:l=>N({...b,description:l.target.value}),className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Describe your room",rows:3})]}),t("div",{children:[e("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Room Type"}),t("select",{value:b.room_type,onChange:l=>N({...b,room_type:l.target.value}),className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e("option",{value:"public",children:"Public Room"}),e("option",{value:"private",children:"Private Room"}),e("option",{value:"live",children:"Live Room"})]})]}),t("div",{children:[e("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Max Members"}),e("input",{type:"number",value:b.max_members,onChange:l=>N({...b,max_members:parseInt(l.target.value)}),className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"2",max:"1000"})]}),(m==null?void 0:m.is_vip)&&t("div",{className:"flex items-center",children:[e("input",{type:"checkbox",id:"vip-only",checked:b.is_vip_only,onChange:l=>N({...b,is_vip_only:l.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),e("label",{htmlFor:"vip-only",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:"VIP Only Room"})]}),t("div",{className:"flex gap-3 pt-4",children:[e("button",{type:"button",onClick:()=>S(!1),className:"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:"Cancel"}),e("button",{type:"submit",className:"flex-1 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors",children:"Create Room"})]})]})]})});return t("div",{className:"h-full bg-gray-50 dark:bg-gray-900",children:[t("div",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-6",children:[t("div",{className:"flex items-center justify-between mb-6",children:[e("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Browse Rooms"}),t("button",{onClick:()=>S(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2",children:[e(Ne,{className:"w-4 h-4"}),"Create Room"]})]}),t("div",{className:"flex flex-col sm:flex-row gap-4",children:[t("div",{className:"flex-1 relative",children:[e(ce,{className:"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),e("input",{type:"text",placeholder:"Search rooms...",value:c,onChange:l=>n(l.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),t("select",{value:h,onChange:l=>y(l.target.value),className:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e("option",{value:"all",children:"All Rooms"}),e("option",{value:"public",children:"Public"}),e("option",{value:"private",children:"Private"}),e("option",{value:"vip",children:"VIP Only"}),e("option",{value:"live",children:"Live"})]}),t("select",{value:k,onChange:l=>x(l.target.value),className:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e("option",{value:"activity",children:"Recent Activity"}),e("option",{value:"members",children:"Most Members"}),e("option",{value:"name",children:"Name"}),e("option",{value:"created",children:"Newest"})]})]})]}),e("div",{className:"p-6",children:j.length>0?e("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:j.map(l=>e(w,{room:l},l.id))}):t("div",{className:"text-center py-12",children:[e("div",{className:"w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4",children:e(q,{className:"w-8 h-8 text-gray-400"})}),e("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No rooms found"}),e("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:c||h!=="all"?"Try adjusting your search or filters":"Be the first to create a room!"}),e("button",{onClick:()=>S(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors",children:"Create First Room"})]})}),_&&e(d,{})]})},Ds=({isOpen:a,onClose:s,messages:i,rooms:m,users:c,onMessageSelect:n})=>{const[h,y]=r.useState(""),[k,x]=r.useState([]),[_,S]=r.useState(!1),[b,N]=r.useState({dateRange:"all",messageType:"all",sender:"all",room:"all",hasAttachments:!1,isEdited:!1,isPinned:!1}),[j,M]=r.useState(!1),[E,v]=r.useState({start:"",end:""}),[w,d]=r.useState(null),l=r.useRef(null),o=async(u,$)=>{S(!0),await new Promise(O=>setTimeout(O,300));let V=i.filter(O=>{var I,A,B,re,fe,ve;const F=!u||O.content.toLowerCase().includes(u.toLowerCase())||((A=(I=O.sender)==null?void 0:I.username)==null?void 0:A.toLowerCase().includes(u.toLowerCase()))||((re=(B=O.sender)==null?void 0:B.display_name)==null?void 0:re.toLowerCase().includes(u.toLowerCase()));let H=!0;if($.dateRange!=="all"){const oe=new Date(O.created_at),R=new Date;switch($.dateRange){case"today":H=oe.toDateString()===R.toDateString();break;case"week":const U=new Date(R.getTime()-7*24*60*60*1e3);H=oe>=U;break;case"month":const he=new Date(R.getTime()-30*24*60*60*1e3);H=oe>=he;break;case"custom":if(E.start&&E.end){const X=new Date(E.start),ue=new Date(E.end);H=oe>=X&&oe<=ue}break}}const Q=$.messageType==="all"||O.message_type===$.messageType;let Z=!0;$.sender!=="all"&&($.sender==="me"?Z=((fe=O.sender)==null?void 0:fe.id)==="current_user_id":Z=((ve=O.sender)==null?void 0:ve.id)===$.sender);const te=$.room==="all"||O.room_id===$.room,me=!$.hasAttachments||O.attachments&&O.attachments.length>0,ae=!$.isEdited||O.is_edited,f=!$.isPinned||O.is_pinned;return F&&H&&Q&&Z&&te&&me&&ae&&f});V=V.sort((O,F)=>{const H=O.content.toLowerCase().includes(u.toLowerCase()),Q=F.content.toLowerCase().includes(u.toLowerCase());return H&&!Q?-1:!H&&Q?1:new Date(F.created_at)-new Date(O.created_at)}),x(V),S(!1)};r.useEffect(()=>{if(h.trim()||Object.values(b).some(u=>u!=="all"&&u!==!1)){const u=setTimeout(()=>{o(h,b)},300);return()=>clearTimeout(u)}else x([])},[h,b,E]),r.useEffect(()=>{a&&l.current&&l.current.focus()},[a]);const C=(u,$)=>{N(V=>({...V,[u]:$}))},p=()=>{N({dateRange:"all",messageType:"all",sender:"all",room:"all",hasAttachments:!1,isEdited:!1,isPinned:!1}),v({start:"",end:""})},D=(u,$)=>{if(!$)return u;const V=new RegExp(`(${$})`,"gi");return u.split(V).map((F,H)=>V.test(F)?e("mark",{className:"bg-yellow-200 dark:bg-yellow-800 px-1 rounded",children:F},H):F)},T=u=>new Date(u).toLocaleDateString([],{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),g=u=>{switch(u){case"file":return e(yr,{className:"w-4 h-4"});case"image":return e(Sr,{className:"w-4 h-4"});case"voice":return e(Je,{className:"w-4 h-4"});default:return null}};return a?e("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:t("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl h-5/6 flex flex-col",children:[t("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700",children:[e("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Search Messages"}),e("button",{onClick:s,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:e(de,{className:"w-5 h-5"})})]}),e("div",{className:"p-4 border-b border-gray-200 dark:border-gray-700",children:t("div",{className:"relative",children:[e(ce,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),e("input",{ref:l,type:"text",placeholder:"Search messages, users, or content...",value:h,onChange:u=>y(u.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),t("div",{className:"p-4 border-b border-gray-200 dark:border-gray-700",children:[t("div",{className:"flex items-center justify-between mb-3",children:[t("button",{onClick:()=>M(!j),className:"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200",children:[e(br,{className:"w-4 h-4"}),e("span",{children:"Advanced Filters"}),j?e(nr,{className:"w-4 h-4"}):e(rr,{className:"w-4 h-4"})]}),e("button",{onClick:p,className:"text-sm text-blue-600 dark:text-blue-400 hover:underline",children:"Clear Filters"})]}),j&&t("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[t("div",{children:[e("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Date Range"}),t("select",{value:b.dateRange,onChange:u=>C("dateRange",u.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm",children:[e("option",{value:"all",children:"All Time"}),e("option",{value:"today",children:"Today"}),e("option",{value:"week",children:"Past Week"}),e("option",{value:"month",children:"Past Month"}),e("option",{value:"custom",children:"Custom Range"})]}),b.dateRange==="custom"&&t("div",{className:"mt-2 space-y-2",children:[e("input",{type:"date",value:E.start,onChange:u=>v($=>({...$,start:u.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"}),e("input",{type:"date",value:E.end,onChange:u=>v($=>({...$,end:u.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"})]})]}),t("div",{children:[e("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Message Type"}),t("select",{value:b.messageType,onChange:u=>C("messageType",u.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm",children:[e("option",{value:"all",children:"All Types"}),e("option",{value:"text",children:"Text Messages"}),e("option",{value:"file",children:"Files"}),e("option",{value:"image",children:"Images"}),e("option",{value:"voice",children:"Voice Messages"})]})]}),t("div",{children:[e("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Sender"}),t("select",{value:b.sender,onChange:u=>C("sender",u.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm",children:[e("option",{value:"all",children:"All Users"}),e("option",{value:"me",children:"My Messages"}),c.map(u=>e("option",{value:u.id,children:u.display_name||u.username},u.id))]})]}),e("div",{className:"md:col-span-3",children:t("div",{className:"flex flex-wrap gap-4",children:[t("label",{className:"flex items-center",children:[e("input",{type:"checkbox",checked:b.hasAttachments,onChange:u=>C("hasAttachments",u.target.checked),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),e("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:"Has Attachments"})]}),t("label",{className:"flex items-center",children:[e("input",{type:"checkbox",checked:b.isEdited,onChange:u=>C("isEdited",u.target.checked),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),e("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:"Edited Messages"})]}),t("label",{className:"flex items-center",children:[e("input",{type:"checkbox",checked:b.isPinned,onChange:u=>C("isPinned",u.target.checked),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),e("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:"Pinned Messages"})]})]})})]})]}),e("div",{className:"flex-1 overflow-y-auto p-4",children:_?e("div",{className:"flex items-center justify-center h-32",children:e("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"})}):k.length>0?t("div",{className:"space-y-3",children:[t("div",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4",children:["Found ",k.length," result",k.length!==1?"s":""]}),k.map(u=>{var $,V,O,F,H;return t("div",{onClick:()=>{d(u.id),n(u)},className:`border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer transition-colors ${w===u.id?"ring-2 ring-blue-500":""}`,children:[t("div",{className:"flex items-start justify-between mb-2",children:[t("div",{className:"flex items-center space-x-2",children:[e("span",{className:"font-medium text-gray-900 dark:text-white",children:(($=u.sender)==null?void 0:$.display_name)||((V=u.sender)==null?void 0:V.username)}),((O=u.sender)==null?void 0:O.is_vip)&&e("span",{className:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 px-2 py-1 rounded-full text-xs",children:"VIP"}),g(u.message_type)]}),e("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:T(u.created_at)})]}),e("div",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2",children:D(u.content,h)}),t("div",{className:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400",children:[t("span",{children:["Room: ",((F=u.room)==null?void 0:F.name)||"Unknown"]}),t("div",{className:"flex items-center space-x-2",children:[u.is_edited&&e("span",{children:"Edited"}),u.is_pinned&&e("span",{children:"Pinned"}),((H=u.attachments)==null?void 0:H.length)>0&&t("span",{children:[u.attachments.length," attachment",u.attachments.length!==1?"s":""]})]})]})]},u.id)})]}):h||Object.values(b).some(u=>u!=="all"&&u!==!1)?t("div",{className:"text-center py-8",children:[e(ce,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),e("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No results found"}),e("p",{className:"text-gray-500 dark:text-gray-400",children:"Try adjusting your search terms or filters."})]}):t("div",{className:"text-center py-8",children:[e(ce,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),e("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Search Messages"}),e("p",{className:"text-gray-500 dark:text-gray-400",children:"Enter a search term to find messages, users, or content."})]})})]})}):null},Ps=({isOpen:a,onClose:s,onCreateRoom:i,user:m})=>{const[c,n]=r.useState({name:"",description:"",room_type:"group",is_public:!0,is_vip_only:!1,max_members:1e3,password:"",tags:[],welcome_message:"",rules:""}),[h,y]=r.useState(""),[k,x]=r.useState(!1),[_,S]=r.useState({}),b=[{id:"group",name:"Group Room",description:"Public room where anyone can join and see messages",icon:Pt,color:"text-blue-500"},{id:"private",name:"Private Room",description:"Invite-only room with restricted access",icon:dt,color:"text-gray-500"},{id:"vip",name:"VIP Room",description:"Exclusive room for VIP members only",icon:G,color:"text-purple-500"},{id:"live",name:"Live Event Room",description:"Room for live streaming and events",icon:ie,color:"text-red-500"},{id:"channel",name:"Channel",description:"Broadcast channel for announcements",icon:We,color:"text-green-500"},{id:"support",name:"Support Room",description:"Customer support and help desk",icon:Se,color:"text-orange-500"}],N=(d,l)=>{n(o=>({...o,[d]:l})),_[d]&&S(o=>({...o,[d]:""}))},j=d=>{n(l=>({...l,room_type:d,is_public:d==="public",is_vip_only:d==="vip"}))},M=()=>{h.trim()&&!c.tags.includes(h.trim())&&(n(d=>({...d,tags:[...d.tags,h.trim()]})),y(""))},E=d=>{n(l=>({...l,tags:l.tags.filter(o=>o!==d)}))},v=()=>{const d={};return c.name.trim()?c.name.length<3?d.name="Room name must be at least 3 characters":c.name.length>50&&(d.name="Room name must be less than 50 characters"):d.name="Room name is required",c.description.length>500&&(d.description="Description must be less than 500 characters"),c.room_type==="private"&&!c.password&&(d.password="Password is required for private rooms"),c.max_members<2?d.max_members="Room must allow at least 2 members":c.max_members>1e4&&(d.max_members="Maximum members cannot exceed 10,000"),S(d),Object.keys(d).length===0},w=async d=>{if(d.preventDefault(),!!v()){x(!0);try{await i(c),s(),n({name:"",description:"",room_type:"group",is_public:!0,is_vip_only:!1,max_members:1e3,password:"",tags:[],welcome_message:"",rules:""})}catch(l){console.error("Failed to create room:",l)}finally{x(!1)}}};return a?e("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:t("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[t("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700",children:[e("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Create New Room"}),e("button",{onClick:s,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",children:e(de,{className:"w-5 h-5"})})]}),t("form",{onSubmit:w,className:"p-6 space-y-6",children:[t("div",{children:[e("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:"Room Type"}),e("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:b.map(d=>{const l=d.icon;return e("button",{type:"button",onClick:()=>j(d.id),className:`p-4 border-2 rounded-lg text-left transition-all ${c.room_type===d.id?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"}`,children:t("div",{className:"flex items-center space-x-3",children:[e(l,{className:`w-5 h-5 ${d.color}`}),t("div",{children:[e("div",{className:"font-medium text-gray-900 dark:text-white",children:d.name}),e("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:d.description})]})]})},d.id)})})]}),t("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[t("div",{children:[e("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Room Name *"}),e("input",{type:"text",value:c.name,onChange:d=>N("name",d.target.value),placeholder:"Enter room name...",className:`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${_.name?"border-red-500":"border-gray-300 dark:border-gray-600"}`}),_.name&&e("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:_.name})]}),t("div",{children:[e("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Max Members"}),e("input",{type:"number",value:c.max_members,onChange:d=>N("max_members",parseInt(d.target.value)||0),min:"2",max:"10000",className:`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${_.max_members?"border-red-500":"border-gray-300 dark:border-gray-600"}`}),_.max_members&&e("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:_.max_members})]})]}),t("div",{children:[e("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Description"}),e("textarea",{value:c.description,onChange:d=>N("description",d.target.value),placeholder:"Describe what this room is about...",rows:3,className:`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${_.description?"border-red-500":"border-gray-300 dark:border-gray-600"}`}),_.description&&e("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:_.description})]}),c.room_type==="private"&&t("div",{children:[e("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Room Password *"}),e("input",{type:"password",value:c.password,onChange:d=>N("password",d.target.value),placeholder:"Enter room password...",className:`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${_.password?"border-red-500":"border-gray-300 dark:border-gray-600"}`}),_.password&&e("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:_.password})]}),t("div",{children:[e("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Tags"}),e("div",{className:"flex flex-wrap gap-2 mb-2",children:c.tags.map((d,l)=>t("span",{className:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 px-2 py-1 rounded-full text-sm flex items-center space-x-1",children:[e("span",{children:d}),e("button",{type:"button",onClick:()=>E(d),className:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200",children:"×"})]},l))}),t("div",{className:"flex space-x-2",children:[e("input",{type:"text",value:h,onChange:d=>y(d.target.value),placeholder:"Add a tag...",className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent",onKeyPress:d=>d.key==="Enter"&&(d.preventDefault(),M())}),e("button",{type:"button",onClick:M,className:"px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors",children:"Add"})]})]}),t("div",{children:[e("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Welcome Message"}),e("textarea",{value:c.welcome_message,onChange:d=>N("welcome_message",d.target.value),placeholder:"Message shown to new members...",rows:2,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),t("div",{children:[e("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Room Rules"}),e("textarea",{value:c.rules,onChange:d=>N("rules",d.target.value),placeholder:"Rules and guidelines for this room...",rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),t("div",{className:"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700",children:[e("button",{type:"button",onClick:s,className:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors",children:"Cancel"}),e("button",{type:"submit",disabled:k,className:"px-6 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center space-x-2",children:k?t(xe,{children:[e("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),e("span",{children:"Creating..."})]}):t(xe,{children:[e(We,{className:"w-4 h-4"}),e("span",{children:"Create Room"})]})})]})]})]})}):null},Ls=({user:a,onCreateRoom:s,onBrowseRooms:i,onTryDemo:m,showDemoOption:c=!1})=>{const[n,h]=r.useState(0),[y,k]=r.useState(!0),x=[{icon:q,title:"Start Conversations",description:"Create rooms for different topics and invite others to join the discussion."},{icon:G,title:"VIP Features",description:"Upgrade to VIP for exclusive rooms, custom emojis, and priority support."},{icon:Le,title:"AI Assistant",description:"Use our intelligent chatbot for quick answers and automated responses."},{icon:ie,title:"Live Events",description:"Host live streaming events and real-time discussions with your community."},{icon:Se,title:"Secure & Private",description:"Your conversations are protected with end-to-end encryption and privacy controls."}],_=[{icon:K,label:"Team Collaboration",color:"text-blue-500"},{icon:Pt,label:"Global Community",color:"text-green-500"},{icon:Ke,label:"Real-time Messaging",color:"text-yellow-500"},{icon:Ze,label:"Reactions & Emojis",color:"text-pink-500"},{icon:Re,label:"Premium Features",color:"text-purple-500"},{icon:Lt,label:"24/7 Support",color:"text-indigo-500"}];r.useEffect(()=>{const N=setInterval(()=>{h(j=>(j+1)%x.length)},4e3);return()=>clearInterval(N)},[x.length]),r.useEffect(()=>{const N=setTimeout(()=>k(!1),1e3);return()=>clearTimeout(N)},[]);const S=x[n],b=S.icon;return e("div",{className:"flex-1 flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 p-8",children:t("div",{className:"max-w-4xl w-full text-center space-y-8",children:[e("div",{className:`transition-all duration-1000 ${y?"scale-0 opacity-0":"scale-100 opacity-100"}`,children:e("div",{className:"w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg",children:e(q,{className:"w-12 h-12 text-white"})})}),t("div",{className:`transition-all duration-1000 delay-300 ${y?"translate-y-8 opacity-0":"translate-y-0 opacity-100"}`,children:[t("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4",children:["Welcome to"," ",e("span",{className:"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"ChatFlow Pro"})]}),t("p",{className:"text-xl text-gray-600 dark:text-gray-400 mb-2",children:["Hey ",(a==null?void 0:a.display_name)||(a==null?void 0:a.username),"! 👋"]}),e("p",{className:"text-lg text-gray-500 dark:text-gray-500",children:"Ready to start your chat experience? Let's get you connected!"})]}),e("div",{className:`transition-all duration-1000 delay-500 ${y?"translate-y-8 opacity-0":"translate-y-0 opacity-100"}`,children:t("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 max-w-md mx-auto border border-gray-200 dark:border-gray-700",children:[t("div",{className:"flex items-center space-x-4",children:[e("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:e(b,{className:"w-6 h-6 text-white"})}),t("div",{className:"flex-1 text-left",children:[e("h3",{className:"font-semibold text-gray-900 dark:text-white",children:S.title}),e("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:S.description})]})]}),e("div",{className:"flex justify-center space-x-2 mt-4",children:x.map((N,j)=>e("div",{className:`w-2 h-2 rounded-full transition-all duration-300 ${j===n?"bg-blue-500 w-6":"bg-gray-300 dark:bg-gray-600"}`},j))})]})}),e("div",{className:`transition-all duration-1000 delay-700 ${y?"translate-y-8 opacity-0":"translate-y-0 opacity-100"}`,children:t("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[t("button",{onClick:s,className:"group bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center space-x-3",children:[e(Ne,{className:"w-6 h-6 group-hover:rotate-90 transition-transform duration-300"}),e("span",{children:"Create Your First Room"}),e(Be,{className:"w-5 h-5 group-hover:animate-pulse"})]}),t("button",{onClick:i,className:"group bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 px-8 py-4 rounded-xl font-semibold text-lg border-2 border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500 transition-all duration-300 transform hover:scale-105 flex items-center space-x-3",children:[e(ce,{className:"w-6 h-6 group-hover:scale-110 transition-transform duration-300"}),e("span",{children:"Browse Existing Rooms"})]}),c&&m&&t("button",{onClick:m,className:"group bg-gradient-to-r from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center space-x-3",children:[e(Ke,{className:"w-6 h-6 group-hover:animate-bounce transition-transform duration-300"}),e("span",{children:"Try Demo Mode"}),e(Re,{className:"w-5 h-5 group-hover:animate-spin"})]})]})}),e("div",{className:`transition-all duration-1000 delay-900 ${y?"translate-y-8 opacity-0":"translate-y-0 opacity-100"}`,children:e("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 max-w-2xl mx-auto",children:_.map((N,j)=>{const M=N.icon;return t("div",{className:"group bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md hover:border-blue-300 dark:hover:border-blue-500 transition-all duration-300 cursor-pointer",children:[e(M,{className:`w-8 h-8 ${N.color} mx-auto mb-2 group-hover:scale-110 transition-transform duration-300`}),e("p",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white transition-colors duration-300",children:N.label})]},j)})})}),e("div",{className:`transition-all duration-1000 delay-1100 ${y?"translate-y-8 opacity-0":"translate-y-0 opacity-100"}`,children:t("div",{className:"flex justify-center space-x-8 text-center",children:[t("div",{className:"group cursor-pointer",children:[e("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300",children:"10K+"}),e("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Active Users"})]}),t("div",{className:"group cursor-pointer",children:[e("div",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400 group-hover:scale-110 transition-transform duration-300",children:"500+"}),e("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Chat Rooms"})]}),t("div",{className:"group cursor-pointer",children:[e("div",{className:"text-2xl font-bold text-green-600 dark:text-green-400 group-hover:scale-110 transition-transform duration-300",children:"24/7"}),e("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Support"})]})]})}),e("div",{className:`transition-all duration-1000 delay-1300 ${y?"translate-y-8 opacity-0":"translate-y-0 opacity-100"}`,children:t("p",{className:"text-sm text-gray-400 dark:text-gray-500",children:["Need help getting started? Check out our"," ",e("button",{className:"text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 underline",children:"quick start guide"})," ","or"," ",e("button",{className:"text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 underline",children:"contact support"})]})})]})})},Es=({message:a="Loading ChatFlow Pro..."})=>{const[s,i]=r.useState(0),[m,c]=r.useState(0),n=[{icon:q,text:"Initializing chat engine...",color:"text-blue-500"},{icon:K,text:"Connecting to community...",color:"text-green-500"},{icon:G,text:"Loading VIP features...",color:"text-purple-500"},{icon:ie,text:"Setting up live events...",color:"text-red-500"},{icon:Ke,text:"Optimizing performance...",color:"text-yellow-500"},{icon:Ze,text:"Almost ready!",color:"text-pink-500"}];r.useEffect(()=>{const k=setInterval(()=>{i(_=>_<n.length-1?_+1:0)},800),x=setInterval(()=>{c(_=>_>=100?0:_+2)},50);return()=>{clearInterval(k),clearInterval(x)}},[n.length]);const h=n[s],y=h.icon;return e("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-8",children:t("div",{className:"text-center space-y-8 max-w-md w-full",children:[t("div",{className:"relative",children:[e("div",{className:"w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto shadow-lg animate-pulse",children:e(q,{className:"w-10 h-10 text-white"})}),e("div",{className:"absolute inset-0 animate-spin",style:{animationDuration:"8s"},children:n.map((k,x)=>{const _=k.icon,S=x*360/n.length,b=50,N=Math.cos(S*Math.PI/180)*b,j=Math.sin(S*Math.PI/180)*b;return e("div",{className:`absolute w-8 h-8 rounded-full bg-white dark:bg-gray-800 shadow-md flex items-center justify-center transition-all duration-500 ${x===s?"scale-125 shadow-lg":"scale-100"}`,style:{transform:`translate(${N}px, ${j}px)`,left:"50%",top:"50%",marginLeft:"-16px",marginTop:"-16px"},children:e(_,{className:`w-4 h-4 ${x===s?k.color:"text-gray-400"}`})},x)})})]}),t("div",{children:[e("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:e("span",{className:"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"ChatFlow Pro"})}),e("p",{className:"text-gray-600 dark:text-gray-400",children:"Professional Chat Platform"})]}),t("div",{className:"space-y-4",children:[t("div",{className:"flex items-center justify-center space-x-3",children:[e(y,{className:`w-5 h-5 ${h.color} animate-bounce`}),e("span",{className:"text-gray-700 dark:text-gray-300 font-medium",children:h.text})]}),e("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden",children:e("div",{className:"h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-full transition-all duration-300 ease-out",style:{width:`${m}%`}})}),t("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:[Math.round(m),"% Complete"]})]}),e("div",{className:"flex justify-center space-x-2",children:[0,1,2].map(k=>e("div",{className:"w-3 h-3 bg-blue-500 rounded-full animate-bounce",style:{animationDelay:`${k*.2}s`,animationDuration:"1s"}},k))}),e("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:a}),e("div",{className:"grid grid-cols-3 gap-4 pt-4",children:[{icon:q,label:"Real-time Chat"},{icon:K,label:"Team Collaboration"},{icon:G,label:"Premium Features"}].map((k,x)=>{const _=k.icon;return t("div",{className:"text-center opacity-60 hover:opacity-100 transition-opacity duration-300",children:[e(_,{className:"w-6 h-6 text-gray-400 mx-auto mb-1"}),e("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:k.label})]},x)})})]})})},Os=()=>{var X,ue;const{user:a,socket:s,isConnected:i,onlineUsers:m,typingUsers:c,unreadCounts:n,sendMessage:h,startTyping:y,stopTyping:k,markAsRead:x,addNotification:_}=le(),S=lt(),{roomId:b}=Ut(),[N,j]=r.useState(null),[M,E]=r.useState([]),[v,w]=r.useState([]),[d,l]=r.useState(!0),[o,C]=r.useState([]),[p,D]=r.useState([]),[T,g]=r.useState(!1),[u,$]=r.useState(!1),[V,O]=r.useState(!1),[F,H]=r.useState(!1),[Q,Z]=r.useState(!1),[te,me]=r.useState(!1);r.useRef(null);const ae=r.useRef(null);r.useRef(null);const f=[{id:1,name:"General Chat",description:"General discussion for everyone",room_type:"public",member_count:1247,is_active:!0,has_unread:!1,last_message:"Welcome to ChatFlow Pro!",last_activity:new Date().toISOString(),created_at:new Date().toISOString()},{id:2,name:"Random",description:"Random conversations and fun topics",room_type:"public",member_count:892,is_active:!1,has_unread:!0,last_message:"Anyone up for a game?",last_activity:new Date(Date.now()-36e5).toISOString(),created_at:new Date(Date.now()-864e5).toISOString()},{id:3,name:"Live Events",description:"Live streaming and events",room_type:"live",member_count:2156,is_active:!0,has_unread:!1,is_live:!0,last_message:"Live event starting now!",last_activity:new Date().toISOString(),created_at:new Date(Date.now()-1728e5).toISOString()},{id:4,name:"VIP Lounge",description:"Exclusive VIP member area",room_type:"vip",member_count:89,is_active:!1,has_unread:!1,requires_vip:!0,last_message:"VIP exclusive content available",last_activity:new Date(Date.now()-72e5).toISOString(),created_at:new Date(Date.now()-2592e5).toISOString()}],I=[{id:1,content:"Welcome to ChatFlow Pro! This is a demo of our professional chat platform.",sender:{id:"system",username:"system",display_name:"ChatFlow System",avatar:null,is_vip:!1,is_staff:!0},created_at:new Date(Date.now()-864e5).toISOString(),message_type:"text",is_edited:!1,is_pinned:!0,reactions:[{emoji:"👍",count:12,users:["user1","user2"]},{emoji:"❤️",count:8,users:["user3","user4"]}]},{id:2,content:"This is a demo message to show how the chat interface works. You can create rooms, send messages, and interact with other users!",sender:{id:"demo_user",username:"demo_user",display_name:"Demo User",avatar:null,is_vip:!1,is_staff:!1},created_at:new Date(Date.now()-36e5).toISOString(),message_type:"text",is_edited:!1,is_pinned:!1,reactions:[{emoji:"👋",count:5,users:["user2","user3"]}]}],A=()=>{me(!0),E(f),w(I),j(f[0]),l(!1),_&&_({type:"info",title:"Demo Mode",message:"Running in demo mode. Create an account to access full features!"})},B=async()=>{try{l(!0);const L=localStorage.getItem("chatflow_token");if(!L){console.log("No auth token found, proceeding without authentication"),E([]),l(!1);return}const z=await fetch("http://localhost:8000/api/chat/rooms/",{headers:{Authorization:`Bearer ${L}`,"Content-Type":"application/json"}});if(z.ok){const W=await z.json();E(W.results||[])}else console.error("Failed to load rooms, status:",z.status),E([]),z.status===401&&localStorage.removeItem("chatflow_token")}catch(L){console.error("Error loading rooms:",L),console.log("Backend not available, enabling demo mode"),A();return}finally{l(!1)}},re=async L=>{if(L)try{l(!0);const z=await fetch(`http://localhost:8000/api/chat/rooms/${L}/messages/`,{headers:{Authorization:`Bearer ${localStorage.getItem("chatflow_token")}`,"Content-Type":"application/json"}});if(z.ok){const W=await z.json();w(W.results||[])}else console.error("Failed to load messages"),w([])}catch(z){console.error("Error loading messages:",z),w([])}finally{l(!1)}},fe=async L=>{if(L)try{const z=await fetch(`http://localhost:8000/api/chat/rooms/${L}/members/`,{headers:{Authorization:`Bearer ${localStorage.getItem("chatflow_token")}`,"Content-Type":"application/json"}});if(z.ok){const W=await z.json();C(W.results||[])}else console.error("Failed to load room members"),C([])}catch(z){console.error("Error loading room members:",z),C([])}};r.useEffect(()=>{B();const L=setTimeout(()=>{d&&(console.log("Loading timeout reached, stopping loading state"),l(!1))},1e4);return()=>clearTimeout(L)},[]),r.useEffect(()=>{if(b){const L=M.find(z=>z.id.toString()===b);L&&(j(L),re(L.id),fe(L.id))}else if(M.length>0){const L=M[0];j(L),re(L.id),fe(L.id),S(`/chat/${L.id}`)}},[b,M,S]);const ve=L=>{j(L),re(L.id),fe(L.id),S(`/chat/${L.id}`),x&&x(L.id)},oe=async(L,z=null,W="send")=>{if(!(!L.trim()&&W==="send")&&N)try{if(W==="send"){const ee={id:`temp_${Date.now()}`,content:L.trim(),sender:a,created_at:new Date().toISOString(),message_type:"text",is_edited:!1,is_pinned:!1,reactions:[],status:"sending"};w(J=>[...J,ee]);const se=await fetch(`http://localhost:8000/api/chat/rooms/${N.id}/messages/`,{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("chatflow_token")}`,"Content-Type":"application/json"},body:JSON.stringify({content:L.trim(),message_type:"text",reply_to:z})});if(se.ok){const J=await se.json();w(Me=>Me.map(Ie=>Ie.id===ee.id?{...J,status:"sent"}:Ie)),h&&h(L,N.id)}else w(J=>J.filter(Me=>Me.id!==ee.id)),_({type:"error",title:"Error",message:"Failed to send message"})}else W==="edit"?(await fetch(`http://localhost:8000/api/chat/messages/${z}/`,{method:"PATCH",headers:{Authorization:`Bearer ${localStorage.getItem("chatflow_token")}`,"Content-Type":"application/json"},body:JSON.stringify({content:L})})).ok&&w(se=>se.map(J=>J.id===z?{...J,content:L,is_edited:!0}:J)):W==="delete"?(await fetch(`http://localhost:8000/api/chat/messages/${z}/`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("chatflow_token")}`}})).ok&&w(se=>se.filter(J=>J.id!==z)):W==="pin"&&(await fetch(`http://localhost:8000/api/chat/messages/${z}/pin/`,{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("chatflow_token")}`}})).ok&&w(se=>se.map(J=>J.id===z?{...J,is_pinned:!J.is_pinned}:J))}catch(ee){console.error("Error handling message:",ee),_({type:"error",title:"Error",message:"Failed to process message"})}},R=()=>{y&&y(N==null?void 0:N.id),ae.current&&clearTimeout(ae.current),ae.current=setTimeout(()=>{k&&k(N==null?void 0:N.id)},1e3)},U=async L=>{try{console.log("Creating room with data:",L);const z=await fetch("http://localhost:8000/api/chat/rooms/",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("chatflow_token")}`,"Content-Type":"application/json"},body:JSON.stringify(L)});if(console.log("Room creation response status:",z.status),z.ok){const W=await z.json();E(ee=>[W,...ee]),_({type:"success",title:"Room Created",message:`Successfully created room "${W.name}"`}),ve(W)}else{const W=await z.json();throw console.log("Room creation error response:",W),new Error(W.message||"Failed to create room")}}catch(z){throw console.error("Error creating room:",z),_({type:"error",title:"Error",message:z.message||"Failed to create room"}),z}},he=async()=>{try{await logout(),S("/")}catch(L){console.error("Logout error:",L),_({type:"error",title:"Logout Failed",message:"Failed to logout properly"})}};return d&&M.length===0?e(Es,{message:"Loading your chat rooms..."}):t("div",{className:"h-screen bg-gray-50 dark:bg-gray-900 flex flex-col",children:[e("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 z-10",children:e("div",{className:"px-4 sm:px-6 lg:px-8",children:t("div",{className:"flex justify-between items-center py-3",children:[t("div",{className:"flex items-center space-x-4",children:[e("div",{className:"h-8 w-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:e("span",{className:"text-white font-bold text-sm",children:"CF"})}),e("h1",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"ChatFlow Pro"}),te&&t("div",{className:"flex items-center space-x-2",children:[e("div",{className:"w-2 h-2 rounded-full bg-orange-500 animate-pulse"}),e("span",{className:"text-xs text-orange-600 dark:text-orange-400 font-medium",children:"Demo Mode"})]}),!te&&t("div",{className:"flex items-center space-x-2",children:[e("div",{className:`w-2 h-2 rounded-full ${i?"bg-green-500":"bg-red-500"}`}),e("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:i?"Connected":"Disconnected"})]})]}),t("div",{className:"flex items-center space-x-3",children:[e("button",{onClick:()=>g(!0),className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",title:"Search Messages",children:e(ce,{className:"w-5 h-5"})}),e("button",{onClick:()=>H(!0),className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",title:"Create Room",children:e(Ne,{className:"w-5 h-5"})}),e("button",{onClick:()=>{_({id:Date.now(),type:"info",title:"Notifications",message:"Notifications panel coming soon!"})},className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",title:"Notifications",children:e(rt,{className:"w-5 h-5"})}),e("button",{onClick:()=>O(!0),className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",title:"Settings",children:e(Te,{className:"w-5 h-5"})}),t("div",{className:"flex items-center space-x-3 pl-3 border-l border-gray-200 dark:border-gray-700",children:[t("div",{className:"flex items-center space-x-2",children:[e("div",{className:"h-8 w-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center",children:e("span",{className:"text-white text-sm font-medium",children:((X=a==null?void 0:a.display_name)==null?void 0:X[0])||((ue=a==null?void 0:a.username)==null?void 0:ue[0])||"U"})}),t("div",{className:"hidden sm:block",children:[e("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:(a==null?void 0:a.display_name)||(a==null?void 0:a.username)||"User"}),e("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:a!=null&&a.is_vip?"VIP Member":"Member"})]}),(a==null?void 0:a.is_vip)&&e(G,{className:"w-4 h-4 text-yellow-500"})]}),e("button",{onClick:he,className:"text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors",children:"Sign Out"})]})]})]})})}),t("div",{className:"flex flex-1 overflow-hidden",children:[e("div",{className:`${Q?"w-16":"w-80"} transition-all duration-200 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex-shrink-0`,children:e(js,{rooms:M,selectedRoom:N,onRoomSelect:ve,onlineUsers:m,unreadCounts:n,user:a,isCollapsed:Q,onToggleCollapse:()=>Z(!Q),onCreateRoom:()=>H(!0)})}),e("div",{className:"flex-1 flex flex-col overflow-hidden",children:M.length===0&&!te?e(Ls,{user:a,onCreateRoom:()=>H(!0),onBrowseRooms:()=>$(!0),onTryDemo:A,showDemoOption:!0}):N?e(Is,{room:N,messages:v,onSendMessage:oe,onTyping:R,user:a,roomMembers:o,typingUsers:c,pinnedMessages:p,isLoading:d,onReply:L=>console.log("Reply to:",L),onEdit:L=>console.log("Edit:",L),onDelete:L=>console.log("Delete:",L),onPin:L=>console.log("Pin:",L),onReaction:(L,z)=>console.log("React:",L,z)}):e("div",{className:"flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:t("div",{className:"text-center",children:[e(q,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),e("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Select a Room"}),e("p",{className:"text-gray-500 dark:text-gray-400 max-w-md",children:"Choose a room from the sidebar to start chatting with your community."})]})})})]}),T&&e(Ds,{isOpen:T,onClose:()=>g(!1),messages:v,rooms:M,users:o,onMessageSelect:L=>{console.log("Navigate to message:",L),g(!1)}}),u&&e(As,{isOpen:u,onClose:()=>$(!1),onRoomSelect:L=>{ve(L),$(!1)},user:a,currentRoom:N}),F&&e(Ps,{isOpen:F,onClose:()=>H(!1),onCreateRoom:U,user:a}),V&&e("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:t("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-5/6 overflow-y-auto",children:[t("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700",children:[e("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Chat Settings"}),e("button",{onClick:()=>O(!1),className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:e(de,{className:"w-5 h-5"})})]}),t("div",{className:"p-6",children:[t("div",{className:"space-y-6",children:[t("div",{children:[e("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Notification Preferences"}),t("div",{className:"space-y-3",children:[t("label",{className:"flex items-center",children:[e("input",{type:"checkbox",className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500",defaultChecked:!0}),e("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:"Desktop notifications"})]}),t("label",{className:"flex items-center",children:[e("input",{type:"checkbox",className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500",defaultChecked:!0}),e("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:"Sound notifications"})]}),t("label",{className:"flex items-center",children:[e("input",{type:"checkbox",className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),e("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:"Email notifications"})]})]})]}),t("div",{children:[e("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Privacy Settings"}),t("div",{className:"space-y-3",children:[t("label",{className:"flex items-center",children:[e("input",{type:"checkbox",className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500",defaultChecked:!0}),e("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:"Show online status"})]}),t("label",{className:"flex items-center",children:[e("input",{type:"checkbox",className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500",defaultChecked:!0}),e("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:"Allow direct messages"})]}),t("label",{className:"flex items-center",children:[e("input",{type:"checkbox",className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),e("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:"Read receipts"})]})]})]})]}),t("div",{className:"mt-6 flex justify-end space-x-3",children:[e("button",{onClick:()=>O(!1),className:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200",children:"Cancel"}),e("button",{onClick:()=>O(!1),className:"px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg",children:"Save Changes"})]})]})]})})]})},Vs=()=>{const{user:a,addNotification:s}=le(),[i,m]=r.useState("overview"),[c,n]=r.useState([]),[h,y]=r.useState(null),[k,x]=r.useState([]),[_,S]=r.useState([]),[b,N]=r.useState([]),[j,M]=r.useState(!0);r.useEffect(()=>{E()},[]);const E=async()=>{try{M(!0);const o=await(await fetch("/api/vip/tiers/")).json();n(o);const C=await fetch("/api/vip/subscription/");if(C.ok){const V=await C.json();y(V)}const D=await(await fetch("/api/vip/exclusive-rooms/")).json();x(D);const g=await(await fetch("/api/vip/custom-emojis/")).json();S(g);const $=await(await fetch("/api/vip/benefits/")).json();N($)}catch(l){console.error("Failed to load VIP data:",l),s({type:"error",title:"Error",message:"Failed to load VIP data"})}finally{M(!1)}},v=async()=>{if(confirm("Are you sure you want to cancel your VIP subscription?"))try{(await fetch("/api/vip/cancel/",{method:"POST"})).ok&&(s({type:"success",title:"Subscription Cancelled",message:"Your VIP subscription has been cancelled"}),E())}catch{s({type:"error",title:"Error",message:"Failed to cancel subscription"})}},w=l=>{const o="w-5 h-5 text-purple-600 dark:text-purple-400";switch(l){case"feature_access":return e(Ke,{className:o});case"priority_support":return e(Se,{className:o});case"exclusive_content":return e(Re,{className:o});case"early_access":return e($e,{className:o});case"custom_branding":return e(Be,{className:o});case"increased_limits":return e(Ee,{className:o});default:return e(vr,{className:o})}},d=()=>t("div",{className:"space-y-8",children:[e("div",{className:"bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl p-6 text-white",children:t("div",{className:"flex items-center justify-between",children:[t("div",{children:[t("h2",{className:"text-2xl font-bold flex items-center gap-2",children:[e(G,{className:"w-8 h-8"}),"VIP Status"]}),h?t("div",{className:"mt-2",children:[e("p",{className:"text-lg",children:h.tier.name}),t("p",{className:"text-purple-200",children:[h.days_remaining," days remaining"]})]}):e("p",{className:"text-lg mt-2",children:"Not a VIP member"})]}),e("div",{className:"text-right",children:h&&t("div",{className:"bg-white/20 rounded-lg p-3",children:[e("div",{className:"text-2xl font-bold",children:h.tier.badge_icon}),t("div",{className:"text-sm",children:["Level ",h.tier.level]})]})})]})}),e("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:b.map(l=>e("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border",children:t("div",{className:"flex items-start gap-3",children:[e("div",{className:"p-2 bg-purple-100 dark:bg-purple-900 rounded-lg",children:w(l.benefit_type)}),t("div",{children:[e("h3",{className:"font-semibold text-gray-900 dark:text-white",children:l.title}),e("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:l.description}),e("div",{className:"mt-2",children:t("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",children:["Level ",l.tier_level_required,"+"]})})]})]})},l.id))}),t("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border",children:[e("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Quick Actions"}),t("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[t("button",{onClick:()=>m("rooms"),className:"flex flex-col items-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors",children:[e(K,{className:"w-6 h-6 text-purple-600 dark:text-purple-400 mb-2"}),e("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Exclusive Rooms"})]}),t("button",{onClick:()=>m("emojis"),className:"flex flex-col items-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors",children:[e(Be,{className:"w-6 h-6 text-purple-600 dark:text-purple-400 mb-2"}),e("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Custom Emojis"})]}),t("button",{onClick:()=>m("support"),className:"flex flex-col items-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors",children:[e(Se,{className:"w-6 h-6 text-purple-600 dark:text-purple-400 mb-2"}),e("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Priority Support"})]}),t("button",{onClick:()=>m("settings"),className:"flex flex-col items-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors",children:[e(Te,{className:"w-6 h-6 text-purple-600 dark:text-purple-400 mb-2"}),e("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"VIP Settings"})]})]})]})]});return j?e("div",{className:"min-h-screen flex items-center justify-center",children:e("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"})}):t("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[e("div",{className:"bg-white dark:bg-gray-800 shadow-sm border-b",children:e("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:t("div",{className:"flex items-center justify-between h-16",children:[t("div",{className:"flex items-center gap-3",children:[e(G,{className:"w-8 h-8 text-purple-600"}),e("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"VIP Membership"})]}),h&&t("div",{className:"flex items-center gap-4",children:[t("div",{className:"text-right",children:[e("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:h.tier.name}),t("div",{className:"text-xs text-purple-600 dark:text-purple-400",children:["Expires ",new Date(h.expires_at).toLocaleDateString()]})]}),e("button",{onClick:v,className:"text-red-600 hover:text-red-700 text-sm",children:"Cancel"})]})]})})}),e("div",{className:"bg-white dark:bg-gray-800 border-b",children:e("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e("nav",{className:"flex space-x-8",children:[{id:"overview",label:"Overview",icon:G},{id:"tiers",label:"VIP Tiers",icon:Re},{id:"rooms",label:"Exclusive Rooms",icon:K},{id:"emojis",label:"Custom Emojis",icon:Be},{id:"support",label:"Priority Support",icon:Se},{id:"settings",label:"Settings",icon:Te}].map(l=>t("button",{onClick:()=>m(l.id),className:`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${i===l.id?"border-purple-500 text-purple-600 dark:text-purple-400":"border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"}`,children:[e(l.icon,{className:"w-4 h-4"}),l.label]},l.id))})})}),e("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:i==="overview"&&e(d,{})})]})},zs=()=>{const{user:a,addNotification:s}=le(),[i,m]=r.useState("live"),[c,n]=r.useState([]),[h,y]=r.useState([]),[k,x]=r.useState([]),[_,S]=r.useState([]),[b,N]=r.useState(!0),[j,M]=r.useState("");r.useState("all"),r.useEffect(()=>{E()},[]);const E=async()=>{try{N(!0);const C=await(await fetch("/api/live/events/?status=live")).json();n(C.results||[]);const D=await(await fetch("/api/live/events/?status=scheduled")).json();y(D.results||[]);const g=await(await fetch("/api/live/rooms/?is_active=true")).json();x(g.results||[]);const $=await(await fetch("/api/live/events/my/")).json();S($.results||[])}catch(o){console.error("Failed to load live data:",o),s({type:"error",title:"Error",message:"Failed to load live events"})}finally{N(!1)}},v=async o=>{try{if((await fetch(`/api/live/events/${o}/join/`,{method:"POST"})).ok)s({type:"success",title:"Joined Event",message:"Successfully joined the live event!"});else throw new Error("Failed to join event")}catch(C){s({type:"error",title:"Error",message:C.message})}},w=async o=>{try{if((await fetch(`/api/live/rooms/${o}/join/`,{method:"POST"})).ok)s({type:"success",title:"Joined Room",message:"Successfully joined the live room!"});else throw new Error("Failed to join room")}catch(C){s({type:"error",title:"Error",message:C.message})}},d=({event:o,isLive:C=!1})=>{var p;return t("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border overflow-hidden",children:[o.banner_image&&t("div",{className:"h-48 bg-gray-200 dark:bg-gray-700 relative",children:[e("img",{src:o.banner_image,alt:o.title,className:"w-full h-full object-cover"}),C&&e("div",{className:"absolute top-4 left-4",children:t("span",{className:"inline-flex items-center gap-1 px-2 py-1 bg-red-500 text-white text-xs font-medium rounded-full",children:[e(ie,{className:"w-3 h-3"}),"LIVE"]})}),o.vip_only&&e("div",{className:"absolute top-4 right-4",children:t("span",{className:"inline-flex items-center gap-1 px-2 py-1 bg-purple-500 text-white text-xs font-medium rounded-full",children:[e(G,{className:"w-3 h-3"}),"VIP"]})})]}),t("div",{className:"p-6",children:[t("div",{className:"flex items-start justify-between mb-3",children:[t("div",{children:[e("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:o.title}),e("p",{className:"text-sm text-gray-600 dark:text-gray-400 capitalize",children:o.event_type.replace("_"," ")})]}),t("div",{className:"flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400",children:[e(K,{className:"w-4 h-4"}),o.current_viewers||0]})]}),e("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2",children:o.description}),t("div",{className:"flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-4",children:[t("div",{className:"flex items-center gap-1",children:[e(ft,{className:"w-4 h-4"}),new Date(o.scheduled_start).toLocaleDateString()]}),t("div",{className:"flex items-center gap-1",children:[e($e,{className:"w-4 h-4"}),new Date(o.scheduled_start).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]})]}),t("div",{className:"flex items-center gap-3 mb-4",children:[e("div",{className:"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center",children:e("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:((p=o.host.display_name)==null?void 0:p[0])||o.host.username[0]})}),t("div",{children:[e("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:o.host.display_name||o.host.username}),e("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Host"})]})]}),t("div",{className:"flex items-center gap-2",children:[C?t("button",{onClick:()=>v(o.id),className:"flex-1 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center justify-center gap-2",children:[e(Gr,{className:"w-4 h-4"}),"Join Live"]}):t("button",{onClick:()=>v(o.id),className:"flex-1 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center justify-center gap-2",children:[e(ft,{className:"w-4 h-4"}),"Register"]}),e("button",{className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",children:e(Ge,{className:"w-4 h-4"})}),e("button",{className:"p-2 text-gray-400 hover:text-red-500 transition-colors",children:e(Ze,{className:"w-4 h-4"})})]})]})]})},l=({room:o})=>t("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border overflow-hidden",children:[t("div",{className:"p-4 border-b border-gray-200 dark:border-gray-700",children:[t("div",{className:"flex items-center justify-between",children:[t("div",{className:"flex items-center gap-3",children:[e("div",{className:"w-3 h-3 rounded-full bg-green-500 animate-pulse",title:"Live"}),e("h3",{className:"font-semibold text-gray-900 dark:text-white",children:o.name}),o.is_featured&&e(Re,{className:"w-4 h-4 text-yellow-500"})]}),t("div",{className:"flex items-center gap-1 text-sm text-gray-500 dark:text-gray-400",children:[e(ct,{className:"w-4 h-4"}),o.current_viewers]})]}),o.topic&&e("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:o.topic})]}),t("div",{className:"p-4",children:[e("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2",children:o.description}),t("div",{className:"flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-4",children:[t("div",{className:"flex items-center gap-1",children:[e(q,{className:"w-4 h-4"}),o.total_messages_today," messages today"]}),t("div",{className:"flex items-center gap-1",children:[e(K,{className:"w-4 h-4"}),"Peak: ",o.peak_viewers]})]}),t("button",{onClick:()=>w(o.id),className:"w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center justify-center gap-2",children:[e(ie,{className:"w-4 h-4"}),"Join Room"]})]})]});return b?e("div",{className:"min-h-screen flex items-center justify-center",children:e("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-500"})}):t("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[e("div",{className:"bg-white dark:bg-gray-800 shadow-sm border-b",children:e("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:t("div",{className:"flex items-center justify-between h-16",children:[t("div",{className:"flex items-center gap-3",children:[e(ie,{className:"w-8 h-8 text-red-500"}),e("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Live Events"})]}),t("div",{className:"flex items-center gap-4",children:[t("div",{className:"relative",children:[e(ce,{className:"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),e("input",{type:"text",placeholder:"Search events...",value:j,onChange:o=>M(o.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-red-500 focus:border-transparent"})]}),t("button",{className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2",children:[e(Ne,{className:"w-4 h-4"}),"Create Event"]})]})]})})}),e("div",{className:"bg-white dark:bg-gray-800 border-b",children:e("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e("nav",{className:"flex space-x-8",children:[{id:"live",label:"Live Now",count:c.length},{id:"rooms",label:"Live Rooms",count:k.length},{id:"upcoming",label:"Upcoming",count:h.length},{id:"my-events",label:"My Events",count:_.length}].map(o=>t("button",{onClick:()=>m(o.id),className:`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${i===o.id?"border-red-500 text-red-600 dark:text-red-400":"border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"}`,children:[o.label,o.count>0&&e("span",{className:"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-2 py-1 rounded-full text-xs",children:o.count})]},o.id))})})}),t("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[i==="live"&&t("div",{children:[e("div",{className:"flex items-center justify-between mb-6",children:t("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:["Live Events (",c.length,")"]})}),c.length>0?e("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:c.map(o=>e(d,{event:o,isLive:!0},o.id))}):t("div",{className:"text-center py-12",children:[e(ie,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),e("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No live events"}),e("p",{className:"text-gray-600 dark:text-gray-400",children:"Check back later for live events and sessions."})]})]}),i==="rooms"&&t("div",{children:[e("div",{className:"flex items-center justify-between mb-6",children:t("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:["Live Rooms (",k.length,")"]})}),k.length>0?e("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:k.map(o=>e(l,{room:o},o.id))}):t("div",{className:"text-center py-12",children:[e(K,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),e("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No active rooms"}),e("p",{className:"text-gray-600 dark:text-gray-400",children:"Create a room or wait for others to start chatting."})]})]})]})]})},Fs=()=>{const{user:a,addNotification:s}=le(),[i,m]=r.useState("overview"),[c,n]=r.useState("7d"),[h,y]=r.useState({}),[k,x]=r.useState(!0),[_,S]=r.useState(!1);r.useEffect(()=>{b()},[c]);const b=async()=>{try{x(!0);const w=await(await fetch(`/api/analytics/?range=${c}`)).json();y(w)}catch(v){console.error("Failed to load analytics:",v),s({type:"error",title:"Error",message:"Failed to load analytics data"})}finally{x(!1)}},N=async()=>{S(!0),await b(),S(!1),s({type:"success",title:"Analytics Refreshed",message:"Analytics data has been updated"})},j=async v=>{try{const d=await(await fetch(`/api/analytics/export/?type=${v}&range=${c}`)).blob(),l=window.URL.createObjectURL(d),o=document.createElement("a");o.href=l,o.download=`analytics-${v}-${c}.csv`,document.body.appendChild(o),o.click(),window.URL.revokeObjectURL(l),document.body.removeChild(o),s({type:"success",title:"Export Complete",message:"Analytics data has been exported"})}catch{s({type:"error",title:"Export Failed",message:"Failed to export analytics data"})}},M=({title:v,value:w,change:d,icon:l,color:o="blue"})=>e("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border",children:t("div",{className:"flex items-center justify-between",children:[t("div",{children:[e("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:v}),e("p",{className:"text-2xl font-bold text-gray-900 dark:text-white mt-2",children:w}),d!==void 0&&t("div",{className:`flex items-center mt-2 text-sm ${d>=0?"text-green-600":"text-red-600"}`,children:[e(gs,{className:`w-4 h-4 mr-1 ${d<0?"rotate-180":""}`}),Math.abs(d),"% from last period"]})]}),e("div",{className:`p-3 rounded-full bg-${o}-100 dark:bg-${o}-900/20`,children:e(l,{className:`w-6 h-6 text-${o}-600 dark:text-${o}-400`})})]})}),E=()=>{var v,w,d,l;return t("div",{className:"space-y-8",children:[t("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[e(M,{title:"Total Users",value:((v=h.total_users)==null?void 0:v.toLocaleString())||"0",change:h.user_growth,icon:K,color:"blue"}),e(M,{title:"Messages Sent",value:((w=h.total_messages)==null?void 0:w.toLocaleString())||"0",change:h.message_growth,icon:q,color:"green"}),e(M,{title:"Active Rooms",value:((d=h.active_rooms)==null?void 0:d.toLocaleString())||"0",change:h.room_growth,icon:bt,color:"purple"}),e(M,{title:"VIP Revenue",value:`$${((l=h.vip_revenue)==null?void 0:l.toLocaleString())||"0"}`,change:h.revenue_growth,icon:vt,color:"yellow"})]}),t("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[t("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border",children:[t("div",{className:"flex items-center justify-between mb-4",children:[e("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"User Activity"}),e("button",{onClick:()=>j("user-activity"),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:e(st,{className:"w-4 h-4"})})]}),e("div",{className:"h-64 bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center",children:t("div",{className:"text-center",children:[e(Ue,{className:"w-12 h-12 text-gray-400 mx-auto mb-2"}),e("p",{className:"text-gray-500 dark:text-gray-400",children:"User Activity Chart"})]})})]}),t("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border",children:[t("div",{className:"flex items-center justify-between mb-4",children:[e("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Message Volume"}),e("button",{onClick:()=>j("message-volume"),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:e(st,{className:"w-4 h-4"})})]}),e("div",{className:"h-64 bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center",children:t("div",{className:"text-center",children:[e(Ue,{className:"w-12 h-12 text-gray-400 mx-auto mb-2"}),e("p",{className:"text-gray-500 dark:text-gray-400",children:"Message Volume Chart"})]})})]})]}),t("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[t("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border",children:[e("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Most Active Users"}),e("div",{className:"space-y-3",children:(h.top_users||[]).map((o,C)=>t("div",{className:"flex items-center justify-between",children:[t("div",{className:"flex items-center gap-3",children:[e("span",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 w-4",children:C+1}),e("div",{className:"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center",children:e("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:o.username[0].toUpperCase()})}),e("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:o.username})]}),t("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:[o.message_count," messages"]})]},o.id))})]}),t("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border",children:[e("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Most Active Rooms"}),e("div",{className:"space-y-3",children:(h.top_rooms||[]).map((o,C)=>t("div",{className:"flex items-center justify-between",children:[t("div",{className:"flex items-center gap-3",children:[e("span",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 w-4",children:C+1}),e("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:o.name})]}),t("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:[o.message_count," messages"]})]},o.id))})]}),t("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border",children:[e("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Recent Activity"}),e("div",{className:"space-y-3",children:(h.recent_activity||[]).map((o,C)=>t("div",{className:"flex items-start gap-3",children:[e("div",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2"}),t("div",{className:"flex-1",children:[e("p",{className:"text-sm text-gray-900 dark:text-white",children:o.description}),e("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:new Date(o.timestamp).toLocaleString()})]})]},C))})]})]})]})};return k?e("div",{className:"min-h-screen flex items-center justify-center",children:e("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"})}):t("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[e("div",{className:"bg-white dark:bg-gray-800 shadow-sm border-b",children:e("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:t("div",{className:"flex items-center justify-between h-16",children:[t("div",{className:"flex items-center gap-3",children:[e(Ue,{className:"w-8 h-8 text-blue-600"}),e("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Analytics Dashboard"})]}),t("div",{className:"flex items-center gap-4",children:[t("select",{value:c,onChange:v=>n(v.target.value),className:"border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e("option",{value:"1d",children:"Last 24 hours"}),e("option",{value:"7d",children:"Last 7 days"}),e("option",{value:"30d",children:"Last 30 days"}),e("option",{value:"90d",children:"Last 90 days"}),e("option",{value:"1y",children:"Last year"})]}),e("button",{onClick:N,disabled:_,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors disabled:opacity-50",children:e(Zr,{className:`w-4 h-4 ${_?"animate-spin":""}`})})]})]})})}),e("div",{className:"bg-white dark:bg-gray-800 border-b",children:e("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e("nav",{className:"flex space-x-8",children:[{id:"overview",label:"Overview",icon:Ue},{id:"users",label:"Users",icon:K},{id:"messages",label:"Messages",icon:q},{id:"revenue",label:"Revenue",icon:vt},{id:"performance",label:"Performance",icon:bt}].map(v=>t("button",{onClick:()=>m(v.id),className:`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${i===v.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"}`,children:[e(v.icon,{className:"w-4 h-4"}),v.label]},v.id))})})}),e("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:i==="overview"&&e(E,{})})]})},Us=({user:a})=>{const[s,i]=r.useState([]),[m,c]=r.useState([]),[n,h]=r.useState(null),[y,k]=r.useState({totalWaiting:0,averageWaitTime:0,activeChats:0,resolvedToday:0}),x=[{id:1,customer:{name:"John Doe",email:"<EMAIL>",avatar:null,isVip:!1,location:"New York, US",device:"Desktop"},waitTime:180,priority:"normal",department:"support",lastMessage:"I need help with my account",timestamp:new Date(Date.now()-18e4),tags:["account","login"],assignedAgent:null,status:"waiting"},{id:2,customer:{name:"Sarah Wilson",email:"<EMAIL>",avatar:null,isVip:!0,location:"London, UK",device:"Mobile"},waitTime:45,priority:"high",department:"vip",lastMessage:"VIP support needed for billing issue",timestamp:new Date(Date.now()-45e3),tags:["billing","vip"],assignedAgent:null,status:"waiting"},{id:3,customer:{name:"Mike Johnson",email:"<EMAIL>",avatar:null,isVip:!1,location:"Toronto, CA",device:"Tablet"},waitTime:320,priority:"normal",department:"technical",lastMessage:"Video call not working properly",timestamp:new Date(Date.now()-32e4),tags:["technical","video"],assignedAgent:"agent_1",status:"active"}],_=[{id:"agent_1",name:"Alice Smith",email:"<EMAIL>",avatar:null,status:"online",department:"support",activeChats:2,maxChats:5,skills:["general","technical"],responseTime:45,satisfaction:4.8,isVipAgent:!1},{id:"agent_2",name:"Bob Wilson",email:"<EMAIL>",avatar:null,status:"busy",department:"vip",activeChats:3,maxChats:3,skills:["vip","billing"],responseTime:30,satisfaction:4.9,isVipAgent:!0},{id:"agent_3",name:"Carol Davis",email:"<EMAIL>",avatar:null,status:"away",department:"technical",activeChats:1,maxChats:4,skills:["technical","advanced"],responseTime:60,satisfaction:4.7,isVipAgent:!1}];r.useEffect(()=>{i(x),c(_);const v=x.filter(l=>l.status==="waiting").length,w=x.filter(l=>l.status==="active").length,d=x.reduce((l,o)=>l+o.waitTime,0)/x.length;k({totalWaiting:v,averageWaitTime:Math.round(d),activeChats:w,resolvedToday:24})},[]);const S=v=>{const w=Math.floor(v/60),d=v%60;return`${w}:${d.toString().padStart(2,"0")}`},b=v=>{switch(v){case"high":return"text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300";case"normal":return"text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-300";case"low":return"text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300";default:return"text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300"}},N=v=>{switch(v){case"online":return"text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300";case"busy":return"text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300";case"away":return"text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300";case"offline":return"text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300";default:return"text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300"}},j=(v,w)=>{i(d=>d.map(l=>l.id===v?{...l,assignedAgent:w,status:"active"}:l)),c(d=>d.map(l=>l.id===w?{...l,activeChats:l.activeChats+1}:l))},M=v=>{const w=s.find(l=>l.id===v);if(!w)return;const d=m.filter(l=>l.status==="online"&&l.activeChats<l.maxChats&&(w.department==="vip"?l.isVipAgent:!0)&&l.skills.some(o=>w.tags.includes(o)||o==="general"));if(d.length>0){const l=d.sort((o,C)=>{const p=o.activeChats/o.maxChats,D=C.activeChats/C.maxChats;return p!==D?p-D:o.responseTime-C.responseTime})[0];j(v,l.id)}},E=(v,w)=>{const d=s.find(l=>l.id===v);!d||!d.assignedAgent||(c(l=>l.map(o=>o.id===d.assignedAgent?{...o,activeChats:o.activeChats-1}:o)),j(v,w))};return t("div",{className:"p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg",children:[t("div",{className:"mb-6",children:[e("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"Agent Queue Management"}),e("p",{className:"text-gray-600 dark:text-gray-400",children:"Manage chat assignments and monitor queue performance"})]}),t("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[e("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg",children:t("div",{className:"flex items-center",children:[e($e,{className:"w-8 h-8 text-blue-600 dark:text-blue-400"}),t("div",{className:"ml-3",children:[e("p",{className:"text-sm font-medium text-blue-600 dark:text-blue-400",children:"Waiting"}),e("p",{className:"text-2xl font-bold text-blue-900 dark:text-blue-100",children:y.totalWaiting})]})]})}),e("div",{className:"bg-green-50 dark:bg-green-900/20 p-4 rounded-lg",children:t("div",{className:"flex items-center",children:[e(q,{className:"w-8 h-8 text-green-600 dark:text-green-400"}),t("div",{className:"ml-3",children:[e("p",{className:"text-sm font-medium text-green-600 dark:text-green-400",children:"Active"}),e("p",{className:"text-2xl font-bold text-green-900 dark:text-green-100",children:y.activeChats})]})]})}),e("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg",children:t("div",{className:"flex items-center",children:[e(ms,{className:"w-8 h-8 text-yellow-600 dark:text-yellow-400"}),t("div",{className:"ml-3",children:[e("p",{className:"text-sm font-medium text-yellow-600 dark:text-yellow-400",children:"Avg Wait"}),e("p",{className:"text-2xl font-bold text-yellow-900 dark:text-yellow-100",children:S(y.averageWaitTime)})]})]})}),e("div",{className:"bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg",children:t("div",{className:"flex items-center",children:[e(Ee,{className:"w-8 h-8 text-purple-600 dark:text-purple-400"}),t("div",{className:"ml-3",children:[e("p",{className:"text-sm font-medium text-purple-600 dark:text-purple-400",children:"Resolved Today"}),e("p",{className:"text-2xl font-bold text-purple-900 dark:text-purple-100",children:y.resolvedToday})]})]})})]}),t("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[t("div",{children:[e("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Chat Queue"}),e("div",{className:"space-y-3",children:s.map(v=>e("div",{className:`border rounded-lg p-4 transition-colors ${n===v.id?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50"}`,onClick:()=>h(v.id),children:t("div",{className:"flex items-start justify-between",children:[t("div",{className:"flex-1",children:[t("div",{className:"flex items-center space-x-2 mb-2",children:[e("h4",{className:"font-medium text-gray-900 dark:text-white",children:v.customer.name}),v.customer.isVip&&e("span",{className:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 px-2 py-1 rounded-full text-xs font-medium",children:"VIP"}),e("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${b(v.priority)}`,children:v.priority})]}),e("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2",children:v.lastMessage}),t("div",{className:"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400",children:[t("div",{className:"flex items-center space-x-1",children:[e($e,{className:"w-3 h-3"}),t("span",{children:["Wait: ",S(v.waitTime)]})]}),e("span",{children:v.customer.location}),e("span",{children:v.customer.device})]}),e("div",{className:"flex flex-wrap gap-1 mt-2",children:v.tags.map((w,d)=>t("span",{className:"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded-full text-xs",children:["#",w]},d))})]}),t("div",{className:"flex flex-col items-end space-y-2",children:[v.status==="waiting"?e("button",{onClick:w=>{w.stopPropagation(),M(v.id)},className:"px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm",children:"Auto Assign"}):e("span",{className:"text-sm text-green-600 dark:text-green-400",children:"Assigned"}),t("div",{className:"flex space-x-1",children:[e("button",{className:"p-1 text-gray-400 hover:text-blue-600",children:e(q,{className:"w-4 h-4"})}),e("button",{className:"p-1 text-gray-400 hover:text-green-600",children:e(Pe,{className:"w-4 h-4"})}),e("button",{className:"p-1 text-gray-400 hover:text-purple-600",children:e(ut,{className:"w-4 h-4"})})]})]})]})},v.id))})]}),t("div",{children:[e("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Available Agents"}),e("div",{className:"space-y-3",children:m.map(v=>e("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors",children:t("div",{className:"flex items-start justify-between",children:[t("div",{className:"flex-1",children:[t("div",{className:"flex items-center space-x-2 mb-2",children:[e("h4",{className:"font-medium text-gray-900 dark:text-white",children:v.name}),e("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${N(v.status)}`,children:v.status}),v.isVipAgent&&e("span",{className:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 px-2 py-1 rounded-full text-xs font-medium",children:"VIP Agent"})]}),t("div",{className:"flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400 mb-2",children:[t("span",{children:["Department: ",v.department]}),t("span",{children:["Chats: ",v.activeChats,"/",v.maxChats]})]}),t("div",{className:"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400 mb-2",children:[t("span",{children:["Response: ",v.responseTime,"s"]}),t("span",{children:["Rating: ",v.satisfaction,"/5.0"]})]}),e("div",{className:"flex flex-wrap gap-1",children:v.skills.map((w,d)=>e("span",{className:"bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full text-xs",children:w},d))})]}),t("div",{className:"flex flex-col items-end space-y-2",children:[n&&e("button",{onClick:()=>{const w=s.find(d=>d.id===n);w&&w.status==="waiting"?j(n,v.id):w&&w.assignedAgent&&w.assignedAgent!==v.id&&E(n,v.id)},disabled:v.status!=="online"||v.activeChats>=v.maxChats,className:"px-3 py-1 bg-green-500 hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded text-sm",children:(()=>{const w=s.find(d=>d.id===n);return!w||w.status==="waiting"?"Assign":w.assignedAgent===v.id?"Assigned":"Transfer"})()}),e("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:e("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${v.activeChats/v.maxChats*100}%`}})})]})]})},v.id))})]})]})]})},Hs=()=>{const{user:a,addNotification:s}=le(),[i,m]=r.useState("users"),[c,n]=r.useState([]),[h,y]=r.useState([]),[k,x]=r.useState([]),[_,S]=r.useState([]),[b,N]=r.useState({}),[j,M]=r.useState(!0),[E,v]=r.useState("");r.useState([]),r.useEffect(()=>{w()},[i]);const w=async()=>{try{M(!0);const p=localStorage.getItem("chatflow_token");if(!p){s({type:"error",title:"Authentication Error",message:"Please log in to access admin features"});return}const D={Authorization:`Bearer ${p}`,"Content-Type":"application/json"};switch(i){case"users":const T=await fetch("http://localhost:8000/api/admin/users/",{headers:D});if(T.ok){const O=await T.json();n(O.results||[])}else n([]);break;case"rooms":const g=await fetch("http://localhost:8000/api/admin/rooms/",{headers:D});if(g.ok){const O=await g.json();y(O.results||[])}else y([]);break;case"bots":const u=await fetch("http://localhost:8000/api/admin/bots/",{headers:D});if(u.ok){const O=await u.json();x(O.results||[])}else x([]);break;case"reports":const $=await fetch("http://localhost:8000/api/admin/reports/",{headers:D});if($.ok){const O=await $.json();S(O.results||[])}else S([]);break;case"settings":const V=await fetch("http://localhost:8000/api/admin/settings/",{headers:D});if(V.ok){const O=await V.json();N(O)}else N({});break}}catch(p){console.error("Failed to load admin data:",p),s({type:"error",title:"Error",message:"Failed to load admin data. Please check your connection."})}finally{M(!1)}},d=async(p,D)=>{try{const g={Authorization:`Bearer ${localStorage.getItem("chatflow_token")}`,"Content-Type":"application/json"};let u;switch(D){case"edit":console.log("Edit user:",p);return;case"suspend":u=await fetch(`http://localhost:8000/api/admin/users/${p}/suspend/`,{method:"POST",headers:g});break;case"activate":u=await fetch(`http://localhost:8000/api/admin/users/${p}/activate/`,{method:"POST",headers:g});break;case"delete":if(!window.confirm("Are you sure you want to delete this user?"))return;u=await fetch(`http://localhost:8000/api/admin/users/${p}/`,{method:"DELETE",headers:g});break;default:throw new Error(`Unknown action: ${D}`)}if(u&&u.ok)s({type:"success",title:"Action Complete",message:`User ${D} successful`}),w();else throw new Error(`Failed to ${D} user`)}catch(T){s({type:"error",title:"Error",message:T.message})}},l=async(p,D)=>{try{const g={Authorization:`Bearer ${localStorage.getItem("chatflow_token")}`,"Content-Type":"application/json"};let u;switch(D){case"edit":console.log("Edit room:",p);return;case"delete":if(!window.confirm("Are you sure you want to delete this room?"))return;u=await fetch(`http://localhost:8000/api/admin/rooms/${p}/`,{method:"DELETE",headers:g});break;default:throw new Error(`Unknown action: ${D}`)}if(u&&u.ok)s({type:"success",title:"Action Complete",message:`Room ${D} successful`}),w();else throw new Error(`Failed to ${D} room`)}catch(T){s({type:"error",title:"Error",message:T.message})}},o=()=>t("div",{className:"space-y-6",children:[t("div",{className:"flex items-center justify-between",children:[t("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:["User Management (",c.length,")"]}),t("div",{className:"flex items-center gap-4",children:[t("div",{className:"relative",children:[e(ce,{className:"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),e("input",{type:"text",placeholder:"Search users...",value:E,onChange:p=>v(p.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),t("button",{onClick:()=>{s({id:Date.now(),type:"info",title:"Add User",message:"Add user functionality coming soon!"})},className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2",children:[e(Ne,{className:"w-4 h-4"}),"Add User"]})]})]}),e("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border overflow-hidden",children:e("div",{className:"overflow-x-auto",children:t("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[e("thead",{className:"bg-gray-50 dark:bg-gray-700",children:t("tr",{children:[e("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"User"}),e("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Status"}),e("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Type"}),e("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Joined"}),e("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Last Active"}),e("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Actions"})]})}),e("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:c.map(p=>t("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[e("td",{className:"px-6 py-4 whitespace-nowrap",children:t("div",{className:"flex items-center",children:[e("div",{className:"w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center",children:e("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:p.username[0].toUpperCase()})}),t("div",{className:"ml-4",children:[e("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:p.display_name||p.username}),e("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:p.email})]})]})}),e("td",{className:"px-6 py-4 whitespace-nowrap",children:e("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${p.is_active?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"}`,children:p.is_active?"Active":"Inactive"})}),e("td",{className:"px-6 py-4 whitespace-nowrap",children:t("div",{className:"flex items-center gap-2",children:[p.user_type==="admin"&&e(Se,{className:"w-4 h-4 text-red-500"}),p.is_vip&&e(G,{className:"w-4 h-4 text-purple-500"}),e("span",{className:"text-sm text-gray-900 dark:text-white capitalize",children:p.user_type})]})}),e("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:new Date(p.date_joined).toLocaleDateString()}),e("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:p.last_login?new Date(p.last_login).toLocaleDateString():"Never"}),e("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:t("div",{className:"flex items-center justify-end gap-2",children:[e("button",{onClick:()=>d(p.id,"edit"),className:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300",children:e(is,{className:"w-4 h-4"})}),e("button",{onClick:()=>d(p.id,p.is_active?"suspend":"activate"),className:`${p.is_active?"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300":"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"}`,children:p.is_active?e(de,{className:"w-4 h-4"}):e(Ee,{className:"w-4 h-4"})}),e("button",{onClick:()=>d(p.id,"delete"),className:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300",children:e(ht,{className:"w-4 h-4"})})]})})]},p.id))})]})})})]}),C=()=>t("div",{className:"space-y-6",children:[t("div",{className:"flex items-center justify-between",children:[t("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:["Room Management (",h.length,")"]}),t("button",{className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2",children:[e(Ne,{className:"w-4 h-4"}),"Create Room"]})]}),e("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:h.map(p=>t("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border p-6",children:[t("div",{className:"flex items-start justify-between mb-4",children:[t("div",{children:[e("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:p.name}),t("p",{className:"text-sm text-gray-600 dark:text-gray-400 capitalize",children:[p.room_type," • ",p.is_public?"Public":"Private"]})]}),t("div",{className:"flex items-center gap-2",children:[p.is_vip_only&&e(G,{className:"w-4 h-4 text-purple-500"}),e("button",{className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:e(it,{className:"w-4 h-4"})})]})]}),e("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2",children:p.description}),t("div",{className:"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4",children:[t("span",{children:[p.member_count," members"]}),t("span",{children:[p.message_count," messages"]})]}),t("div",{className:"flex items-center gap-2",children:[e("button",{onClick:()=>l(p.id,"edit"),className:"flex-1 bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-900/30 text-blue-600 dark:text-blue-400 px-3 py-2 rounded-lg text-sm font-medium transition-colors",children:"Edit"}),e("button",{onClick:()=>l(p.id,"delete"),className:"flex-1 bg-red-50 hover:bg-red-100 dark:bg-red-900/20 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400 px-3 py-2 rounded-lg text-sm font-medium transition-colors",children:"Delete"})]})]},p.id))})]});return j?e("div",{className:"min-h-screen flex items-center justify-center",children:e("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"})}):t("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[e("div",{className:"bg-white dark:bg-gray-800 shadow-sm border-b",children:e("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:t("div",{className:"flex items-center justify-between h-16",children:[t("div",{className:"flex items-center gap-3",children:[e(Se,{className:"w-8 h-8 text-red-600"}),e("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Admin Dashboard"})]}),e("div",{className:"flex items-center gap-4",children:t("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Welcome, ",(a==null?void 0:a.display_name)||(a==null?void 0:a.username)]})})]})})}),e("div",{className:"bg-white dark:bg-gray-800 border-b",children:e("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e("nav",{className:"flex space-x-8",children:[{id:"users",label:"Users",icon:K},{id:"rooms",label:"Rooms",icon:q},{id:"queue",label:"Agent Queue",icon:Lt},{id:"bots",label:"Bots",icon:Le},{id:"reports",label:"Reports",icon:nt},{id:"settings",label:"Settings",icon:Te}].map(p=>t("button",{onClick:()=>m(p.id),className:`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${i===p.id?"border-red-500 text-red-600 dark:text-red-400":"border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"}`,children:[e(p.icon,{className:"w-4 h-4"}),p.label]},p.id))})})}),t("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[i==="users"&&e(o,{}),i==="rooms"&&e(C,{}),i==="queue"&&e(Us,{user:a})]})]})},Ot=({size:a="md",className:s="",color:i="primary"})=>{const m={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8",xl:"h-12 w-12"},c={primary:"border-blue-600",white:"border-white",gray:"border-gray-600"};return e("div",{className:`animate-spin rounded-full border-2 border-t-transparent ${m[a]} ${c[i]} ${s}`,role:"status","aria-label":"Loading",children:e("span",{className:"sr-only",children:"Loading..."})})},Ae=({children:a,requireAdmin:s=!1})=>{const i=Ht(),{isAuthenticated:m,isLoading:c,user:n}=le();return c?e("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:e(Ot,{size:"lg"})}):m?s&&(!n||!n.is_staff)?e(_e,{to:"/chat",replace:!0}):a:e(_e,{to:"/login",state:{from:i},replace:!0})},Bs=()=>{const{notifications:a,removeNotification:s}=le(),[i,m]=r.useState(!1),[c,n]=r.useState("all"),h=b=>{const N="w-5 h-5";switch(b){case"message":return e(q,{className:`${N} text-blue-500`});case"user_joined":return e(K,{className:`${N} text-green-500`});case"vip":return e(G,{className:`${N} text-purple-500`});case"live":return e(ie,{className:`${N} text-red-500`});case"error":return e(nt,{className:`${N} text-red-500`});case"success":return e(Ee,{className:`${N} text-green-500`});case"warning":return e(nt,{className:`${N} text-yellow-500`});case"reaction":return e(Ze,{className:`${N} text-pink-500`});default:return e(jr,{className:`${N} text-blue-500`})}},y=b=>{switch(b){case"error":return"border-l-red-500 bg-red-50 dark:bg-red-900/20";case"success":return"border-l-green-500 bg-green-50 dark:bg-green-900/20";case"warning":return"border-l-yellow-500 bg-yellow-50 dark:bg-yellow-900/20";case"vip":return"border-l-purple-500 bg-purple-50 dark:bg-purple-900/20";case"live":return"border-l-red-500 bg-red-50 dark:bg-red-900/20";default:return"border-l-blue-500 bg-blue-50 dark:bg-blue-900/20"}},k=a.filter(b=>c==="all"?!0:b.type===c),x=a.filter(b=>!b.read).length,_=b=>{console.log("Mark as read:",b)},S=()=>{a.forEach(b=>{s(b.id)})};return t("div",{className:"relative",children:[t("button",{onClick:()=>m(!i),className:"relative p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",children:[e(rt,{className:"w-6 h-6"}),x>0&&e("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:x>9?"9+":x})]}),i&&t(xe,{children:[e("div",{className:"fixed inset-0 z-40",onClick:()=>m(!1)}),t("div",{className:"absolute right-0 top-full mt-2 w-96 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50 max-h-96 overflow-hidden",children:[t("div",{className:"p-4 border-b border-gray-200 dark:border-gray-700",children:[t("div",{className:"flex items-center justify-between",children:[e("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Notifications"}),t("div",{className:"flex items-center gap-2",children:[a.length>0&&e("button",{onClick:S,className:"text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300",children:"Clear all"}),e("button",{onClick:()=>m(!1),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:e(de,{className:"w-5 h-5"})})]})]}),e("div",{className:"flex gap-2 mt-3",children:[{id:"all",label:"All"},{id:"message",label:"Messages"},{id:"live",label:"Live"},{id:"vip",label:"VIP"}].map(b=>e("button",{onClick:()=>n(b.id),className:`px-3 py-1 rounded-full text-xs font-medium transition-colors ${c===b.id?"bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300":"bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600"}`,children:b.label},b.id))})]}),e("div",{className:"max-h-80 overflow-y-auto",children:k.length>0?e("div",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:k.map(b=>e("div",{className:`p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border-l-4 ${y(b.type)}`,children:t("div",{className:"flex items-start gap-3",children:[e("div",{className:"flex-shrink-0 mt-0.5",children:h(b.type)}),e("div",{className:"flex-1 min-w-0",children:t("div",{className:"flex items-start justify-between",children:[t("div",{className:"flex-1",children:[e("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:b.title}),b.message&&e("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:b.message}),e("p",{className:"text-xs text-gray-500 dark:text-gray-500 mt-2",children:new Date(b.timestamp).toLocaleString()})]}),t("div",{className:"flex items-center gap-1 ml-2",children:[!b.read&&e("button",{onClick:()=>_(b.id),className:"text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300",title:"Mark as read",children:e(Ee,{className:"w-4 h-4"})}),e("button",{onClick:()=>s(b.id),className:"text-gray-400 hover:text-red-500 dark:hover:text-red-400",title:"Remove",children:e(de,{className:"w-4 h-4"})})]})]})})]})},b.id))}):t("div",{className:"p-8 text-center",children:[e(rt,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),e("h3",{className:"text-sm font-medium text-gray-900 dark:text-white mb-1",children:"No notifications"}),e("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:c==="all"?"You're all caught up!":`No ${c} notifications`})]})}),a.length>0&&e("div",{className:"p-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700",children:e("button",{className:"w-full text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium",children:"View all notifications"})})]})]})]})},qs=({call:a,onClose:s})=>{var O,F,H,Q,Z,te,me,ae;const{user:i,socket:m,addNotification:c}=le(),[n,h]=r.useState(!1),[y,k]=r.useState(!1),[x,_]=r.useState((a==null?void 0:a.callType)==="video"),[S,b]=r.useState(!1);r.useState([]);const[N,j]=r.useState(0),[M,E]=r.useState(!1);r.useState(100);const v=r.useRef(null),w=r.useRef(null),d=r.useRef(null),l=r.useRef(null),o=r.useRef(null);r.useEffect(()=>{if((a==null?void 0:a.type)==="incoming"){const f=setTimeout(()=>{c({type:"warning",title:"Missed Call",message:`Missed call from ${a.caller.display_name||a.caller.username}`}),s()},3e4);return()=>clearTimeout(f)}},[a]),r.useEffect(()=>{let f;return n&&o.current&&(f=setInterval(()=>{const I=Math.floor((Date.now()-o.current)/1e3);j(I)},1e3)),()=>clearInterval(f)},[n]);const C=async()=>{try{const f=await navigator.mediaDevices.getUserMedia({video:x,audio:!0});d.current=f,v.current&&(v.current.srcObject=f);const I=new RTCPeerConnection({iceServers:[{urls:"stun:stun.l.google.com:19302"}]});l.current=I,f.getTracks().forEach(A=>{I.addTrack(A,f)}),I.ontrack=A=>{w.current&&(w.current.srcObject=A.streams[0])},I.onicecandidate=A=>{A.candidate&&m&&m.send(JSON.stringify({type:"ice_candidate",candidate:A.candidate,room_id:a.roomId}))},h(!0),o.current=Date.now(),m&&m.send(JSON.stringify({type:"call_start",room_id:a.roomId,call_type:a.callType}))}catch(f){console.error("Failed to initialize call:",f),c({type:"error",title:"Call Failed",message:"Failed to access camera/microphone"}),s()}},p=()=>{C()},D=()=>{m&&m.send(JSON.stringify({type:"call_decline",room_id:a.roomId})),s()},T=()=>{d.current&&d.current.getTracks().forEach(f=>f.stop()),l.current&&l.current.close(),m&&m.send(JSON.stringify({type:"call_end",room_id:a.roomId})),s()},g=()=>{if(d.current){const f=d.current.getAudioTracks()[0];f&&(f.enabled=y,k(!y))}},u=()=>{if(d.current){const f=d.current.getVideoTracks()[0];f&&(f.enabled=!x,_(!x))}},$=async()=>{try{const f=await navigator.mediaDevices.getDisplayMedia({video:!0,audio:!0});if(l.current&&d.current){const I=f.getVideoTracks()[0],A=l.current.getSenders().find(B=>B.track&&B.track.kind==="video");A&&await A.replaceTrack(I)}b(!0),f.getVideoTracks()[0].onended=()=>{if(b(!1),d.current){const I=d.current.getVideoTracks()[0],A=l.current.getSenders().find(B=>B.track&&B.track.kind==="video");A&&I&&A.replaceTrack(I)}}}catch(f){console.error("Failed to start screen share:",f),c({type:"error",title:"Screen Share Failed",message:"Failed to start screen sharing"})}},V=f=>{const I=Math.floor(f/60),A=f%60;return`${I.toString().padStart(2,"0")}:${A.toString().padStart(2,"0")}`};return M?e("div",{className:"fixed bottom-4 right-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg border p-4 z-50 w-64",children:t("div",{className:"flex items-center justify-between",children:[t("div",{className:"flex items-center gap-3",children:[e("div",{className:"w-10 h-10 bg-green-500 rounded-full flex items-center justify-center",children:e(Pe,{className:"w-5 h-5 text-white"})}),t("div",{children:[e("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:((O=a==null?void 0:a.caller)==null?void 0:O.display_name)||((F=a==null?void 0:a.caller)==null?void 0:F.username)||"Call in progress"}),e("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:V(N)})]})]}),t("div",{className:"flex items-center gap-2",children:[e("button",{onClick:()=>E(!1),className:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:e(Oe,{className:"w-4 h-4"})}),e("button",{onClick:T,className:"p-1 text-red-500 hover:text-red-600",children:e(et,{className:"w-4 h-4"})})]})]})}):e("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:t("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl h-full max-h-[80vh] overflow-hidden",children:[t("div",{className:"p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between",children:[t("div",{className:"flex items-center gap-3",children:[e("div",{className:"w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center",children:e("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:((Q=(H=a==null?void 0:a.caller)==null?void 0:H.display_name)==null?void 0:Q[0])||((te=(Z=a==null?void 0:a.caller)==null?void 0:Z.username)==null?void 0:te[0])||"U"})}),t("div",{children:[e("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:((me=a==null?void 0:a.caller)==null?void 0:me.display_name)||((ae=a==null?void 0:a.caller)==null?void 0:ae.username)||"Unknown"}),e("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:n?V(N):"Connecting..."})]})]}),e("div",{className:"flex items-center gap-2",children:e("button",{onClick:()=>E(!0),className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:e(Ve,{className:"w-5 h-5"})})})]}),t("div",{className:"flex-1 bg-gray-900 relative",children:[e("video",{ref:w,autoPlay:!0,playsInline:!0,className:"w-full h-full object-cover"}),x&&e("div",{className:"absolute top-4 right-4 w-48 h-36 bg-gray-800 rounded-lg overflow-hidden",children:e("video",{ref:v,autoPlay:!0,playsInline:!0,muted:!0,className:"w-full h-full object-cover"})}),!n&&(a==null?void 0:a.type)==="incoming"&&e("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center",children:t("div",{className:"text-center text-white",children:[e("div",{className:"w-24 h-24 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse",children:e(Pe,{className:"w-12 h-12"})}),e("h3",{className:"text-xl font-semibold mb-2",children:"Incoming Call"}),t("p",{className:"text-gray-300 mb-8",children:[a.caller.display_name||a.caller.username," is calling..."]}),t("div",{className:"flex items-center justify-center gap-4",children:[e("button",{onClick:D,className:"w-16 h-16 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center transition-colors",children:e(et,{className:"w-8 h-8 text-white"})}),e("button",{onClick:p,className:"w-16 h-16 bg-green-500 hover:bg-green-600 rounded-full flex items-center justify-center transition-colors",children:e(Pe,{className:"w-8 h-8 text-white"})})]})]})})]}),n&&t("div",{className:"p-4 bg-gray-50 dark:bg-gray-700 flex items-center justify-center gap-4",children:[e("button",{onClick:g,className:`p-3 rounded-full transition-colors ${y?"bg-red-500 hover:bg-red-600 text-white":"bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300"}`,children:y?e(Or,{className:"w-5 h-5"}):e(Je,{className:"w-5 h-5"})}),e("button",{onClick:u,className:`p-3 rounded-full transition-colors ${x?"bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300":"bg-red-500 hover:bg-red-600 text-white"}`,children:x?e(ut,{className:"w-5 h-5"}):e(Ns,{className:"w-5 h-5"})}),e("button",{onClick:$,className:`p-3 rounded-full transition-colors ${S?"bg-blue-500 hover:bg-blue-600 text-white":"bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300"}`,children:e(Ge,{className:"w-5 h-5"})}),e("button",{onClick:T,className:"p-3 bg-red-500 hover:bg-red-600 text-white rounded-full transition-colors",children:e(et,{className:"w-5 h-5"})})]})]})})},Ws=({screenShare:a,onClose:s})=>{const{user:i,socket:m,addNotification:c}=le(),[n,h]=r.useState(!1),[y,k]=r.useState(!1),[x,_]=r.useState(!1),[S,b]=r.useState(!1),[N,j]=r.useState([]);r.useState("high"),r.useState(!0);const M=r.useRef(null),E=r.useRef(null),v=r.useRef(null),w=r.useRef([]);r.useEffect(()=>{if(a!=null&&a.streamUrl&&M.current&&console.log("Connecting to screen share stream:",a.streamUrl),m){const T=g=>{const u=JSON.parse(g.data);switch(u.type){case"screen_share_ended":c({type:"info",title:"Screen Share Ended",message:`${a.sharedBy.display_name||a.sharedBy.username} stopped sharing`}),s();break;case"remote_control_granted":_(!0),c({type:"success",title:"Remote Control Granted",message:"You can now control the shared screen"});break;case"remote_control_revoked":_(!1),c({type:"info",title:"Remote Control Revoked",message:"Remote control access has been removed"});break;case"viewers_updated":j(u.viewers||[]);break}};return m.addEventListener("message",T),()=>m.removeEventListener("message",T)}},[a,m]);const d=()=>{m&&m.send(JSON.stringify({type:"request_remote_control",room_id:a.roomId}))},l=async()=>{try{if(!M.current)return;const T=M.current.captureStream(),g=new MediaRecorder(T,{mimeType:"video/webm;codecs=vp9"});v.current=g,w.current=[],g.ondataavailable=u=>{u.data.size>0&&w.current.push(u.data)},g.onstop=()=>{const u=new Blob(w.current,{type:"video/webm"}),$=URL.createObjectURL(u),V=document.createElement("a");V.href=$,V.download=`screen-share-${Date.now()}.webm`,document.body.appendChild(V),V.click(),document.body.removeChild(V),URL.revokeObjectURL($),c({type:"success",title:"Recording Saved",message:"Screen share recording has been downloaded"})},g.start(),b(!0)}catch(T){console.error("Failed to start recording:",T),c({type:"error",title:"Recording Failed",message:"Failed to start screen recording"})}},o=()=>{v.current&&S&&(v.current.stop(),b(!1))},C=()=>{if(!M.current||!E.current)return;const T=E.current,g=M.current,u=T.getContext("2d");T.width=g.videoWidth,T.height=g.videoHeight,u.drawImage(g,0,0),T.toBlob($=>{const V=URL.createObjectURL($),O=document.createElement("a");O.href=V,O.download=`screenshot-${Date.now()}.png`,document.body.appendChild(O),O.click(),document.body.removeChild(O),URL.revokeObjectURL(V),c({type:"success",title:"Screenshot Saved",message:"Screenshot has been downloaded"})},"image/png")},p=T=>{if(!x||!m)return;const g=M.current.getBoundingClientRect(),u=(T.clientX-g.left)/g.width,$=(T.clientY-g.top)/g.height;m.send(JSON.stringify({type:"remote_mouse_event",room_id:a.roomId,event_type:T.type,x:u,y:$,button:T.button}))},D=T=>{!x||!m||m.send(JSON.stringify({type:"remote_key_event",room_id:a.roomId,event_type:T.type,key:T.key,code:T.code,ctrlKey:T.ctrlKey,shiftKey:T.shiftKey,altKey:T.altKey}))};return y?e("div",{className:"fixed bottom-4 left-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg border p-4 z-50 w-64",children:t("div",{className:"flex items-center justify-between",children:[t("div",{className:"flex items-center gap-3",children:[e("div",{className:"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center",children:e(Ge,{className:"w-5 h-5 text-white"})}),t("div",{children:[e("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Screen Share"}),e("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:a.sharedBy.display_name||a.sharedBy.username})]})]}),t("div",{className:"flex items-center gap-2",children:[e("button",{onClick:()=>k(!1),className:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:e(Oe,{className:"w-4 h-4"})}),e("button",{onClick:s,className:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:e(de,{className:"w-4 h-4"})})]})]})}):e("div",{className:`fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 ${n?"p-0":"p-4"}`,children:t("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden ${n?"w-full h-full":"w-full max-w-6xl h-full max-h-[90vh]"}`,children:[!n&&t("div",{className:"p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between",children:[t("div",{className:"flex items-center gap-3",children:[e("div",{className:"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center",children:e(Ge,{className:"w-5 h-5 text-white"})}),t("div",{children:[e("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Screen Share"}),t("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Shared by ",a.sharedBy.display_name||a.sharedBy.username]})]})]}),t("div",{className:"flex items-center gap-2",children:[t("div",{className:"flex items-center gap-1 text-sm text-gray-500 dark:text-gray-400",children:[e(ct,{className:"w-4 h-4"}),N.length]}),e("button",{onClick:C,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",title:"Take Screenshot",children:e(st,{className:"w-5 h-5"})}),!x&&e("button",{onClick:d,className:"p-2 text-gray-400 hover:text-blue-500",title:"Request Remote Control",children:e(wt,{className:"w-5 h-5"})}),e("button",{onClick:S?o:l,className:`p-2 transition-colors ${S?"text-red-500 hover:text-red-600":"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"}`,title:S?"Stop Recording":"Start Recording",children:e("div",{className:`w-5 h-5 rounded ${S?"bg-red-500":"border-2 border-current"}`})}),e("button",{onClick:()=>h(!0),className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",title:"Fullscreen",children:e(Oe,{className:"w-5 h-5"})}),e("button",{onClick:()=>k(!0),className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",title:"Minimize",children:e(Ve,{className:"w-5 h-5"})}),e("button",{onClick:s,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:e(de,{className:"w-5 h-5"})})]})]}),t("div",{className:"flex-1 bg-black relative",children:[e("video",{ref:M,autoPlay:!0,playsInline:!0,className:"w-full h-full object-contain",onMouseDown:p,onMouseUp:p,onMouseMove:p,onClick:p,onKeyDown:D,onKeyUp:D,tabIndex:x?0:-1,style:{cursor:x?"crosshair":"default"}}),x&&t("div",{className:"absolute top-4 left-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center gap-2",children:[e(wt,{className:"w-4 h-4"}),"Remote Control Active"]}),S&&t("div",{className:"absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center gap-2 animate-pulse",children:[e("div",{className:"w-2 h-2 bg-white rounded-full"}),"Recording"]}),n&&e("button",{onClick:()=>h(!1),className:"absolute top-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded-lg hover:bg-opacity-70 transition-colors",children:e(Ve,{className:"w-5 h-5"})})]}),e("canvas",{ref:E,style:{display:"none"}})]})})},Js=({isOpen:a,onClose:s,onRegister:i})=>{const[m,c]=r.useState([]),[n,h]=r.useState(""),[y,k]=r.useState(""),[x,_]=r.useState(!1),[S,b]=r.useState(!1),[N,j]=r.useState(!1),[M]=r.useState(()=>`guest_${Math.random().toString(36).substr(2,9)}`);r.useEffect(()=>{if(a&&!x){const d=["Guest","Visitor","User","Anonymous"],l=d[Math.floor(Math.random()*d.length)],o=Math.floor(Math.random()*9999)+1;k(`${l}${o}`)}},[a,x]);const E=()=>{if(y.trim()){_(!0),j(!0);const d={id:Date.now(),content:`Welcome ${y}! You're chatting as a guest. To access all features, consider creating an account.`,sender:{username:"System",isSystem:!0},timestamp:new Date,type:"system"};c([d])}},v=d=>{if(d.preventDefault(),!n.trim()||!N)return;const l={id:Date.now(),content:n.trim(),sender:{id:M,username:y,isGuest:!0},timestamp:new Date,type:"text"};c(o=>[...o,l]),h(""),setTimeout(()=>{const o={id:Date.now()+1,content:"Thanks for your message! As a guest user, you have limited features. Would you like to create an account for the full experience?",sender:{username:"Support Bot",isBot:!0},timestamp:new Date,type:"bot",actions:[{text:"Create Account",action:"register"},{text:"Continue as Guest",action:"continue"}]};c(C=>[...C,o])},1e3)},w=d=>{d==="register"&&i()};return a?t("div",{className:`fixed bottom-4 right-4 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50 transition-all duration-200 ${S?"h-14":"h-96"}`,children:[t("div",{className:"flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 bg-blue-500 text-white rounded-t-lg",children:[t("div",{className:"flex items-center space-x-2",children:[e(q,{className:"w-5 h-5"}),e("span",{className:"font-medium",children:"Guest Chat"}),e("span",{className:"text-xs bg-blue-600 px-2 py-1 rounded-full",children:"Guest"})]}),t("div",{className:"flex items-center space-x-1",children:[e("button",{onClick:()=>b(!S),className:"p-1 hover:bg-blue-600 rounded",children:S?e(Oe,{className:"w-4 h-4"}):e(Ve,{className:"w-4 h-4"})}),e("button",{onClick:s,className:"p-1 hover:bg-blue-600 rounded",children:e(de,{className:"w-4 h-4"})})]})]}),!S&&e(xe,{children:x?t(xe,{children:[e("div",{className:"flex-1 overflow-y-auto p-3 space-y-3 h-64",children:m.map(d=>{var l,o,C,p,D;return e("div",{className:`flex ${(l=d.sender)!=null&&l.isGuest?"justify-end":"justify-start"}`,children:t("div",{className:`max-w-xs px-3 py-2 rounded-lg ${(o=d.sender)!=null&&o.isGuest?"bg-blue-500 text-white":d.type==="system"?"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200":(C=d.sender)!=null&&C.isBot?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white"}`,children:[!((p=d.sender)!=null&&p.isGuest)&&e("div",{className:"text-xs font-medium mb-1 opacity-75",children:(D=d.sender)==null?void 0:D.username}),e("p",{className:"text-sm whitespace-pre-wrap",children:d.content}),d.actions&&e("div",{className:"mt-2 space-y-1",children:d.actions.map((T,g)=>e("button",{onClick:()=>w(T.action),className:"block w-full text-left text-xs bg-white/20 hover:bg-white/30 px-2 py-1 rounded",children:T.text},g))}),e("div",{className:"text-xs opacity-70 mt-1",children:d.timestamp.toLocaleTimeString()})]})},d.id)})}),t("div",{className:"p-3 border-t border-gray-200 dark:border-gray-700",children:[t("form",{onSubmit:v,className:"flex space-x-2",children:[e("input",{type:"text",value:n,onChange:d=>h(d.target.value),placeholder:"Type your message...",className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),e("button",{type:"submit",disabled:!n.trim(),className:"px-3 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors",children:e(mt,{className:"w-4 h-4"})})]}),e("div",{className:"mt-2 text-center",children:e("button",{onClick:i,className:"text-xs text-blue-600 dark:text-blue-400 hover:underline",children:"Upgrade to full account for more features"})})]})]}):t("div",{className:"p-4 space-y-4",children:[t("div",{className:"text-center",children:[e(fs,{className:"w-12 h-12 text-gray-400 mx-auto mb-3"}),e("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Welcome to Guest Chat"}),e("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4",children:"Enter your name to start chatting. No registration required!"})]}),t("div",{className:"space-y-3",children:[t("div",{children:[e("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Your Name"}),e("input",{type:"text",value:y,onChange:d=>k(d.target.value),placeholder:"Enter your name...",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent",onKeyPress:d=>d.key==="Enter"&&E(),autoFocus:!0})]}),e("button",{onClick:E,disabled:!y.trim(),className:"w-full px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors",children:"Start Chatting"}),e("div",{className:"text-center",children:t("button",{onClick:i,className:"text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center justify-center space-x-1",children:[e(xs,{className:"w-4 h-4"}),e("span",{children:"Create Account Instead"})]})})]}),t("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3",children:[e("h4",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1",children:"Guest Limitations"}),t("ul",{className:"text-xs text-yellow-700 dark:text-yellow-300 space-y-1",children:[e("li",{children:"• Limited to public rooms only"}),e("li",{children:"• No file uploads or voice messages"}),e("li",{children:"• Chat history not saved"}),e("li",{children:"• No VIP features"})]})]})]})})]}):null},Gs=({onRegister:a,onLogin:s})=>{const[i,m]=r.useState(!1),[c,n]=r.useState(!1);r.useEffect(()=>{const y=setTimeout(()=>{i||n(!0)},1e4);return()=>clearTimeout(y)},[i]);const h=()=>{m(!0),n(!1)};return i?e(Js,{isOpen:i,onClose:()=>m(!1),onRegister:a}):t("div",{className:"fixed bottom-4 right-4 z-50",children:[c&&e("div",{className:"mb-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-3 max-w-xs",children:t("div",{className:"flex items-start space-x-2",children:[e("div",{className:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0",children:e(q,{className:"w-4 h-4 text-white"})}),t("div",{className:"flex-1",children:[e("p",{className:"text-sm text-gray-900 dark:text-white",children:"👋 Hi there! Need help? Start a quick chat with us!"}),t("div",{className:"flex space-x-2 mt-2",children:[e("button",{onClick:h,className:"text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded",children:"Start Chat"}),e("button",{onClick:()=>n(!1),className:"text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200",children:"Dismiss"})]})]})]})}),t("button",{onClick:h,className:"w-14 h-14 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-200 hover:scale-110 relative",children:[e(q,{className:"w-6 h-6"}),c&&e("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center",children:e("span",{className:"text-xs text-white",children:"1"})})]})]})},Vt=r.createContext(),le=()=>{const a=r.useContext(Vt);if(!a)throw new Error("useApp must be used within AppProvider");return a};function Ks(){const[a,s]=r.useState(null),[i,m]=r.useState(!1),[c,n]=r.useState(!0),[h,y]=r.useState("light"),[k,x]=r.useState([]),[_,S]=r.useState(null),[b,N]=r.useState(null),[j,M]=r.useState([]),[E,v]=r.useState({}),[w,d]=r.useState({}),[l,o]=r.useState(null),[C,p]=r.useState(!1);r.useEffect(()=>{D()},[]);const D=async()=>{try{const f=localStorage.getItem("chatflow_user"),I=localStorage.getItem("chatflow_token");if(f&&I){const B=JSON.parse(f);s(B),m(!0),await T(I)}const A=localStorage.getItem("chatflow_theme")||"light";y(A),document.documentElement.classList.toggle("dark",A==="dark")}catch(f){console.error("Failed to initialize app:",f)}finally{n(!1)}},T=async f=>{try{console.log("WebSocket connection disabled - will implement room-specific connections"),p(!1);return}catch(I){console.error("Failed to connect WebSocket:",I)}},g=f=>{switch(f.type){case"chat_message":f.message&&(d(I=>({...I,[f.message.room]:(I[f.message.room]||0)+1})),F({type:"message",title:`New message from ${f.message.sender.display_name||f.message.sender.username}`,message:f.message.content,roomId:f.message.room}));break;case"user_joined":f.user&&!j.find(I=>I.id===f.user.id)&&M(I=>[...I,f.user]);break;case"user_left":f.user_id&&M(I=>I.filter(A=>A.id!==f.user_id));break;case"typing_start":f.user&&f.room_id&&v(I=>({...I,[f.room_id]:[...I[f.room_id]||[],f.user]}));break;case"typing_stop":f.user_id&&f.room_id&&v(I=>({...I,[f.room_id]:(I[f.room_id]||[]).filter(A=>A.id!==f.user_id)}));break;case"voice_call_incoming":S({type:"incoming",caller:f.caller,roomId:f.room_id,callType:f.call_type});break;case"screen_share_started":N({sharedBy:f.shared_by,roomId:f.room_id,streamUrl:f.stream_url});break;case"notification":F({type:f.notification_type||"info",title:f.title,message:f.message});break;default:console.log("Unknown WebSocket message type:",f.type)}},u=async f=>{try{n(!0);try{const I=await fetch("http://localhost:8000/api/auth/login/",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:f.email,password:f.password})});if(I.ok){const A=await I.json();return localStorage.setItem("chatflow_user",JSON.stringify(A.user)),localStorage.setItem("chatflow_token",A.tokens.access),localStorage.setItem("chatflow_refresh_token",A.tokens.refresh),s(A.user),m(!0),await T(A.tokens.access),F({type:"success",title:"Welcome!",message:A.message||"Successfully logged in"}),!0}else{const A=await I.json();throw new Error(A.error||"Login failed")}}catch{console.log("Backend not available, using mock authentication");const A={id:1,username:f.email.split("@")[0],email:f.email,display_name:f.email.split("@")[0],is_staff:f.email==="<EMAIL>",is_vip:f.email.includes("vip"),avatar:null,date_joined:new Date().toISOString(),last_login:new Date().toISOString()},B="mock_token_"+Date.now();return localStorage.setItem("chatflow_user",JSON.stringify(A)),localStorage.setItem("chatflow_token",B),s(A),m(!0),F({type:"success",title:"Welcome!",message:"Successfully logged in (demo mode)"}),!0}}catch(I){return console.error("Login error:",I),F({type:"error",title:"Login Failed",message:I.message}),!1}finally{n(!1)}},$=async f=>{try{if(n(!0),f.password!==f.confirmPassword)throw new Error("Passwords do not match");try{const I=await fetch("http://localhost:8000/api/auth/register/",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:f.username,email:f.email,password:f.password,display_name:f.displayName})});if(I.ok){const A=await I.json();return localStorage.setItem("chatflow_user",JSON.stringify(A.user)),localStorage.setItem("chatflow_token",A.tokens.access),localStorage.setItem("chatflow_refresh_token",A.tokens.refresh),s(A.user),m(!0),await T(A.tokens.access),F({type:"success",title:"Welcome!",message:A.message||"Account created successfully"}),!0}else{const A=await I.json();throw new Error(A.error||"Registration failed")}}catch{console.log("Backend not available, using mock registration");const A={id:Date.now(),username:f.username,email:f.email,display_name:f.displayName||f.username,is_staff:!1,is_vip:!1,avatar:null,date_joined:new Date().toISOString(),last_login:new Date().toISOString()},B="mock_token_"+Date.now();return localStorage.setItem("chatflow_user",JSON.stringify(A)),localStorage.setItem("chatflow_token",B),s(A),m(!0),F({type:"success",title:"Welcome!",message:"Account created successfully (demo mode)"}),!0}}catch(I){return console.error("Registration error:",I),F({type:"error",title:"Registration Failed",message:I.message}),!1}finally{n(!1)}},V=async()=>{try{const f=localStorage.getItem("chatflow_token"),I=localStorage.getItem("chatflow_refresh_token");if(f)try{await fetch("http://localhost:8000/api/auth/logout/",{method:"POST",headers:{Authorization:`Bearer ${f}`,"Content-Type":"application/json"},body:JSON.stringify({refresh_token:I})})}catch{console.log("Backend logout failed, continuing with local logout")}l&&l.close(),localStorage.removeItem("chatflow_user"),localStorage.removeItem("chatflow_token"),localStorage.removeItem("chatflow_refresh_token"),s(null),m(!1),o(null),p(!1),M([]),v({}),d({}),S(null),N(null),F({type:"success",title:"Logged Out",message:"You have been successfully logged out"})}catch(f){console.error("Logout error:",f),F({type:"error",title:"Logout Error",message:"There was an issue logging out"})}},O=()=>{const f=h==="light"?"dark":"light";y(f),localStorage.setItem("chatflow_theme",f),document.documentElement.classList.toggle("dark",f==="dark")},F=f=>{const I=Date.now().toString(),A={id:I,...f,timestamp:new Date};x(B=>[A,...B.slice(0,49)]),f.persistent||setTimeout(()=>{H(I)},5e3)},H=f=>{x(I=>I.filter(A=>A.id!==f))},ae={user:a,isAuthenticated:i,isLoading:c,theme:h,toggleTheme:O,socket:l,isConnected:C,onlineUsers:j,typingUsers:E,unreadCounts:w,notifications:k,addNotification:F,removeNotification:H,activeCall:_,setActiveCall:S,screenShare:b,setScreenShare:N,login:u,register:$,logout:V,sendMessage:(f,I,A={})=>{l&&C&&l.send(JSON.stringify({type:"chat_message",room_id:f,content:I,...A}))},startTyping:f=>{l&&C&&l.send(JSON.stringify({type:"typing_start",room_id:f}))},stopTyping:f=>{l&&C&&l.send(JSON.stringify({type:"typing_stop",room_id:f}))},markAsRead:(f,I)=>{l&&C&&(l.send(JSON.stringify({type:"mark_read",room_id:f,message_id:I})),d(A=>({...A,[f]:Math.max(0,(A[f]||0)-1)})))}};return c?e("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:t("div",{className:"text-center",children:[e(Ot,{size:"lg"}),e("p",{className:"mt-4 text-gray-600",children:"Loading ChatFlow Pro..."})]})}):e(Vt.Provider,{value:ae,children:e(Bt,{children:e("div",{className:`min-h-screen ${h==="dark"?"dark":""}`,children:t("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors",children:[t(qt,{children:[e(ge,{path:"/",element:i?e(_e,{to:"/chat",replace:!0}):e(_e,{to:"/login",replace:!0})}),e(ge,{path:"/login",element:i?e(_e,{to:"/chat",replace:!0}):e(Ua,{})}),e(ge,{path:"/register",element:i?e(_e,{to:"/chat",replace:!0}):e(Ha,{})}),e(ge,{path:"/chat/*",element:e(Ae,{children:e(Os,{})})}),e(ge,{path:"/vip",element:e(Ae,{children:e(Vs,{})})}),e(ge,{path:"/live",element:e(Ae,{children:e(zs,{})})}),e(ge,{path:"/analytics",element:e(Ae,{requireAdmin:!0,children:e(Fs,{})})}),e(ge,{path:"/admin",element:e(Ae,{requireAdmin:!0,children:e(Hs,{})})}),e(ge,{path:"*",element:e(_e,{to:"/",replace:!0})})]}),e(Bs,{}),_&&e(qs,{call:_,onClose:()=>S(null)}),b&&e(Ws,{screenShare:b,onClose:()=>N(null)}),!i&&e(Gs,{onRegister:()=>window.location.href="/register",onLogin:()=>window.location.href="/login"}),e(Fa,{position:"top-right",toastOptions:{duration:4e3,className:"dark:bg-gray-800 dark:text-white",style:{background:h==="dark"?"#1f2937":"#ffffff",color:h==="dark"?"#ffffff":"#1f2937"}}})]})})})})}Mt(document.getElementById("root")).render(e(r.StrictMode,{children:e(Ks,{})}));
//# sourceMappingURL=index-8e5ce683.js.map
