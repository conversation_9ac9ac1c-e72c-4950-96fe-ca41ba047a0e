from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView
from django.contrib.auth import authenticate, get_user_model
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from django.core.mail import send_mail
from django.conf import settings
from django.utils.crypto import get_random_string
from django.utils import timezone
from django.db.models import Q
from datetime import timedelta

User = get_user_model()


class RegisterView(APIView):
    """
    User registration endpoint
    """
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        username = request.data.get('username')
        email = request.data.get('email')
        password = request.data.get('password')
        # Accept both 'display_name' and 'displayName' for frontend compatibility
        display_name = request.data.get('display_name') or request.data.get('displayName', '')

        if not username or not email or not password:
            return Response({
                'error': 'Username, email, and password are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if user exists
        if User.objects.filter(username=username).exists():
            return Response({
                'error': 'Username already exists'
            }, status=status.HTTP_400_BAD_REQUEST)

        if User.objects.filter(email=email).exists():
            return Response({
                'error': 'Email already exists'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Validate password
            validate_password(password)

            # Create user
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password,
                display_name=display_name
            )

            # Generate tokens
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token

            return Response({
                'message': 'User registered successfully',
                'user': {
                    'id': str(user.id),
                    'username': user.username,
                    'email': user.email,
                    'display_name': user.display_name or user.username,
                    'is_vip': user.is_vip,
                    'user_type': user.user_type,
                    'is_staff': user.is_staff
                },
                'tokens': {
                    'access': str(access_token),
                    'refresh': str(refresh)
                }
            }, status=status.HTTP_201_CREATED)

        except ValidationError as e:
            return Response({
                'error': 'Password validation failed',
                'details': e.messages
            }, status=status.HTTP_400_BAD_REQUEST)


# Admin Views
class AdminUsersListView(APIView):
    """
    Admin endpoint to list all users
    """
    permission_classes = [permissions.IsAuthenticated, permissions.IsAdminUser]

    def get(self, request):
        users = User.objects.all().order_by('-date_joined')
        user_data = []

        for user in users:
            user_data.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'display_name': user.display_name,
                'is_active': user.is_active,
                'is_staff': user.is_staff,
                'is_vip': user.is_vip,
                'user_type': user.user_type,
                'date_joined': user.date_joined,
                'last_login': user.last_login,
                'is_online': user.is_online
            })

        return Response({
            'results': user_data,
            'count': len(user_data)
        })


class AdminUserDetailView(APIView):
    """
    Admin endpoint to get/update user details
    """
    permission_classes = [permissions.IsAuthenticated, permissions.IsAdminUser]

    def get(self, request, user_id):
        try:
            user = User.objects.get(id=user_id)
            return Response({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'display_name': user.display_name,
                'is_active': user.is_active,
                'is_staff': user.is_staff,
                'is_vip': user.is_vip,
                'user_type': user.user_type,
                'date_joined': user.date_joined,
                'last_login': user.last_login,
                'is_online': user.is_online
            })
        except User.DoesNotExist:
            return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)

    def delete(self, request, user_id):
        try:
            user = User.objects.get(id=user_id)
            user.delete()
            return Response({'message': 'User deleted successfully'})
        except User.DoesNotExist:
            return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)


class AdminSuspendUserView(APIView):
    """
    Admin endpoint to suspend a user
    """
    permission_classes = [permissions.IsAuthenticated, permissions.IsAdminUser]

    def post(self, request, user_id):
        try:
            user = User.objects.get(id=user_id)
            user.is_active = False
            user.save()
            return Response({'message': 'User suspended successfully'})
        except User.DoesNotExist:
            return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)


class AdminActivateUserView(APIView):
    """
    Admin endpoint to activate a user
    """
    permission_classes = [permissions.IsAuthenticated, permissions.IsAdminUser]

    def post(self, request, user_id):
        try:
            user = User.objects.get(id=user_id)
            user.is_active = True
            user.save()
            return Response({'message': 'User activated successfully'})
        except User.DoesNotExist:
            return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)


class LoginView(APIView):
    """
    User login endpoint
    """
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        # Accept both 'username' and 'email' fields for flexibility
        username = request.data.get('username') or request.data.get('email')
        password = request.data.get('password')

        if not username or not password:
            return Response({
                'error': 'Email/username and password are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Try to authenticate with username or email
        user = authenticate(request, username=username, password=password)

        if not user:
            # Try with email if username authentication failed
            try:
                user_obj = User.objects.get(email=username)
                user = authenticate(request, username=user_obj.username, password=password)
            except User.DoesNotExist:
                pass

        if user:
            if not user.is_active:
                return Response({
                    'error': 'Account is disabled'
                }, status=status.HTTP_401_UNAUTHORIZED)

            # Update last login and online status
            user.last_login = timezone.now()
            user.is_online = True
            user.save(update_fields=['last_login', 'is_online'])

            # Generate tokens
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token

            return Response({
                'message': 'Login successful',
                'user': {
                    'id': str(user.id),
                    'username': user.username,
                    'email': user.email,
                    'display_name': user.display_name or user.username,
                    'is_vip': user.is_vip,
                    'user_type': user.user_type,
                    'is_online': user.is_online,
                    'is_staff': user.is_staff
                },
                'tokens': {
                    'access': str(access_token),
                    'refresh': str(refresh)
                }
            }, status=status.HTTP_200_OK)

        return Response({
            'error': 'Invalid credentials'
        }, status=status.HTTP_401_UNAUTHORIZED)


class LogoutView(APIView):
    """
    User logout endpoint
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        try:
            refresh_token = request.data.get('refresh_token')
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()

            # Update user offline status
            request.user.is_online = False
            request.user.last_seen = timezone.now()
            request.user.save(update_fields=['is_online', 'last_seen'])

            return Response({
                'message': 'Logout successful'
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': 'Invalid token'
            }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_profile(request):
    """
    Get current user profile
    """
    user = request.user
    return Response({
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'display_name': user.display_name,
        'is_vip': user.is_vip,
        'user_type': user.user_type,
        'is_online': user.is_online,
        'last_seen': user.last_seen,
        'date_joined': user.date_joined
    })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def update_profile(request):
    """
    Update user profile
    """
    user = request.user
    display_name = request.data.get('display_name')

    if display_name is not None:
        user.display_name = display_name
        user.save(update_fields=['display_name'])

    return Response({
        'message': 'Profile updated successfully',
        'user': {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'display_name': user.display_name,
            'is_vip': user.is_vip,
            'user_type': user.user_type
        }
    })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def change_password(request):
    """
    Change user password
    """
    current_password = request.data.get('current_password')
    new_password = request.data.get('new_password')

    if not current_password or not new_password:
        return Response({
            'error': 'Current password and new password are required'
        }, status=status.HTTP_400_BAD_REQUEST)

    user = request.user

    if not user.check_password(current_password):
        return Response({
            'error': 'Current password is incorrect'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        validate_password(new_password, user)
        user.set_password(new_password)
        user.save()

        return Response({
            'message': 'Password changed successfully'
        }, status=status.HTTP_200_OK)

    except ValidationError as e:
        return Response({
            'error': 'Password validation failed',
            'details': e.messages
        }, status=status.HTTP_400_BAD_REQUEST)


# Admin Views
class AdminUsersListView(APIView):
    """
    Admin view to list all users
    """
    permission_classes = [permissions.IsAuthenticated, permissions.IsAdminUser]

    def get(self, request):
        try:
            users = User.objects.all().order_by('-date_joined')

            # Apply filters
            search = request.GET.get('search')
            if search:
                users = users.filter(
                    Q(username__icontains=search) |
                    Q(email__icontains=search) |
                    Q(display_name__icontains=search)
                )

            user_type = request.GET.get('user_type')
            if user_type:
                users = users.filter(user_type=user_type)

            is_active = request.GET.get('is_active')
            if is_active is not None:
                users = users.filter(is_active=is_active.lower() == 'true')

            # Pagination
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('page_size', 20))
            start = (page - 1) * page_size
            end = start + page_size

            total_count = users.count()
            users_page = users[start:end]

            users_data = []
            for user in users_page:
                users_data.append({
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'display_name': user.display_name,
                    'user_type': user.user_type,
                    'is_active': user.is_active,
                    'is_online': user.is_online,
                    'date_joined': user.date_joined,
                    'last_login': user.last_login,
                    'last_seen': user.last_seen,
                })

            return Response({
                'users': users_data,
                'total_count': total_count,
                'page': page,
                'page_size': page_size,
                'total_pages': (total_count + page_size - 1) // page_size
            })

        except Exception as e:
            return Response({
                'error': 'Failed to fetch users',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AdminUserDetailView(APIView):
    """
    Admin view to get user details
    """
    permission_classes = [permissions.IsAuthenticated, permissions.IsAdminUser]

    def get(self, request, user_id):
        try:
            user = User.objects.get(id=user_id)

            # Get user statistics
            from apps.chat.models import Message, ChatRoom
            message_count = Message.objects.filter(sender=user).count()
            rooms_count = ChatRoom.objects.filter(created_by=user).count()

            user_data = {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'display_name': user.display_name,
                'bio': user.bio,
                'user_type': user.user_type,
                'status': user.status,
                'is_active': user.is_active,
                'is_online': user.is_online,
                'is_vip': user.is_vip,
                'date_joined': user.date_joined,
                'last_login': user.last_login,
                'last_seen': user.last_seen,
                'avatar': user.avatar.url if user.avatar else None,
                'statistics': {
                    'message_count': message_count,
                    'rooms_created': rooms_count,
                }
            }

            return Response({'user': user_data})

        except User.DoesNotExist:
            return Response({
                'error': 'User not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'error': 'Failed to fetch user details',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def patch(self, request, user_id):
        """Update user details"""
        try:
            user = User.objects.get(id=user_id)

            # Update allowed fields
            allowed_fields = ['user_type', 'is_active', 'is_vip']
            for field in allowed_fields:
                if field in request.data:
                    setattr(user, field, request.data[field])

            user.save()

            return Response({
                'message': 'User updated successfully',
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'user_type': user.user_type,
                    'is_active': user.is_active,
                    'is_vip': user.is_vip,
                }
            })

        except User.DoesNotExist:
            return Response({
                'error': 'User not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'error': 'Failed to update user',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AdminSuspendUserView(APIView):
    """
    Admin view to suspend a user
    """
    permission_classes = [permissions.IsAuthenticated, permissions.IsAdminUser]

    def post(self, request, user_id):
        try:
            user = User.objects.get(id=user_id)

            if user.is_superuser:
                return Response({
                    'error': 'Cannot suspend superuser'
                }, status=status.HTTP_400_BAD_REQUEST)

            user.is_active = False
            user.save()

            return Response({
                'message': f'User {user.username} has been suspended'
            })

        except User.DoesNotExist:
            return Response({
                'error': 'User not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'error': 'Failed to suspend user',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AdminActivateUserView(APIView):
    """
    Admin view to activate a user
    """
    permission_classes = [permissions.IsAuthenticated, permissions.IsAdminUser]

    def post(self, request, user_id):
        try:
            user = User.objects.get(id=user_id)
            user.is_active = True
            user.save()

            return Response({
                'message': f'User {user.username} has been activated'
            })

        except User.DoesNotExist:
            return Response({
                'error': 'User not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'error': 'Failed to activate user',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
