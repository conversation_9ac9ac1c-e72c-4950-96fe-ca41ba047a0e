from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model

User = get_user_model()

class Command(BaseCommand):
    help = 'Create a test user for development'

    def handle(self, *args, **options):
        # Create test user if it doesn't exist
        email = '<EMAIL>'
        username = 'testuser'
        password = 'testpass123'
        
        if not User.objects.filter(email=email).exists():
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password,
                display_name='Test User',
                is_active=True
            )
            self.stdout.write(
                self.style.SUCCESS(f'Successfully created test user: {user.email}')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'Test user already exists: {email}')
            )
            
        # Create admin user if it doesn't exist
        admin_email = '<EMAIL>'
        admin_username = 'admin'
        admin_password = 'admin123'
        
        if not User.objects.filter(email=admin_email).exists():
            admin_user = User.objects.create_superuser(
                username=admin_username,
                email=admin_email,
                password=admin_password,
                display_name='Admin User'
            )
            self.stdout.write(
                self.style.SUCCESS(f'Successfully created admin user: {admin_user.email}')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'Admin user already exists: {admin_email}')
            )
