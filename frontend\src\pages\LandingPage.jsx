import React from 'react';
import { Link } from 'react-router-dom';

const LandingPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold">CF</span>
              </div>
              <span className="ml-2 text-2xl font-bold text-gray-900">ChatFlow Pro</span>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                to="/login"
                className="text-gray-600 hover:text-gray-900 font-medium transition-colors"
              >
                Sign In
              </Link>
              <Link
                to="/register"
                className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
              >
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            Modern Real-Time
            <span className="text-blue-600 block">Chat Platform</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Connect, collaborate, and communicate with your team and community through 
            our feature-rich chat platform. Built for businesses and communities seeking 
            interactive digital communication.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/register"
              className="bg-blue-600 hover:bg-blue-700 text-white font-medium text-lg px-8 py-3 rounded-lg transition-colors"
            >
              Start Chatting Free
            </Link>
            <button
              onClick={() => {
                // TODO: Implement demo video modal or redirect to demo
                alert('Demo video coming soon!');
              }}
              className="bg-gray-100 hover:bg-gray-200 text-gray-900 font-medium text-lg px-8 py-3 rounded-lg transition-colors"
            >
              Watch Demo
            </button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Everything you need for modern communication
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              From real-time messaging to live events, ChatFlow Pro provides all the tools 
              your team needs to stay connected and productive.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: 'Real-time Messaging',
                description: 'Instant messaging with typing indicators, read receipts, and emoji reactions.',
                icon: '💬'
              },
              {
                title: 'Group Chats & Channels',
                description: 'Create public channels, private groups, and organize conversations by topics.',
                icon: '👥'
              },
              {
                title: 'Live Rooms',
                description: 'Host live events, AMAs, and community sessions with real-time viewer count.',
                icon: '📺'
              },
              {
                title: 'VIP Experience',
                description: 'Exclusive VIP channels, priority support, and premium features for subscribers.',
                icon: '⭐'
              },
              {
                title: 'Enterprise Security',
                description: 'End-to-end encryption, audit logs, and compliance-ready security features.',
                icon: '🔒'
              },
              {
                title: 'AI-Powered',
                description: 'Smart chatbots, automated responses, and intelligent message suggestions.',
                icon: '🤖'
              },
            ].map((feature, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* VIP Section */}
      <section className="py-20 bg-gradient-to-r from-purple-500 to-purple-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="text-white">
            <h2 className="text-4xl font-bold mb-4">
              Unlock VIP Features
            </h2>
            <p className="text-xl mb-8 text-purple-100 max-w-2xl mx-auto">
              Get access to exclusive VIP channels, priority support, custom badges, 
              and early access to new features.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => {
                  // TODO: Implement VIP features modal or redirect to VIP page
                  alert('VIP features information coming soon!');
                }}
                className="bg-white text-purple-600 hover:bg-purple-50 font-medium py-3 px-8 rounded-lg transition-colors"
              >
                Learn More
              </button>
              <Link
                to="/vip"
                className="border-2 border-white text-white hover:bg-white hover:text-purple-600 font-medium py-3 px-8 rounded-lg transition-colors text-center"
              >
                Upgrade to VIP
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold text-white mb-4">
            Ready to transform your communication?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join thousands of teams and communities already using ChatFlow Pro 
            to stay connected and productive.
          </p>
          <Link
            to="/register"
            className="bg-white text-blue-600 hover:bg-blue-50 font-medium py-3 px-8 rounded-lg transition-colors text-lg"
          >
            Get Started for Free
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center mb-4">
                <div className="h-6 w-6 bg-blue-400 rounded flex items-center justify-center">
                  <span className="text-white text-sm font-bold">CF</span>
                </div>
                <span className="ml-2 text-lg font-bold">ChatFlow Pro</span>
              </div>
              <p className="text-gray-400">
                Modern real-time chat platform for teams and communities.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Product</h3>
              <ul className="space-y-2 text-gray-400">
                <li><button onClick={() => alert('Features page coming soon!')} className="hover:text-white transition-colors">Features</button></li>
                <li><button onClick={() => alert('Pricing page coming soon!')} className="hover:text-white transition-colors">Pricing</button></li>
                <li><button onClick={() => alert('API documentation coming soon!')} className="hover:text-white transition-colors">API</button></li>
                <li><button onClick={() => alert('Integrations page coming soon!')} className="hover:text-white transition-colors">Integrations</button></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2 text-gray-400">
                <li><button onClick={() => alert('About page coming soon!')} className="hover:text-white transition-colors">About</button></li>
                <li><button onClick={() => alert('Blog coming soon!')} className="hover:text-white transition-colors">Blog</button></li>
                <li><button onClick={() => alert('Careers page coming soon!')} className="hover:text-white transition-colors">Careers</button></li>
                <li><button onClick={() => alert('Contact page coming soon!')} className="hover:text-white transition-colors">Contact</button></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Support</h3>
              <ul className="space-y-2 text-gray-400">
                <li><button onClick={() => alert('Help Center coming soon!')} className="hover:text-white transition-colors">Help Center</button></li>
                <li><button onClick={() => alert('Documentation coming soon!')} className="hover:text-white transition-colors">Documentation</button></li>
                <li><button onClick={() => alert('Status page coming soon!')} className="hover:text-white transition-colors">Status</button></li>
                <li><button onClick={() => alert('Privacy Policy coming soon!')} className="hover:text-white transition-colors">Privacy</button></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 ChatFlow Pro. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
