from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.pagination import PageNumberPagination
from django.db.models import Q, Count, Prefetch
from django.utils import timezone
from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter

from .models import (
    ChatRoom, Message, RoomMembership, MessageReaction, MessageRead,
    TypingIndicator, MessageThread, PinnedMessage, ChatTranscript,
    VisitorTracking, ChatAgent, ChatQueue, CannedResponse, ChatBot,
    BotKnowledgeBase, CustomerFeedback, ChatIntegration, VoiceCall,
    ScreenShare
)
from .serializers import (
    ChatRoomSerializer, MessageSerializer, RoomMembershipSerializer,
    MessageReactionSerializer, MessageReadSerializer, TypingIndicatorSerializer,
    MessageThreadSerializer, PinnedMessageSerializer, ChatTranscriptSerializer,
    VisitorTrackingSerializer, ChatAgentSerializer, ChatQueueSerializer,
    CannedResponseSerializer, ChatBotSerializer, BotKnowledgeBaseSerializer,
    CustomerFeedbackSerializer, ChatIntegrationSerializer, VoiceCallSerializer,
    ScreenShareSerializer
)
from .permissions import IsRoomMemberOrPublic, IsAgentOrAdmin, IsOwnerOrModerator


class StandardResultsSetPagination(PageNumberPagination):
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 100


class ChatRoomViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing chat rooms
    """
    queryset = ChatRoom.objects.all()
    serializer_class = ChatRoomSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['room_type', 'is_public', 'is_vip_only']
    search_fields = ['name', 'description']
    ordering_fields = ['created_at', 'updated_at', 'member_count']
    ordering = ['-updated_at']

    def get_queryset(self):
        user = self.request.user
        queryset = ChatRoom.objects.select_related('created_by').prefetch_related(
            'members__user'
        )

        # Filter based on user access
        if user.user_type == 'admin':
            return queryset.all()

        # Regular users see public rooms and rooms they're members of
        return queryset.filter(
            Q(is_public=True) |
            Q(members__user=user, members__is_active=True)
        ).distinct()

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def join(self, request, pk=None):
        """Join a chat room"""
        room = self.get_object()
        user = request.user

        # Check if user can join
        if room.is_vip_only and not user.is_vip:
            return Response(
                {'error': 'VIP membership required'},
                status=status.HTTP_403_FORBIDDEN
            )

        if room.member_count >= room.max_members:
            return Response(
                {'error': 'Room is full'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create or reactivate membership
        membership, created = RoomMembership.objects.get_or_create(
            user=user,
            room=room,
            defaults={'is_active': True}
        )

        if not created and not membership.is_active:
            membership.is_active = True
            membership.save()

        return Response({'message': 'Successfully joined room'})

    @action(detail=True, methods=['post'])
    def leave(self, request, pk=None):
        """Leave a chat room"""
        room = self.get_object()
        user = request.user

        try:
            membership = RoomMembership.objects.get(user=user, room=room)
            membership.is_active = False
            membership.save()
            return Response({'message': 'Successfully left room'})
        except RoomMembership.DoesNotExist:
            return Response(
                {'error': 'Not a member of this room'},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['get'])
    def members(self, request, pk=None):
        """Get room members"""
        room = self.get_object()
        memberships = RoomMembership.objects.filter(
            room=room, is_active=True
        ).select_related('user')

        serializer = RoomMembershipSerializer(memberships, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def messages(self, request, pk=None):
        """Get room messages with pagination"""
        room = self.get_object()

        # Check if user has access to room
        if not room.is_public:
            if not RoomMembership.objects.filter(
                user=request.user, room=room, is_active=True
            ).exists():
                return Response(
                    {'error': 'Access denied'},
                    status=status.HTTP_403_FORBIDDEN
                )

        messages = Message.objects.filter(
            room=room, is_deleted=False
        ).select_related('sender', 'reply_to__sender').prefetch_related(
            'reactions__user', 'read_by__user'
        ).order_by('-created_at')

        page = self.paginate_queryset(messages)
        if page is not None:
            serializer = MessageSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = MessageSerializer(messages, many=True, context={'request': request})
        return Response(serializer.data)


class ChatAgentViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing chat agents
    """
    queryset = ChatAgent.objects.all()
    serializer_class = ChatAgentSerializer
    permission_classes = [permissions.IsAuthenticated, IsAgentOrAdmin]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['department', 'is_available', 'languages']
    search_fields = ['user__username', 'user__display_name', 'department']
    ordering_fields = ['created_at', 'total_chats_handled', 'customer_satisfaction']
    ordering = ['-created_at']

    def get_queryset(self):
        return ChatAgent.objects.select_related('user').all()

    @action(detail=True, methods=['post'])
    def set_availability(self, request, pk=None):
        """Set agent availability status"""
        agent = self.get_object()
        is_available = request.data.get('is_available', True)

        agent.is_available = is_available
        agent.save()

        return Response({'message': 'Availability updated', 'is_available': is_available})

    @action(detail=True, methods=['get'])
    def performance_stats(self, request, pk=None):
        """Get agent performance statistics"""
        agent = self.get_object()

        # Calculate stats for last 30 days
        from datetime import datetime, timedelta
        thirty_days_ago = datetime.now() - timedelta(days=30)

        stats = {
            'total_chats': agent.total_chats_handled,
            'avg_response_time': agent.average_response_time,
            'customer_satisfaction': agent.customer_satisfaction,
            'chats_last_30_days': Message.objects.filter(
                sender=agent.user,
                created_at__gte=thirty_days_ago
            ).values('room').distinct().count()
        }

        return Response(stats)


class ChatQueueViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing chat queue
    """
    queryset = ChatQueue.objects.all()
    serializer_class = ChatQueueSerializer
    permission_classes = [permissions.IsAuthenticated, IsAgentOrAdmin]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['status', 'department', 'priority']
    ordering_fields = ['created_at', 'priority', 'estimated_wait_time']
    ordering = ['priority', 'created_at']

    def get_queryset(self):
        return ChatQueue.objects.select_related('visitor', 'assigned_agent__user').all()

    @action(detail=True, methods=['post'])
    def assign_agent(self, request, pk=None):
        """Assign agent to queue item"""
        queue_item = self.get_object()
        agent_id = request.data.get('agent_id')

        if not agent_id:
            return Response({'error': 'Agent ID required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            agent = ChatAgent.objects.get(id=agent_id, is_available=True)
            if not agent.can_accept_chat:
                return Response({'error': 'Agent cannot accept more chats'}, status=status.HTTP_400_BAD_REQUEST)

            queue_item.assigned_agent = agent
            queue_item.status = 'assigned'
            queue_item.assigned_at = timezone.now()
            queue_item.save()

            return Response({'message': 'Agent assigned successfully'})

        except ChatAgent.DoesNotExist:
            return Response({'error': 'Agent not found or unavailable'}, status=status.HTTP_400_BAD_REQUEST)


class CannedResponseViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing canned responses
    """
    queryset = CannedResponse.objects.all()
    serializer_class = CannedResponseSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['category', 'department', 'language', 'is_public']
    search_fields = ['title', 'content', 'shortcut']
    ordering_fields = ['created_at', 'usage_count', 'title']
    ordering = ['-usage_count']

    def get_queryset(self):
        user = self.request.user
        queryset = CannedResponse.objects.select_related('created_by')

        # Users can see public responses and their own
        return queryset.filter(
            Q(is_public=True) | Q(created_by=user)
        )

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def use_response(self, request, pk=None):
        """Track usage of canned response"""
        response = self.get_object()
        response.usage_count += 1
        response.save()

        return Response({'message': 'Usage tracked'})


class ChatBotViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing chatbots
    """
    queryset = ChatBot.objects.all()
    serializer_class = ChatBotSerializer
    permission_classes = [permissions.IsAuthenticated, IsAgentOrAdmin]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['is_active', 'departments', 'supported_languages']
    search_fields = ['name', 'description']
    ordering_fields = ['created_at', 'total_conversations', 'handover_rate']
    ordering = ['-created_at']

    def get_queryset(self):
        return ChatBot.objects.all()

    @action(detail=True, methods=['post'])
    def train(self, request, pk=None):
        """Train chatbot with new data"""
        bot = self.get_object()
        training_data = request.data.get('training_data', [])

        # Process training data
        for item in training_data:
            BotKnowledgeBase.objects.create(
                bot=bot,
                question=item.get('question'),
                answer=item.get('answer'),
                keywords=item.get('keywords', []),
                category=item.get('category', '')
            )

        return Response({'message': f'Bot trained with {len(training_data)} items'})

    @action(detail=True, methods=['post'])
    def chat(self, request, pk=None):
        """Chat with bot"""
        bot = self.get_object()
        message = request.data.get('message')

        if not message:
            return Response({'error': 'Message required'}, status=status.HTTP_400_BAD_REQUEST)

        # Simple keyword matching (in production, use AI/ML)
        knowledge_items = bot.knowledge_base.filter(is_active=True)

        best_match = None
        best_score = 0

        for item in knowledge_items:
            score = 0
            message_lower = message.lower()

            # Check keywords
            for keyword in item.keywords:
                if keyword.lower() in message_lower:
                    score += 1

            # Check question similarity (simple word matching)
            question_words = item.question.lower().split()
            for word in question_words:
                if word in message_lower:
                    score += 0.5

            if score > best_score and score >= item.confidence_threshold:
                best_score = score
                best_match = item

        if best_match:
            best_match.usage_count += 1
            best_match.save()

            return Response({
                'response': best_match.answer,
                'confidence': best_score,
                'source': 'knowledge_base'
            })
        else:
            return Response({
                'response': bot.fallback_message,
                'confidence': 0,
                'source': 'fallback'
            })


class CustomerFeedbackViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing customer feedback
    """
    queryset = CustomerFeedback.objects.all()
    serializer_class = CustomerFeedbackSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['overall_rating', 'issue_resolved', 'agent']
    ordering_fields = ['created_at', 'overall_rating']
    ordering = ['-created_at']

    def get_queryset(self):
        user = self.request.user
        if user.user_type in ['admin', 'agent']:
            return CustomerFeedback.objects.select_related('customer', 'agent__user').all()
        else:
            return CustomerFeedback.objects.filter(customer=user)

    def perform_create(self, serializer):
        serializer.save(customer=self.request.user)


class ChatIntegrationViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing chat integrations
    """
    queryset = ChatIntegration.objects.all()
    serializer_class = ChatIntegrationSerializer
    permission_classes = [permissions.IsAuthenticated, IsAgentOrAdmin]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['integration_type', 'is_active']
    search_fields = ['name']
    ordering_fields = ['created_at', 'name']
    ordering = ['-created_at']

    def get_queryset(self):
        return ChatIntegration.objects.all()

    @action(detail=True, methods=['post'])
    def test_connection(self, request, pk=None):
        """Test integration connection"""
        integration = self.get_object()

        # Simulate connection test
        import random
        success = random.choice([True, False])

        return Response({
            'success': success,
            'message': 'Connection successful' if success else 'Connection failed'
        })


class VoiceCallViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing voice calls
    """
    queryset = VoiceCall.objects.all()
    serializer_class = VoiceCallSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['call_type', 'status']
    ordering_fields = ['started_at', 'duration']
    ordering = ['-started_at']

    def get_queryset(self):
        user = self.request.user
        return VoiceCall.objects.filter(
            Q(initiated_by=user) | Q(participants=user)
        ).distinct().select_related('initiated_by').prefetch_related('participants')

    def perform_create(self, serializer):
        serializer.save(initiated_by=self.request.user)

    @action(detail=True, methods=['post'])
    def join(self, request, pk=None):
        """Join voice call"""
        call = self.get_object()
        call.participants.add(request.user)

        return Response({'message': 'Joined call successfully'})

    @action(detail=True, methods=['post'])
    def leave(self, request, pk=None):
        """Leave voice call"""
        call = self.get_object()
        call.participants.remove(request.user)

        return Response({'message': 'Left call successfully'})

    @action(detail=True, methods=['post'])
    def end(self, request, pk=None):
        """End voice call"""
        call = self.get_object()

        if call.initiated_by != request.user:
            return Response({'error': 'Only call initiator can end call'}, status=status.HTTP_403_FORBIDDEN)

        call.status = 'ended'
        call.ended_at = timezone.now()
        call.duration = int((call.ended_at - call.started_at).total_seconds())
        call.save()

        return Response({'message': 'Call ended successfully'})

    @action(detail=True, methods=['post'])
    def pin_message(self, request, pk=None):
        """Pin a message in the room"""
        room = self.get_object()
        message_id = request.data.get('message_id')
        reason = request.data.get('reason', '')

        # Check if user is moderator or admin
        membership = get_object_or_404(
            RoomMembership, user=request.user, room=room, is_active=True
        )
        if membership.role not in ['moderator', 'admin', 'owner']:
            return Response(
                {'error': 'Insufficient permissions'},
                status=status.HTTP_403_FORBIDDEN
            )

        message = get_object_or_404(Message, id=message_id, room=room)

        pinned_message, created = PinnedMessage.objects.get_or_create(
            room=room,
            message=message,
            defaults={
                'pinned_by': request.user,
                'reason': reason
            }
        )

        if created:
            message.is_pinned = True
            message.save()
            return Response({'message': 'Message pinned successfully'})
        else:
            return Response(
                {'error': 'Message already pinned'},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['get'])
    def analytics(self, request, pk=None):
        """Get room analytics"""
        room = self.get_object()

        # Check if user is moderator or admin
        membership = get_object_or_404(
            RoomMembership, user=request.user, room=room, is_active=True
        )
        if membership.role not in ['moderator', 'admin', 'owner']:
            return Response(
                {'error': 'Insufficient permissions'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Calculate analytics
        total_messages = Message.objects.filter(room=room).count()
        active_members = RoomMembership.objects.filter(
            room=room, is_active=True
        ).count()

        # Message breakdown by type
        message_stats = Message.objects.filter(room=room).values(
            'message_type'
        ).annotate(count=Count('id'))

        # Top contributors
        top_contributors = Message.objects.filter(room=room).values(
            'sender__username', 'sender__display_name'
        ).annotate(
            message_count=Count('id')
        ).order_by('-message_count')[:10]

        return Response({
            'total_messages': total_messages,
            'active_members': active_members,
            'message_stats': list(message_stats),
            'top_contributors': list(top_contributors),
            'created_at': room.created_at,
            'last_activity': room.updated_at,
        })


class MessageViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing messages
    """
    queryset = Message.objects.all()
    serializer_class = MessageSerializer
    permission_classes = [permissions.IsAuthenticated, IsRoomMemberOrPublic]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['message_type', 'is_edited', 'is_pinned']
    search_fields = ['content']
    ordering_fields = ['created_at']
    ordering = ['-created_at']

    def get_queryset(self):
        return Message.objects.filter(
            is_deleted=False
        ).select_related(
            'sender', 'room', 'reply_to__sender'
        ).prefetch_related(
            'reactions__user', 'read_by__user'
        )

    def perform_create(self, serializer):
        serializer.save(sender=self.request.user)

    @action(detail=True, methods=['post'])
    def react(self, request, pk=None):
        """Add or remove reaction to message"""
        message = self.get_object()
        emoji = request.data.get('emoji')

        if not emoji:
            return Response(
                {'error': 'Emoji is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        reaction, created = MessageReaction.objects.get_or_create(
            message=message,
            user=request.user,
            emoji=emoji
        )

        if not created:
            # Remove reaction if it already exists
            reaction.delete()
            return Response({'message': 'Reaction removed'})

        return Response({'message': 'Reaction added'})

    @action(detail=True, methods=['post'])
    def mark_read(self, request, pk=None):
        """Mark message as read"""
        message = self.get_object()

        MessageRead.objects.get_or_create(
            message=message,
            user=request.user
        )

        return Response({'message': 'Message marked as read'})

    @action(detail=True, methods=['post'])
    def reply(self, request, pk=None):
        """Reply to a message"""
        parent_message = self.get_object()
        content = request.data.get('content')

        if not content:
            return Response(
                {'error': 'Content is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        reply = Message.objects.create(
            room=parent_message.room,
            sender=request.user,
            content=content,
            reply_to=parent_message,
            message_type='text'
        )

        serializer = MessageSerializer(reply, context={'request': request})
        return Response(serializer.data, status=status.HTTP_201_CREATED)


# File Upload Views
class FileUploadView(APIView):
    """
    Handle file uploads for chat messages
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        import os
        from django.core.files.storage import default_storage
        from django.conf import settings
        import uuid
        from PIL import Image

        file = request.FILES.get('file')
        room_id = request.data.get('room_id')

        if not file or not room_id:
            return Response({
                'error': 'File and room_id are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate file size
        max_size = getattr(settings, 'CHATFLOW_SETTINGS', {}).get('MAX_FILE_SIZE', 50 * 1024 * 1024)
        if file.size > max_size:
            return Response({
                'error': f'File size exceeds {max_size // (1024*1024)}MB limit'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate file type
        allowed_types = getattr(settings, 'CHATFLOW_SETTINGS', {}).get('ALLOWED_FILE_TYPES', [])
        if allowed_types and file.content_type not in allowed_types:
            return Response({
                'error': 'File type not allowed'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Generate unique filename
            file_extension = os.path.splitext(file.name)[1]
            unique_filename = f"{uuid.uuid4()}{file_extension}"

            # Determine upload path based on file type
            if file.content_type.startswith('image/'):
                upload_path = f"chat/images/{unique_filename}"
            elif file.content_type.startswith('video/'):
                upload_path = f"chat/videos/{unique_filename}"
            elif file.content_type.startswith('audio/'):
                upload_path = f"chat/audio/{unique_filename}"
            else:
                upload_path = f"chat/files/{unique_filename}"

            # Save file
            file_path = default_storage.save(upload_path, file)
            file_url = default_storage.url(file_path)

            # Process image if needed
            thumbnail_url = None
            if file.content_type.startswith('image/'):
                thumbnail_url = self.create_thumbnail(file_path)

            return Response({
                'file_url': file_url,
                'file_name': file.name,
                'file_size': file.size,
                'file_type': file.content_type,
                'thumbnail_url': thumbnail_url
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({
                'error': f'Upload failed: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def create_thumbnail(self, file_path):
        """Create thumbnail for images"""
        try:
            from PIL import Image
            import os
            from django.core.files.storage import default_storage

            # Open image
            with default_storage.open(file_path, 'rb') as f:
                image = Image.open(f)

                # Create thumbnail
                image.thumbnail((300, 300), Image.Resampling.LANCZOS)

                # Save thumbnail
                thumbnail_path = file_path.replace('.', '_thumb.')

                # Save to storage
                from io import BytesIO
                thumb_io = BytesIO()
                image.save(thumb_io, format='JPEG', quality=85)
                thumb_io.seek(0)

                from django.core.files.base import ContentFile
                thumbnail_file = ContentFile(thumb_io.read())
                saved_path = default_storage.save(thumbnail_path, thumbnail_file)

                return default_storage.url(saved_path)

        except Exception as e:
            print(f"Thumbnail creation failed: {e}")
            return None


class VoiceUploadView(APIView):
    """
    Handle voice message uploads
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        import os
        from django.core.files.storage import default_storage
        import uuid
        import wave

        audio_file = request.FILES.get('audio')
        room_id = request.data.get('room_id')

        if not audio_file or not room_id:
            return Response({
                'error': 'Audio file and room_id are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Generate unique filename
            unique_filename = f"voice_{uuid.uuid4()}.webm"
            upload_path = f"chat/voice/{unique_filename}"

            # Save file
            file_path = default_storage.save(upload_path, audio_file)
            file_url = default_storage.url(file_path)

            # Try to get audio duration (basic implementation)
            duration = self.get_audio_duration(audio_file)

            return Response({
                'file_url': file_url,
                'duration': duration,
                'file_size': audio_file.size
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({
                'error': f'Voice upload failed: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get_audio_duration(self, audio_file):
        """Get audio duration in seconds"""
        try:
            # For WebM files, this is a simplified approach
            # In production, use proper audio processing libraries
            return min(300, max(1, audio_file.size // 8000))  # Rough estimate
        except:
            return 30  # Default duration


class MessageSearchView(APIView):
    """
    Search messages across rooms
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        query = request.GET.get('q', '')
        room_id = request.GET.get('room_id')
        message_type = request.GET.get('type', 'all')
        date_from = request.GET.get('date_from')
        date_to = request.GET.get('date_to')

        if not query:
            return Response({
                'error': 'Search query is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Base queryset
        messages = Message.objects.filter(
            is_deleted=False,
            content__icontains=query
        ).select_related('sender', 'room')

        # Filter by room if specified
        if room_id:
            messages = messages.filter(room_id=room_id)
        else:
            # Only search in rooms user has access to
            user_rooms = ChatRoom.objects.filter(
                Q(is_public=True) |
                Q(members__user=request.user, members__is_active=True)
            ).values_list('id', flat=True)
            messages = messages.filter(room_id__in=user_rooms)

        # Filter by message type
        if message_type != 'all':
            messages = messages.filter(message_type=message_type)

        # Filter by date range
        if date_from:
            from datetime import datetime
            messages = messages.filter(created_at__gte=datetime.fromisoformat(date_from))
        if date_to:
            from datetime import datetime
            messages = messages.filter(created_at__lte=datetime.fromisoformat(date_to))

        # Limit results
        messages = messages.order_by('-created_at')[:100]

        serializer = MessageSerializer(messages, many=True, context={'request': request})
        return Response({
            'results': serializer.data,
            'count': len(serializer.data)
        })


class ChatAnalyticsView(APIView):
    """
    Get chat analytics data
    """
    permission_classes = [permissions.IsAuthenticated, IsAgentOrAdmin]

    def get(self, request):
        from datetime import datetime, timedelta
        from django.db.models import Count, Avg

        # Date range
        days = int(request.GET.get('days', 30))
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # Basic metrics
        total_messages = Message.objects.filter(
            created_at__gte=start_date,
            is_deleted=False
        ).count()

        total_rooms = ChatRoom.objects.filter(
            created_at__gte=start_date
        ).count()

        active_users = Message.objects.filter(
            created_at__gte=start_date
        ).values('sender').distinct().count()

        # Message breakdown by type
        message_types = Message.objects.filter(
            created_at__gte=start_date,
            is_deleted=False
        ).values('message_type').annotate(count=Count('id'))

        # Daily message counts
        daily_messages = []
        for i in range(days):
            day = start_date + timedelta(days=i)
            count = Message.objects.filter(
                created_at__date=day.date(),
                is_deleted=False
            ).count()
            daily_messages.append({
                'date': day.date().isoformat(),
                'count': count
            })

        # Top active rooms
        top_rooms = ChatRoom.objects.annotate(
            message_count=Count('messages', filter=Q(messages__created_at__gte=start_date))
        ).order_by('-message_count')[:10]

        return Response({
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'days': days
            },
            'metrics': {
                'total_messages': total_messages,
                'total_rooms': total_rooms,
                'active_users': active_users
            },
            'message_types': list(message_types),
            'daily_messages': daily_messages,
            'top_rooms': [
                {
                    'id': room.id,
                    'name': room.name,
                    'message_count': room.message_count
                }
                for room in top_rooms
            ]
        })


class VisitorTrackingView(APIView):
    """
    Track visitor activity
    """
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        session_id = request.data.get('session_id')
        page_url = request.data.get('page_url')
        referrer = request.data.get('referrer')

        if not session_id:
            return Response({
                'error': 'Session ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get or create visitor tracking
        visitor, created = VisitorTracking.objects.get_or_create(
            session_id=session_id,
            defaults={
                'ip_address': self.get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                'referrer': referrer or '',
                'current_page': page_url or '',
                'device_type': self.get_device_type(request),
                'browser': self.get_browser(request),
                'os': self.get_os(request)
            }
        )

        if not created:
            # Update existing visitor
            visitor.current_page = page_url or visitor.current_page
            visitor.last_activity = timezone.now()
            if page_url and page_url not in visitor.pages_visited:
                visitor.pages_visited.append(page_url)
            visitor.save()

        return Response({
            'visitor_id': visitor.id,
            'session_id': visitor.session_id,
            'is_returning': visitor.is_returning
        })

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

    def get_device_type(self, request):
        user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
        if 'mobile' in user_agent or 'android' in user_agent or 'iphone' in user_agent:
            return 'mobile'
        elif 'tablet' in user_agent or 'ipad' in user_agent:
            return 'tablet'
        else:
            return 'desktop'

    def get_browser(self, request):
        user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
        if 'chrome' in user_agent:
            return 'Chrome'
        elif 'firefox' in user_agent:
            return 'Firefox'
        elif 'safari' in user_agent:
            return 'Safari'
        elif 'edge' in user_agent:
            return 'Edge'
        else:
            return 'Unknown'

    def get_os(self, request):
        user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
        if 'windows' in user_agent:
            return 'Windows'
        elif 'mac' in user_agent:
            return 'macOS'
        elif 'linux' in user_agent:
            return 'Linux'
        elif 'android' in user_agent:
            return 'Android'
        elif 'ios' in user_agent:
            return 'iOS'
        else:
            return 'Unknown'


# Admin Views
class AdminRoomsListView(APIView):
    """
    Admin endpoint to list all chat rooms
    """
    permission_classes = [permissions.IsAuthenticated, permissions.IsAdminUser]

    def get(self, request):
        rooms = ChatRoom.objects.all().order_by('-created_at')
        room_data = []

        for room in rooms:
            room_data.append({
                'id': room.id,
                'name': room.name,
                'description': room.description,
                'room_type': room.room_type,
                'is_public': room.is_public,
                'is_active': room.is_active,
                'member_count': room.members.filter(is_active=True).count(),
                'created_at': room.created_at,
                'created_by': {
                    'id': room.created_by.id,
                    'username': room.created_by.username,
                    'display_name': room.created_by.display_name
                } if room.created_by else None
            })

        return Response({
            'results': room_data,
            'count': len(room_data)
        })


class AdminRoomDetailView(APIView):
    """
    Admin endpoint to get/update/delete room details
    """
    permission_classes = [permissions.IsAuthenticated, permissions.IsAdminUser]

    def get(self, request, room_id):
        try:
            room = ChatRoom.objects.get(id=room_id)
            return Response({
                'id': room.id,
                'name': room.name,
                'description': room.description,
                'room_type': room.room_type,
                'is_public': room.is_public,
                'is_active': room.is_active,
                'member_count': room.members.filter(is_active=True).count(),
                'created_at': room.created_at,
                'created_by': {
                    'id': room.created_by.id,
                    'username': room.created_by.username,
                    'display_name': room.created_by.display_name
                } if room.created_by else None
            })
        except ChatRoom.DoesNotExist:
            return Response({'error': 'Room not found'}, status=status.HTTP_404_NOT_FOUND)

    def delete(self, request, room_id):
        try:
            room = ChatRoom.objects.get(id=room_id)
            room.delete()
            return Response({'message': 'Room deleted successfully'})
        except ChatRoom.DoesNotExist:
            return Response({'error': 'Room not found'}, status=status.HTTP_404_NOT_FOUND)


class AdminBotsListView(APIView):
    """
    Admin endpoint to list all chat bots
    """
    permission_classes = [permissions.IsAuthenticated, permissions.IsAdminUser]

    def get(self, request):
        bots = ChatBot.objects.all().order_by('-created_at')
        bot_data = []

        for bot in bots:
            bot_data.append({
                'id': bot.id,
                'name': bot.name,
                'description': bot.description,
                'is_active': bot.is_active,
                'total_conversations': bot.total_conversations,
                'handover_rate': bot.handover_rate,
                'created_at': bot.created_at
            })

        return Response({
            'results': bot_data,
            'count': len(bot_data)
        })


class AdminReportsListView(APIView):
    """
    Admin endpoint to list reports
    """
    permission_classes = [permissions.IsAuthenticated, permissions.IsAdminUser]

    def get(self, request):
        # TODO: Implement actual reports
        return Response({
            'results': [],
            'count': 0,
            'message': 'Reports functionality coming soon!'
        })


class AdminSettingsView(APIView):
    """
    Admin endpoint to get/update system settings
    """
    permission_classes = [permissions.IsAuthenticated, permissions.IsAdminUser]

    def get(self, request):
        # TODO: Implement actual settings
        return Response({
            'settings': {},
            'message': 'Settings functionality coming soon!'
        })
