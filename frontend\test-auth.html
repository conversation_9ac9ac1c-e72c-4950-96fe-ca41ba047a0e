<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatFlow Pro - Auth Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
        .status {
            font-weight: bold;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 ChatFlow Pro - Frontend-Backend Integration Test</h1>
        
        <div class="test-section info">
            <h3>📋 Test Overview</h3>
            <p>This page tests the integration between the frontend and backend APIs to ensure they're properly aligned.</p>
        </div>

        <div class="test-section">
            <h3>🔐 Authentication Test</h3>
            <button onclick="testLogin()">Test Login</button>
            <button onclick="testProfile()">Test Profile</button>
            <button onclick="testLogout()">Test Logout</button>
            <div id="auth-status" class="status"></div>
            <pre id="auth-result"></pre>
        </div>

        <div class="test-section">
            <h3>💬 Chat API Test</h3>
            <button onclick="testRooms()">Test Get Rooms</button>
            <button onclick="testCreateRoom()">Test Create Room</button>
            <div id="chat-status" class="status"></div>
            <pre id="chat-result"></pre>
        </div>

        <div class="test-section">
            <h3>🎥 Live Events Test</h3>
            <button onclick="testLiveEvents()">Test Get Live Events</button>
            <button onclick="testCreateLiveEvent()">Test Create Live Event</button>
            <div id="live-status" class="status"></div>
            <pre id="live-result"></pre>
        </div>

        <div class="test-section">
            <h3>📊 Summary</h3>
            <div id="summary" class="status"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';
        let authToken = null;
        let testResults = {
            auth: { passed: 0, failed: 0 },
            chat: { passed: 0, failed: 0 },
            live: { passed: 0, failed: 0 }
        };

        function updateStatus(section, message, isSuccess = true) {
            const statusEl = document.getElementById(`${section}-status`);
            statusEl.textContent = message;
            statusEl.className = `status ${isSuccess ? 'success' : 'error'}`;
        }

        function updateResult(section, data) {
            const resultEl = document.getElementById(`${section}-result`);
            resultEl.textContent = JSON.stringify(data, null, 2);
        }

        function updateSummary() {
            const total = Object.values(testResults).reduce((acc, curr) => ({
                passed: acc.passed + curr.passed,
                failed: acc.failed + curr.failed
            }), { passed: 0, failed: 0 });
            
            const summaryEl = document.getElementById('summary');
            summaryEl.innerHTML = `
                <strong>Test Results:</strong><br>
                ✅ Passed: ${total.passed}<br>
                ❌ Failed: ${total.failed}<br>
                📊 Success Rate: ${total.passed + total.failed > 0 ? Math.round((total.passed / (total.passed + total.failed)) * 100) : 0}%
            `;
        }

        async function testLogin() {
            try {
                updateStatus('auth', 'Testing login...');
                
                const response = await fetch(`${API_BASE}/auth/login/`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        username: '<EMAIL>',
                        password: 'admin123'
                    })
                });

                const data = await response.json();
                
                if (response.ok && data.tokens) {
                    authToken = data.tokens.access;
                    updateStatus('auth', '✅ Login successful!');
                    updateResult('auth', data);
                    testResults.auth.passed++;
                } else {
                    throw new Error(data.error || 'Login failed');
                }
            } catch (error) {
                updateStatus('auth', `❌ Login failed: ${error.message}`, false);
                updateResult('auth', { error: error.message });
                testResults.auth.failed++;
            }
            updateSummary();
        }

        async function testProfile() {
            if (!authToken) {
                updateStatus('auth', '❌ No auth token. Please login first.', false);
                testResults.auth.failed++;
                updateSummary();
                return;
            }

            try {
                updateStatus('auth', 'Testing profile...');
                
                const response = await fetch(`${API_BASE}/auth/profile/`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                const data = await response.json();
                
                if (response.ok) {
                    updateStatus('auth', '✅ Profile fetch successful!');
                    updateResult('auth', data);
                    testResults.auth.passed++;
                } else {
                    throw new Error(data.error || 'Profile fetch failed');
                }
            } catch (error) {
                updateStatus('auth', `❌ Profile failed: ${error.message}`, false);
                updateResult('auth', { error: error.message });
                testResults.auth.failed++;
            }
            updateSummary();
        }

        async function testLogout() {
            if (!authToken) {
                updateStatus('auth', '❌ No auth token. Please login first.', false);
                testResults.auth.failed++;
                updateSummary();
                return;
            }

            try {
                updateStatus('auth', 'Testing logout...');
                
                const response = await fetch(`${API_BASE}/auth/logout/`, {
                    method: 'POST',
                    headers: { 
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ refresh_token: 'dummy' })
                });

                const data = await response.json();
                
                if (response.ok) {
                    updateStatus('auth', '✅ Logout successful!');
                    updateResult('auth', data);
                    testResults.auth.passed++;
                    authToken = null;
                } else {
                    throw new Error(data.error || 'Logout failed');
                }
            } catch (error) {
                updateStatus('auth', `❌ Logout failed: ${error.message}`, false);
                updateResult('auth', { error: error.message });
                testResults.auth.failed++;
            }
            updateSummary();
        }

        async function testRooms() {
            if (!authToken) {
                updateStatus('chat', '❌ No auth token. Please login first.', false);
                testResults.chat.failed++;
                updateSummary();
                return;
            }

            try {
                updateStatus('chat', 'Testing get rooms...');
                
                const response = await fetch(`${API_BASE}/chat/rooms/`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                const data = await response.json();
                
                if (response.ok) {
                    updateStatus('chat', '✅ Get rooms successful!');
                    updateResult('chat', data);
                    testResults.chat.passed++;
                } else {
                    throw new Error(data.error || 'Get rooms failed');
                }
            } catch (error) {
                updateStatus('chat', `❌ Get rooms failed: ${error.message}`, false);
                updateResult('chat', { error: error.message });
                testResults.chat.failed++;
            }
            updateSummary();
        }

        async function testCreateRoom() {
            if (!authToken) {
                updateStatus('chat', '❌ No auth token. Please login first.', false);
                testResults.chat.failed++;
                updateSummary();
                return;
            }

            try {
                updateStatus('chat', 'Testing create room...');
                
                const response = await fetch(`${API_BASE}/chat/rooms/`, {
                    method: 'POST',
                    headers: { 
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: 'Test Room ' + Date.now(),
                        description: 'Test room created by integration test',
                        room_type: 'group',
                        is_public: true
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    updateStatus('chat', '✅ Create room successful!');
                    updateResult('chat', data);
                    testResults.chat.passed++;
                } else {
                    throw new Error(data.error || 'Create room failed');
                }
            } catch (error) {
                updateStatus('chat', `❌ Create room failed: ${error.message}`, false);
                updateResult('chat', { error: error.message });
                testResults.chat.failed++;
            }
            updateSummary();
        }

        async function testLiveEvents() {
            if (!authToken) {
                updateStatus('live', '❌ No auth token. Please login first.', false);
                testResults.live.failed++;
                updateSummary();
                return;
            }

            try {
                updateStatus('live', 'Testing get live events...');
                
                const response = await fetch(`${API_BASE}/live/events/`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                const data = await response.json();
                
                if (response.ok) {
                    updateStatus('live', '✅ Get live events successful!');
                    updateResult('live', data);
                    testResults.live.passed++;
                } else {
                    throw new Error(data.error || 'Get live events failed');
                }
            } catch (error) {
                updateStatus('live', `❌ Get live events failed: ${error.message}`, false);
                updateResult('live', { error: error.message });
                testResults.live.failed++;
            }
            updateSummary();
        }

        async function testCreateLiveEvent() {
            if (!authToken) {
                updateStatus('live', '❌ No auth token. Please login first.', false);
                testResults.live.failed++;
                updateSummary();
                return;
            }

            try {
                updateStatus('live', 'Testing create live event...');
                
                const response = await fetch(`${API_BASE}/live/events/`, {
                    method: 'POST',
                    headers: { 
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        title: 'Test Live Event ' + Date.now(),
                        description: 'Test live event created by integration test',
                        event_type: 'webinar',
                        scheduled_start: new Date(Date.now() + 86400000).toISOString(),
                        scheduled_end: new Date(Date.now() + 90000000).toISOString(),
                        is_public: true
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    updateStatus('live', '✅ Create live event successful!');
                    updateResult('live', data);
                    testResults.live.passed++;
                } else {
                    throw new Error(data.error || 'Create live event failed');
                }
            } catch (error) {
                updateStatus('live', `❌ Create live event failed: ${error.message}`, false);
                updateResult('live', { error: error.message });
                testResults.live.failed++;
            }
            updateSummary();
        }

        // Initialize
        updateSummary();
    </script>
</body>
</html>
