import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, AuthTokens, LoginCredentials, RegisterData } from '../types';
import { apiService } from '../services/api';
import { wsService } from '../services/websocket';

interface AuthState {
  user: User | null;
  tokens: AuthTokens | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface AuthActions {
  login: (credentials: LoginCredentials) => Promise<boolean>;
  register: (data: RegisterData) => Promise<boolean>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  updateUser: (updates: Partial<User>) => void;
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      tokens: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (credentials: LoginCredentials) => {
        set({ isLoading: true, error: null });

        try {
          const response = await apiService.login(credentials.email, credentials.password);

          if (response.success && response.data) {
            // Backend returns { user: {...}, tokens: {...}, message: "..." }
            const { user, tokens } = response.data;

            if (user && tokens) {
              set({
                user: user,
                tokens: tokens,
                isAuthenticated: true,
                isLoading: false,
                error: null,
              });

              // Connect to WebSocket
              try {
                await wsService.connect();
              } catch (wsError) {
                console.error('WebSocket connection failed:', wsError);
              }

              return true;
            }
          }

          set({
            isLoading: false,
            error: response.message || 'Login failed',
          });
          return false;
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || 'Login failed',
          });
          return false;
        }
      },

      register: async (data: RegisterData) => {
        set({ isLoading: true, error: null });

        try {
          const response = await apiService.register(data);

          if (response.success && response.data) {
            // Backend returns { user: {...}, tokens: {...}, message: "..." }
            const { user, tokens } = response.data;

            if (user && tokens) {
              set({
                user: user,
                tokens: tokens,
                isAuthenticated: true,
                isLoading: false,
                error: null,
              });

              // Connect to WebSocket
              try {
                await wsService.connect();
              } catch (wsError) {
                console.error('WebSocket connection failed:', wsError);
              }

              return true;
            }
          }

          set({
            isLoading: false,
            error: response.message || 'Registration failed',
          });
          return false;
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || 'Registration failed',
          });
          return false;
        }
      },

      logout: async () => {
        set({ isLoading: true });

        try {
          await apiService.logout();
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          // Disconnect WebSocket
          wsService.disconnect();

          // Clear state
          set({
            user: null,
            tokens: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        }
      },

      refreshUser: async () => {
        if (!get().isAuthenticated) return;

        try {
          const response = await apiService.get<User>('/auth/profile/');

          if (response.success && response.data) {
            set({ user: response.data });
          } else {
            // If user fetch fails, logout
            await get().logout();
          }
        } catch (error) {
          console.error('Failed to refresh user:', error);
          await get().logout();
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      updateUser: (updates: Partial<User>) => {
        const currentUser = get().user;
        if (currentUser) {
          set({
            user: { ...currentUser, ...updates },
          });
        }
      },
    }),
    {
      name: 'chatflow-auth',
      partialize: (state) => ({
        user: state.user,
        tokens: state.tokens,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Initialize auth state on app start
export const initializeAuth = async () => {
  const { isAuthenticated, refreshUser } = useAuthStore.getState();

  if (isAuthenticated) {
    try {
      await refreshUser();
      await wsService.connect();
    } catch (error) {
      console.error('Failed to initialize auth:', error);
    }
  }
};
