#!/usr/bin/env python3
"""
Test script to check all API endpoints
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def test_endpoint(method, endpoint, data=None, headers=None):
    """Test a single endpoint"""
    url = f"{BASE_URL}{endpoint}"
    try:
        if method.upper() == 'GET':
            response = requests.get(url, headers=headers)
        elif method.upper() == 'POST':
            response = requests.post(url, json=data, headers=headers)
        elif method.upper() == 'PUT':
            response = requests.put(url, json=data, headers=headers)
        elif method.upper() == 'DELETE':
            response = requests.delete(url, headers=headers)
        
        print(f"{method.upper()} {endpoint}: {response.status_code}")
        if response.status_code >= 400:
            print(f"  Error: {response.text}")
        return response
    except requests.exceptions.ConnectionError:
        print(f"{method.upper()} {endpoint}: CONNECTION ERROR - Backend not running")
        return None
    except Exception as e:
        print(f"{method.upper()} {endpoint}: ERROR - {str(e)}")
        return None

def main():
    print("Testing ChatFlow Pro API Endpoints")
    print("=" * 50)
    
    # Test basic endpoints
    print("\n1. Testing Authentication Endpoints:")
    test_endpoint('POST', '/api/auth/register/', {
        'username': 'testuser',
        'email': '<EMAIL>',
        'password': 'testpass123',
        'displayName': 'Test User'
    })
    
    test_endpoint('POST', '/api/auth/login/', {
        'email': '<EMAIL>',
        'password': 'testpass123'
    })
    
    test_endpoint('POST', '/api/auth/logout/')
    test_endpoint('POST', '/api/auth/token/refresh/')
    
    print("\n2. Testing Chat Endpoints:")
    test_endpoint('GET', '/api/chat/rooms/')
    test_endpoint('POST', '/api/chat/rooms/', {
        'name': 'Test Room',
        'description': 'Test room description'
    })
    
    print("\n3. Testing Admin Endpoints:")
    test_endpoint('GET', '/api/admin/users/')
    test_endpoint('GET', '/api/admin/rooms/')
    test_endpoint('GET', '/api/admin/bots/')
    
    print("\n4. Testing VIP Endpoints:")
    test_endpoint('GET', '/api/vip/subscription/')
    test_endpoint('POST', '/api/vip/subscribe/')
    
    print("\n5. Testing Live Events Endpoints:")
    test_endpoint('GET', '/api/live/events/')
    
    print("\n6. Testing Analytics Endpoints:")
    test_endpoint('GET', '/api/analytics/dashboard/')
    
    print("\n7. Testing Support Endpoints:")
    test_endpoint('GET', '/api/support/tickets/')
    test_endpoint('GET', '/api/support/faq/')

if __name__ == "__main__":
    main()
