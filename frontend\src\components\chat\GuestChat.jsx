import React, { useState, useEffect } from 'react';
import { MessageCircle, Send, User, X, Minimize2, Maximize2, UserPlus } from 'lucide-react';

const GuestChat = ({ isOpen, onClose, onRegister }) => {
  const [messages, setMessages] = useState([]);
  const [inputText, setInputText] = useState('');
  const [guestName, setGuestName] = useState('');
  const [isNameSet, setIsNameSet] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isConnected, setIsConnected] = useState(false);

  // Generate random guest ID
  const [guestId] = useState(() => `guest_${Math.random().toString(36).substr(2, 9)}`);

  useEffect(() => {
    if (isOpen && !isNameSet) {
      // Auto-generate a guest name
      const randomNames = ['Guest', 'Visitor', 'User', 'Anonymous'];
      const randomName = randomNames[Math.floor(Math.random() * randomNames.length)];
      const randomNumber = Math.floor(Math.random() * 9999) + 1;
      setGuestName(`${randomName}${randomNumber}`);
    }
  }, [isOpen, isNameSet]);

  const handleSetName = () => {
    if (guestName.trim()) {
      setIsNameSet(true);
      setIsConnected(true);
      
      // Add welcome message
      const welcomeMessage = {
        id: Date.now(),
        content: `Welcome ${guestName}! You're chatting as a guest. To access all features, consider creating an account.`,
        sender: { username: 'System', isSystem: true },
        timestamp: new Date(),
        type: 'system'
      };
      setMessages([welcomeMessage]);
    }
  };

  const handleSendMessage = (e) => {
    e.preventDefault();
    if (!inputText.trim() || !isConnected) return;

    const message = {
      id: Date.now(),
      content: inputText.trim(),
      sender: {
        id: guestId,
        username: guestName,
        isGuest: true
      },
      timestamp: new Date(),
      type: 'text'
    };

    setMessages(prev => [...prev, message]);
    setInputText('');

    // Simulate bot response for demo
    setTimeout(() => {
      const botResponse = {
        id: Date.now() + 1,
        content: "Thanks for your message! As a guest user, you have limited features. Would you like to create an account for the full experience?",
        sender: { username: 'Support Bot', isBot: true },
        timestamp: new Date(),
        type: 'bot',
        actions: [
          { text: 'Create Account', action: 'register' },
          { text: 'Continue as Guest', action: 'continue' }
        ]
      };
      setMessages(prev => [...prev, botResponse]);
    }, 1000);
  };

  const handleAction = (action) => {
    if (action === 'register') {
      onRegister();
    }
  };

  if (!isOpen) return null;

  return (
    <div className={`fixed bottom-4 right-4 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50 transition-all duration-200 ${isMinimized ? 'h-14' : 'h-96'}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 bg-blue-500 text-white rounded-t-lg">
        <div className="flex items-center space-x-2">
          <MessageCircle className="w-5 h-5" />
          <span className="font-medium">Guest Chat</span>
          <span className="text-xs bg-blue-600 px-2 py-1 rounded-full">Guest</span>
        </div>
        <div className="flex items-center space-x-1">
          <button
            onClick={() => setIsMinimized(!isMinimized)}
            className="p-1 hover:bg-blue-600 rounded"
          >
            {isMinimized ? <Maximize2 className="w-4 h-4" /> : <Minimize2 className="w-4 h-4" />}
          </button>
          <button
            onClick={onClose}
            className="p-1 hover:bg-blue-600 rounded"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>

      {!isMinimized && (
        <>
          {!isNameSet ? (
            /* Name Setup */
            <div className="p-4 space-y-4">
              <div className="text-center">
                <User className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Welcome to Guest Chat
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  Enter your name to start chatting. No registration required!
                </p>
              </div>

              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Your Name
                  </label>
                  <input
                    type="text"
                    value={guestName}
                    onChange={(e) => setGuestName(e.target.value)}
                    placeholder="Enter your name..."
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    onKeyPress={(e) => e.key === 'Enter' && handleSetName()}
                    autoFocus
                  />
                </div>

                <button
                  onClick={handleSetName}
                  disabled={!guestName.trim()}
                  className="w-full px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                >
                  Start Chatting
                </button>

                <div className="text-center">
                  <button
                    onClick={onRegister}
                    className="text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center justify-center space-x-1"
                  >
                    <UserPlus className="w-4 h-4" />
                    <span>Create Account Instead</span>
                  </button>
                </div>
              </div>

              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
                <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                  Guest Limitations
                </h4>
                <ul className="text-xs text-yellow-700 dark:text-yellow-300 space-y-1">
                  <li>• Limited to public rooms only</li>
                  <li>• No file uploads or voice messages</li>
                  <li>• Chat history not saved</li>
                  <li>• No VIP features</li>
                </ul>
              </div>
            </div>
          ) : (
            <>
              {/* Messages */}
              <div className="flex-1 overflow-y-auto p-3 space-y-3 h-64">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.sender?.isGuest ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-xs px-3 py-2 rounded-lg ${
                        message.sender?.isGuest
                          ? 'bg-blue-500 text-white'
                          : message.type === 'system'
                          ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                          : message.sender?.isBot
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
                      }`}
                    >
                      {!message.sender?.isGuest && (
                        <div className="text-xs font-medium mb-1 opacity-75">
                          {message.sender?.username}
                        </div>
                      )}
                      
                      <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                      
                      {message.actions && (
                        <div className="mt-2 space-y-1">
                          {message.actions.map((action, index) => (
                            <button
                              key={index}
                              onClick={() => handleAction(action.action)}
                              className="block w-full text-left text-xs bg-white/20 hover:bg-white/30 px-2 py-1 rounded"
                            >
                              {action.text}
                            </button>
                          ))}
                        </div>
                      )}
                      
                      <div className="text-xs opacity-70 mt-1">
                        {message.timestamp.toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Input */}
              <div className="p-3 border-t border-gray-200 dark:border-gray-700">
                <form onSubmit={handleSendMessage} className="flex space-x-2">
                  <input
                    type="text"
                    value={inputText}
                    onChange={(e) => setInputText(e.target.value)}
                    placeholder="Type your message..."
                    className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <button
                    type="submit"
                    disabled={!inputText.trim()}
                    className="px-3 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                  >
                    <Send className="w-4 h-4" />
                  </button>
                </form>
                
                <div className="mt-2 text-center">
                  <button
                    onClick={onRegister}
                    className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
                  >
                    Upgrade to full account for more features
                  </button>
                </div>
              </div>
            </>
          )}
        </>
      )}
    </div>
  );
};

// Guest Chat Widget for embedding on websites
export const GuestChatWidget = ({ onRegister, onLogin }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [hasNewMessage, setHasNewMessage] = useState(false);

  useEffect(() => {
    // Simulate proactive chat invitation
    const timer = setTimeout(() => {
      if (!isOpen) {
        setHasNewMessage(true);
      }
    }, 10000); // Show after 10 seconds

    return () => clearTimeout(timer);
  }, [isOpen]);

  const handleOpen = () => {
    setIsOpen(true);
    setHasNewMessage(false);
  };

  if (isOpen) {
    return (
      <GuestChat
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onRegister={onRegister}
      />
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* Proactive Message */}
      {hasNewMessage && (
        <div className="mb-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-3 max-w-xs">
          <div className="flex items-start space-x-2">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
              <MessageCircle className="w-4 h-4 text-white" />
            </div>
            <div className="flex-1">
              <p className="text-sm text-gray-900 dark:text-white">
                👋 Hi there! Need help? Start a quick chat with us!
              </p>
              <div className="flex space-x-2 mt-2">
                <button
                  onClick={handleOpen}
                  className="text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded"
                >
                  Start Chat
                </button>
                <button
                  onClick={() => setHasNewMessage(false)}
                  className="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Chat Button */}
      <button
        onClick={handleOpen}
        className="w-14 h-14 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-200 hover:scale-110 relative"
      >
        <MessageCircle className="w-6 h-6" />
        {hasNewMessage && (
          <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
            <span className="text-xs text-white">1</span>
          </div>
        )}
      </button>
    </div>
  );
};

export default GuestChat;
