from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.pagination import PageNumberPagination
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter
from django.db.models import Q, Count
from django.utils import timezone
from django.shortcuts import get_object_or_404
from datetime import datetime, timedelta

from .models import (
    LiveEvent, LiveRoom, LiveQuestion, LivePoll, LivePollVote,
    LiveEventRegistration, LiveViewer
)
from .serializers import (
    LiveEventSerializer, LiveRoomSerializer, LiveQuestionSerializer,
    LivePollSerializer, LiveEventRegistrationSerializer
)


class StandardResultsSetPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100


class LiveEventViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing live events
    """
    queryset = LiveEvent.objects.all()
    serializer_class = LiveEventSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'event_type', 'is_public', 'vip_only']
    search_fields = ['title', 'description']
    ordering_fields = ['created_at', 'scheduled_start']
    ordering = ['-scheduled_start']

    def get_queryset(self):
        user = self.request.user
        queryset = LiveEvent.objects.select_related('host').prefetch_related('registrations')

        if user.is_staff:
            return queryset
        else:
            # Filter based on user permissions
            return queryset.filter(
                Q(is_public=True) |
                Q(host=user) |
                Q(registrations__user=user, registrations__status='confirmed')
            ).distinct()

    def perform_create(self, serializer):
        serializer.save(host=self.request.user)

    @action(detail=True, methods=['post'])
    def start(self, request, pk=None):
        """Start live event"""
        event = self.get_object()

        if event.host != request.user and not request.user.is_staff:
            return Response({'error': 'Only event host can start event'},
                          status=status.HTTP_403_FORBIDDEN)

        if event.status != 'scheduled':
            return Response({'error': 'Event must be scheduled to start'},
                          status=status.HTTP_400_BAD_REQUEST)

        event.status = 'live'
        event.actual_start_time = timezone.now()
        event.save()

        # TODO: Create live stream session when LiveStreamSession model is implemented
        # stream_session = LiveStreamSession.objects.create(
        #     event=event,
        #     started_by=request.user,
        #     status='active'
        # )

        return Response({
            'message': 'Event started successfully'
        })

    @action(detail=True, methods=['post'])
    def end(self, request, pk=None):
        """End live event"""
        event = self.get_object()

        if event.host != request.user and not request.user.is_staff:
            return Response({'error': 'Only event host can end event'},
                          status=status.HTTP_403_FORBIDDEN)

        if event.status != 'live':
            return Response({'error': 'Event must be live to end'},
                          status=status.HTTP_400_BAD_REQUEST)

        event.status = 'completed'
        event.actual_end_time = timezone.now()
        event.save()

        # TODO: End stream session when LiveStreamSession model is implemented
        # stream_sessions = LiveStreamSession.objects.filter(event=event, status='active')
        # for session in stream_sessions:
        #     session.status = 'ended'
        #     session.ended_at = timezone.now()
        #     session.save()

        return Response({'message': 'Event ended successfully'})

    @action(detail=True, methods=['get'])
    def analytics(self, request, pk=None):
        """Get event analytics"""
        event = self.get_object()

        if event.host != request.user and not request.user.is_staff:
            return Response({'error': 'Permission denied'}, status=status.HTTP_403_FORBIDDEN)

        # Calculate analytics
        total_registrations = event.registrations.count()
        confirmed_registrations = event.registrations.filter(status='confirmed').count()
        # TODO: Add actual_attendees when LiveEventAttendance model is implemented
        # actual_attendees = event.attendances.filter(attended=True).count()

        # TODO: Add peak viewers when LiveStreamSession model is implemented
        # peak_viewers = event.stream_sessions.aggregate(
        #     peak=Count('concurrent_viewers')
        # )['peak'] or 0
        peak_viewers = 0

        # Questions and polls
        total_questions = event.questions.count()
        answered_questions = event.questions.filter(is_answered=True).count()
        total_polls = event.polls.count()

        return Response({
            'registrations': {
                'total': total_registrations,
                'confirmed': confirmed_registrations,
                'attendance_rate': 0  # TODO: Calculate when attendance tracking is implemented
            },
            'engagement': {
                'peak_viewers': peak_viewers,
                'total_questions': total_questions,
                'answered_questions': answered_questions,
                'total_polls': total_polls
            },
            'duration': {
                'scheduled_duration': event.duration_minutes,
                'actual_duration': self.calculate_actual_duration(event)
            }
        })

    def calculate_actual_duration(self, event):
        """Calculate actual event duration in minutes"""
        if event.actual_start_time and event.actual_end_time:
            duration = event.actual_end_time - event.actual_start_time
            return int(duration.total_seconds() / 60)
        return 0


class LiveRoomViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing live rooms
    """
    queryset = LiveRoom.objects.all()
    serializer_class = LiveRoomSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['is_active', 'is_featured']
    search_fields = ['name', 'description']
    ordering_fields = ['created_at', 'current_viewers']
    ordering = ['-current_viewers']

    def get_queryset(self):
        user = self.request.user
        queryset = LiveRoom.objects.select_related('created_by')

        if user.is_staff:
            return queryset
        else:
            # Filter based on user permissions
            return queryset.filter(is_active=True)

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def join(self, request, pk=None):
        """Join live room"""
        room = self.get_object()
        user = request.user



        # Check capacity
        if room.max_viewers and room.current_viewers >= room.max_viewers:
            return Response({'error': 'Room is at capacity'},
                          status=status.HTTP_400_BAD_REQUEST)

        # Increment viewer count
        room.current_viewers += 1
        room.save()

        return Response({
            'message': f'Joined {room.name}',
            'room_id': room.id,
            'current_viewers': room.current_viewers
        })

    @action(detail=True, methods=['post'])
    def leave(self, request, pk=None):
        """Leave live room"""
        room = self.get_object()

        # Decrement viewer count
        if room.current_viewers > 0:
            room.current_viewers -= 1
            room.save()

        return Response({
            'message': f'Left {room.name}',
            'current_viewers': room.current_viewers
        })


class LiveQuestionViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing live questions
    """
    queryset = LiveQuestion.objects.all()
    serializer_class = LiveQuestionSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['event', 'is_answered', 'is_featured']
    ordering_fields = ['created_at', 'upvotes']
    ordering = ['-upvotes', '-created_at']

    def get_queryset(self):
        user = self.request.user
        if user.is_staff:
            return LiveQuestion.objects.select_related('event', 'asked_by').all()
        else:
            # Users can see questions from events they have access to
            accessible_events = LiveEvent.objects.filter(
                Q(is_public=True) |
                Q(host=user) |
                Q(registrations__user=user, registrations__status='confirmed')
            ).values_list('id', flat=True)

            return LiveQuestion.objects.filter(
                event_id__in=accessible_events
            ).select_related('event', 'asked_by')

    def perform_create(self, serializer):
        serializer.save(asked_by=self.request.user)

    @action(detail=True, methods=['post'])
    def upvote(self, request, pk=None):
        """Upvote a question"""
        question = self.get_object()
        question.upvotes += 1
        question.save()

        return Response({
            'message': 'Question upvoted',
            'upvotes': question.upvotes
        })

    @action(detail=True, methods=['post'])
    def answer(self, request, pk=None):
        """Answer a question (host only)"""
        question = self.get_object()
        answer = request.data.get('answer')

        if not answer:
            return Response({'error': 'Answer is required'},
                          status=status.HTTP_400_BAD_REQUEST)

        # Check if user is event host
        if question.event.host != request.user and not request.user.is_staff:
            return Response({'error': 'Only event host can answer questions'},
                          status=status.HTTP_403_FORBIDDEN)

        question.answer = answer
        question.is_answered = True
        question.answered_at = timezone.now()
        question.save()

        return Response({'message': 'Question answered successfully'})


class LivePollViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing live polls
    """
    queryset = LivePoll.objects.all()
    serializer_class = LivePollSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['event', 'is_active']
    ordering_fields = ['created_at']
    ordering = ['-created_at']

    def get_queryset(self):
        user = self.request.user
        if user.is_staff:
            return LivePoll.objects.select_related('event', 'created_by').prefetch_related('options')
        else:
            # Users can see polls from events they have access to
            accessible_events = LiveEvent.objects.filter(
                Q(is_public=True) |
                Q(host=user) |
                Q(registrations__user=user, registrations__status='confirmed')
            ).values_list('id', flat=True)

            return LivePoll.objects.filter(
                event_id__in=accessible_events
            ).select_related('event', 'created_by').prefetch_related('options')

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def vote(self, request, pk=None):
        """Vote on a poll option"""
        poll = self.get_object()
        option_id = request.data.get('option_id')

        if not option_id:
            return Response({'error': 'Option ID is required'},
                          status=status.HTTP_400_BAD_REQUEST)

        if not poll.is_active:
            return Response({'error': 'Poll is not active'},
                          status=status.HTTP_400_BAD_REQUEST)

        try:
            option = poll.options.get(id=option_id)
            option.vote_count += 1
            option.save()

            return Response({
                'message': 'Vote recorded',
                'option': option.text,
                'vote_count': option.vote_count
            })

        except LivePollOption.DoesNotExist:
            return Response({'error': 'Invalid option'},
                          status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'])
    def results(self, request, pk=None):
        """Get poll results"""
        poll = self.get_object()

        options_data = []
        total_votes = 0

        for option in poll.options.all():
            total_votes += option.vote_count
            options_data.append({
                'id': option.id,
                'text': option.text,
                'vote_count': option.vote_count
            })

        # Calculate percentages
        for option_data in options_data:
            if total_votes > 0:
                option_data['percentage'] = (option_data['vote_count'] / total_votes) * 100
            else:
                option_data['percentage'] = 0

        return Response({
            'poll_id': poll.id,
            'question': poll.question,
            'total_votes': total_votes,
            'options': options_data
        })


# Additional views for live functionality
class MyEventsView(APIView):
    """
    Get current user's events
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        user = request.user

        # Events hosted by user
        hosted_events = LiveEvent.objects.filter(host=user).order_by('-start_time')

        # Events user is registered for
        registered_events = LiveEvent.objects.filter(
            registrations__user=user,
            registrations__status='confirmed'
        ).order_by('-start_time')

        return Response({
            'hosted_events': LiveEventSerializer(hosted_events, many=True, context={'request': request}).data,
            'registered_events': LiveEventSerializer(registered_events, many=True, context={'request': request}).data
        })


class JoinEventView(APIView):
    """
    Join a live event
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, event_id):
        try:
            event = LiveEvent.objects.get(id=event_id)
        except LiveEvent.DoesNotExist:
            return Response({'error': 'Event not found'}, status=status.HTTP_404_NOT_FOUND)

        # Check VIP requirements
        if event.vip_only and not request.user.is_vip:
            return Response({'error': 'VIP subscription required'}, status=status.HTTP_403_FORBIDDEN)

        # Check capacity
        if event.max_attendees and event.registrations.filter(status='confirmed').count() >= event.max_attendees:
            return Response({'error': 'Event is at capacity'}, status=status.HTTP_400_BAD_REQUEST)

        # Create or update registration
        registration, created = LiveEventRegistration.objects.get_or_create(
            user=request.user,
            event=event,
            defaults={'status': 'confirmed'}
        )

        if not created and registration.status != 'confirmed':
            registration.status = 'confirmed'
            registration.save()

        return Response({'message': 'Successfully joined event'})


class LeaveEventView(APIView):
    """
    Leave a live event
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, event_id):
        try:
            registration = LiveEventRegistration.objects.get(
                user=request.user,
                event_id=event_id
            )
            registration.status = 'cancelled'
            registration.save()

            return Response({'message': 'Successfully left event'})

        except LiveEventRegistration.DoesNotExist:
            return Response({'error': 'Not registered for this event'}, status=status.HTTP_400_BAD_REQUEST)


class StartEventView(APIView):
    """
    Start a live event
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, event_id):
        try:
            event = LiveEvent.objects.get(id=event_id, host=request.user)
        except LiveEvent.DoesNotExist:
            return Response({'error': 'Event not found or access denied'}, status=status.HTTP_404_NOT_FOUND)

        if event.status != 'scheduled':
            return Response({'error': 'Event must be scheduled to start'}, status=status.HTTP_400_BAD_REQUEST)

        event.status = 'live'
        event.actual_start_time = timezone.now()
        event.save()

        return Response({'message': 'Event started successfully'})


class EndEventView(APIView):
    """
    End a live event
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, event_id):
        try:
            event = LiveEvent.objects.get(id=event_id, host=request.user)
        except LiveEvent.DoesNotExist:
            return Response({'error': 'Event not found or access denied'}, status=status.HTTP_404_NOT_FOUND)

        if event.status != 'live':
            return Response({'error': 'Event must be live to end'}, status=status.HTTP_400_BAD_REQUEST)

        event.status = 'completed'
        event.actual_end_time = timezone.now()
        event.save()

        return Response({'message': 'Event ended successfully'})


class JoinLiveRoomView(APIView):
    """
    Join a live room
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, room_id):
        try:
            room = LiveRoom.objects.get(id=room_id)
        except LiveRoom.DoesNotExist:
            return Response({'error': 'Room not found'}, status=status.HTTP_404_NOT_FOUND)



        # Check capacity
        if room.max_viewers and room.current_viewers >= room.max_viewers:
            return Response({'error': 'Room is at capacity'}, status=status.HTTP_400_BAD_REQUEST)

        # Increment viewer count
        room.current_viewers += 1
        room.save()

        return Response({
            'message': f'Joined {room.name}',
            'current_viewers': room.current_viewers
        })


class LeaveLiveRoomView(APIView):
    """
    Leave a live room
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, room_id):
        try:
            room = LiveRoom.objects.get(id=room_id)
        except LiveRoom.DoesNotExist:
            return Response({'error': 'Room not found'}, status=status.HTTP_404_NOT_FOUND)

        # Decrement viewer count
        if room.current_viewers > 0:
            room.current_viewers -= 1
            room.save()

        return Response({
            'message': f'Left {room.name}',
            'current_viewers': room.current_viewers
        })


class StartStreamView(APIView):
    """
    Start live streaming
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        event_id = request.data.get('event_id')

        try:
            event = LiveEvent.objects.get(id=event_id, host=request.user)
        except LiveEvent.DoesNotExist:
            return Response({'error': 'Event not found or access denied'}, status=status.HTTP_404_NOT_FOUND)

        # TODO: Create stream session when LiveStreamSession model is implemented
        # stream_session = LiveStreamSession.objects.create(
        #     event=event,
        #     started_by=request.user,
        #     status='active'
        # )

        return Response({
            'message': 'Stream started successfully'
            # TODO: Add stream_session_id and stream_url when implemented
        })


class StopStreamView(APIView):
    """
    Stop live streaming
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        session_id = request.data.get('session_id')

        # TODO: Implement when LiveStreamSession model is available
        # try:
        #     session = LiveStreamSession.objects.get(
        #         id=session_id,
        #         started_by=request.user,
        #         status='active'
        #     )
        # except LiveStreamSession.DoesNotExist:
        #     return Response({'error': 'Stream session not found'}, status=status.HTTP_404_NOT_FOUND)
        #
        # session.status = 'ended'
        # session.ended_at = timezone.now()
        # session.save()

        return Response({'message': 'Stream stopped successfully'})


class LiveAnalyticsView(APIView):
    """
    Get live events analytics
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        user = request.user

        # Only hosts can see analytics for their events
        if not user.is_staff:
            events = LiveEvent.objects.filter(host=user)
        else:
            events = LiveEvent.objects.all()

        # Calculate analytics
        total_events = events.count()
        live_events = events.filter(status='live').count()
        completed_events = events.filter(status='completed').count()

        # Registration stats
        total_registrations = LiveEventRegistration.objects.filter(
            event__in=events,
            status='confirmed'
        ).count()

        # TODO: Add attendance stats when LiveEventAttendance model is implemented
        # total_attendances = LiveEventAttendance.objects.filter(
        #     event__in=events,
        #     attended=True
        # ).count()
        total_attendances = 0

        return Response({
            'total_events': total_events,
            'live_events': live_events,
            'completed_events': completed_events,
            'total_registrations': total_registrations,
            'total_attendances': total_attendances,
            'attendance_rate': (total_attendances / total_registrations * 100) if total_registrations > 0 else 0
        })
