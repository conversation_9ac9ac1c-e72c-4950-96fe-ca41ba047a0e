from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    LiveEvent, LiveRoom, LiveQuestion, LivePoll, LivePollVote,
    LiveEventRegistration, LiveViewer
)

User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'display_name', 'avatar', 'is_vip', 'user_type']
        read_only_fields = ['id']


class LiveEventSerializer(serializers.ModelSerializer):
    host = UserSerializer(read_only=True)
    registration_count = serializers.SerializerMethodField()
    attendee_count = serializers.SerializerMethodField()
    is_registered = serializers.SerializerMethodField()
    
    class Meta:
        model = LiveEvent
        fields = [
            'id', 'title', 'description', 'host', 'scheduled_start', 'scheduled_end',
            'event_type', 'is_public', 'vip_only',
            'max_participants', 'requires_registration', 'status',
            'recording_url', 'theme_color', 'actual_start',
            'actual_end', 'created_at', 'updated_at', 'registration_count',
            'attendee_count', 'is_registered'
        ]
        read_only_fields = [
            'id', 'host', 'actual_start', 'actual_end',
            'created_at', 'updated_at'
        ]
    
    def get_registration_count(self, obj):
        return obj.registrations.filter(status='confirmed').count()
    
    def get_attendee_count(self, obj):
        # TODO: Implement when LiveEventAttendance model is available
        return 0
    
    def get_is_registered(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.registrations.filter(
                user=request.user, 
                status='confirmed'
            ).exists()
        return False


class LiveRoomSerializer(serializers.ModelSerializer):
    created_by = UserSerializer(read_only=True)
    
    class Meta:
        model = LiveRoom
        fields = [
            'id', 'name', 'description', 'room_type', 'is_active', 'is_vip_only',
            'max_viewers', 'current_viewers', 'stream_key', 'stream_url',
            'chat_enabled', 'moderation_enabled', 'created_by', 'created_at',
            'updated_at'
        ]
        read_only_fields = [
            'id', 'created_by', 'current_viewers', 'stream_key', 
            'created_at', 'updated_at'
        ]


class LiveQuestionSerializer(serializers.ModelSerializer):
    asked_by = UserSerializer(read_only=True)
    event = serializers.StringRelatedField(read_only=True)
    
    class Meta:
        model = LiveQuestion
        fields = [
            'id', 'event', 'asked_by', 'question', 'answer', 'upvotes',
            'is_answered', 'is_featured', 'answered_at', 'created_at'
        ]
        read_only_fields = [
            'id', 'asked_by', 'upvotes', 'is_answered', 'answered_at', 'created_at'
        ]


# TODO: Implement when LivePollOption model is available
# class LivePollOptionSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = LivePollOption
#         fields = ['id', 'text', 'vote_count']
#         read_only_fields = ['id', 'vote_count']


class LivePollSerializer(serializers.ModelSerializer):
    created_by = UserSerializer(read_only=True)
    event = serializers.StringRelatedField(read_only=True)
    # options = LivePollOptionSerializer(many=True, read_only=True)  # TODO: Enable when LivePollOption is available
    total_votes = serializers.SerializerMethodField()

    class Meta:
        model = LivePoll
        fields = [
            'id', 'event', 'created_by', 'question', 'is_active',
            'allow_multiple_votes', 'show_results_immediately', 'ends_at',
            'created_at', 'total_votes'
        ]
        read_only_fields = ['id', 'created_by', 'created_at']

    def get_total_votes(self, obj):
        # TODO: Calculate from LivePollOption when available
        return 0


class LiveEventRegistrationSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    event = LiveEventSerializer(read_only=True)
    
    class Meta:
        model = LiveEventRegistration
        fields = [
            'id', 'user', 'event', 'status', 'registered_at', 'confirmation_sent',
            'reminder_sent', 'notes'
        ]
        read_only_fields = [
            'id', 'user', 'registered_at', 'confirmation_sent', 'reminder_sent'
        ]


# TODO: Implement when LiveEventAttendance model is available
# class LiveEventAttendanceSerializer(serializers.ModelSerializer):
#     user = UserSerializer(read_only=True)
#     event = LiveEventSerializer(read_only=True)
#
#     class Meta:
#         model = LiveEventAttendance
#         fields = [
#             'id', 'user', 'event', 'joined_at', 'left_at', 'duration_minutes',
#             'attended', 'engagement_score'
#         ]
#         read_only_fields = [
#             'id', 'user', 'joined_at', 'left_at', 'duration_minutes', 'attended'
#         ]


# TODO: Implement when LiveStreamSession model is available
# class LiveStreamSessionSerializer(serializers.ModelSerializer):
#     event = LiveEventSerializer(read_only=True)
#     started_by = UserSerializer(read_only=True)
#
#     class Meta:
#         model = LiveStreamSession
#         fields = [
#             'id', 'event', 'started_by', 'status', 'stream_key', 'stream_url',
#             'recording_url', 'peak_viewers', 'total_viewers', 'duration_minutes',
#             'started_at', 'ended_at'
#         ]
#         read_only_fields = [
#             'id', 'started_by', 'stream_key', 'peak_viewers', 'total_viewers',
#             'duration_minutes', 'started_at', 'ended_at'
#         ]


# Additional serializers for specific use cases
class LiveEventCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = LiveEvent
        fields = [
            'title', 'description', 'scheduled_start', 'scheduled_end',
            'event_type', 'is_public', 'vip_only', 'max_participants',
            'requires_registration'
        ]

    def validate(self, data):
        if data['scheduled_start'] >= data['scheduled_end']:
            raise serializers.ValidationError("End time must be after start time")
        return data


class LiveRoomCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = LiveRoom
        fields = [
            'name', 'description', 'room_type', 'is_vip_only', 'max_viewers',
            'chat_enabled', 'moderation_enabled'
        ]


class LiveQuestionCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = LiveQuestion
        fields = ['question']
    
    def validate_question(self, value):
        if len(value.strip()) < 10:
            raise serializers.ValidationError("Question must be at least 10 characters long")
        return value


class LivePollCreateSerializer(serializers.ModelSerializer):
    options = serializers.ListField(
        child=serializers.CharField(max_length=200),
        min_length=2,
        max_length=10
    )
    
    class Meta:
        model = LivePoll
        fields = [
            'question', 'options', 'allow_multiple_votes', 
            'show_results_immediately', 'ends_at'
        ]
    
    def create(self, validated_data):
        options_data = validated_data.pop('options')
        poll = LivePoll.objects.create(**validated_data)

        # TODO: Create poll options when LivePollOption model is available
        # for option_text in options_data:
        #     LivePollOption.objects.create(poll=poll, text=option_text)

        return poll


class LiveEventAnalyticsSerializer(serializers.Serializer):
    total_registrations = serializers.IntegerField()
    confirmed_registrations = serializers.IntegerField()
    actual_attendees = serializers.IntegerField()
    peak_viewers = serializers.IntegerField()
    average_watch_time = serializers.FloatField()
    engagement_rate = serializers.FloatField()
    questions_asked = serializers.IntegerField()
    polls_created = serializers.IntegerField()
    chat_messages = serializers.IntegerField()
    recording_views = serializers.IntegerField()


class LiveDashboardSerializer(serializers.Serializer):
    upcoming_events = LiveEventSerializer(many=True)
    live_events = LiveEventSerializer(many=True)
    recent_events = LiveEventSerializer(many=True)
    active_rooms = LiveRoomSerializer(many=True)
    my_registrations = LiveEventRegistrationSerializer(many=True)
    analytics = LiveEventAnalyticsSerializer()
