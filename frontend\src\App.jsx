import React, { useEffect, useState, createContext, useContext } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';

// Page imports
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import ChatPage from './pages/ChatPage';
import LandingPage from './pages/LandingPage';
import VIPPage from './pages/VIPPage';
import LiveEventsPage from './pages/LiveEventsPage';
import AnalyticsPage from './pages/AnalyticsPage';
import AdminPage from './pages/AdminPage';

// Component imports
import ProtectedRoute from './components/auth/ProtectedRoute';
import LoadingSpinner from './components/ui/LoadingSpinner';
import NotificationCenter from './components/notifications/NotificationCenter';
import VoiceCallModal from './components/calls/VoiceCallModal';
import ScreenShareModal from './components/calls/ScreenShareModal';
import { GuestChatWidget } from './components/chat/GuestChat';

// Context for global app state
const AppContext = createContext();

export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within AppProvider');
  }
  return context;
};

function App() {
  // Global app state
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [theme, setTheme] = useState('light');
  const [notifications, setNotifications] = useState([]);
  const [activeCall, setActiveCall] = useState(null);
  const [screenShare, setScreenShare] = useState(null);
  const [onlineUsers, setOnlineUsers] = useState([]);
  const [typingUsers, setTypingUsers] = useState({});
  const [unreadCounts, setUnreadCounts] = useState({});

  // WebSocket connection
  const [socket, setSocket] = useState(null);
  const [isConnected, setIsConnected] = useState(false);

  // Initialize app
  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Check for existing session
      const savedUser = localStorage.getItem('chatflow_user');
      const savedToken = localStorage.getItem('chatflow_token');

      if (savedUser && savedToken) {
        const userData = JSON.parse(savedUser);
        setUser(userData);
        setIsAuthenticated(true);

        // Initialize WebSocket connection
        await connectWebSocket(savedToken);
      }

      // Load theme preference
      const savedTheme = localStorage.getItem('chatflow_theme') || 'light';
      setTheme(savedTheme);
      document.documentElement.classList.toggle('dark', savedTheme === 'dark');

    } catch (error) {
      console.error('Failed to initialize app:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const connectWebSocket = async (token) => {
    try {
      // For now, disable WebSocket connection since backend WebSocket needs room_id
      // TODO: Implement proper WebSocket connection with room-specific connections
      console.log('WebSocket connection disabled - will implement room-specific connections');
      setIsConnected(false);
      return;

      const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8000';
      const ws = new WebSocket(`${wsUrl}/ws/chat/?token=${token}`);

      ws.onopen = () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        setSocket(ws);
      };

      ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        handleWebSocketMessage(data);
      };

      ws.onclose = () => {
        console.log('WebSocket disconnected');
        setIsConnected(false);
        setSocket(null);

        // Attempt to reconnect after 3 seconds
        setTimeout(() => {
          if (isAuthenticated) {
            connectWebSocket(token);
          }
        }, 3000);
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
      };

    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
    }
  };

  const handleWebSocketMessage = (data) => {
    switch (data.type) {
      case 'chat_message':
        // Handle new message
        if (data.message) {
          // Update unread counts
          setUnreadCounts(prev => ({
            ...prev,
            [data.message.room]: (prev[data.message.room] || 0) + 1
          }));

          // Show notification if not in the room
          addNotification({
            type: 'message',
            title: `New message from ${data.message.sender.display_name || data.message.sender.username}`,
            message: data.message.content,
            roomId: data.message.room
          });
        }
        break;

      case 'user_joined':
        // Update online users
        if (data.user && !onlineUsers.find(u => u.id === data.user.id)) {
          setOnlineUsers(prev => [...prev, data.user]);
        }
        break;

      case 'user_left':
        // Remove from online users
        if (data.user_id) {
          setOnlineUsers(prev => prev.filter(u => u.id !== data.user_id));
        }
        break;

      case 'typing_start':
        // Add to typing users
        if (data.user && data.room_id) {
          setTypingUsers(prev => ({
            ...prev,
            [data.room_id]: [...(prev[data.room_id] || []), data.user]
          }));
        }
        break;

      case 'typing_stop':
        // Remove from typing users
        if (data.user_id && data.room_id) {
          setTypingUsers(prev => ({
            ...prev,
            [data.room_id]: (prev[data.room_id] || []).filter(u => u.id !== data.user_id)
          }));
        }
        break;

      case 'voice_call_incoming':
        // Handle incoming voice call
        setActiveCall({
          type: 'incoming',
          caller: data.caller,
          roomId: data.room_id,
          callType: data.call_type
        });
        break;

      case 'screen_share_started':
        // Handle screen share
        setScreenShare({
          sharedBy: data.shared_by,
          roomId: data.room_id,
          streamUrl: data.stream_url
        });
        break;

      case 'notification':
        // Handle system notifications
        addNotification({
          type: data.notification_type || 'info',
          title: data.title,
          message: data.message
        });
        break;

      default:
        console.log('Unknown WebSocket message type:', data.type);
    }
  };

  const login = async (credentials) => {
    try {
      setIsLoading(true);

      // Try API call first, fallback to mock if backend not available
      try {
        const response = await fetch('http://localhost:8000/api/auth/login/', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            username: credentials.email, // Backend can handle email as username
            password: credentials.password
          })
        });

        if (response.ok) {
          const data = await response.json();

          // Save user data
          localStorage.setItem('chatflow_user', JSON.stringify(data.user));
          localStorage.setItem('chatflow_token', data.tokens.access);
          localStorage.setItem('chatflow_refresh_token', data.tokens.refresh);

          setUser(data.user);
          setIsAuthenticated(true);

          // Connect WebSocket
          await connectWebSocket(data.tokens.access);

          addNotification({
            type: 'success',
            title: 'Welcome!',
            message: data.message || 'Successfully logged in'
          });

          return true;
        } else {
          const error = await response.json();
          throw new Error(error.error || 'Login failed');
        }
      } catch (fetchError) {
        // Backend not available, use mock authentication
        console.log('Backend not available, using mock authentication');

        // Mock user data
        const mockUser = {
          id: 1,
          username: credentials.email.split('@')[0],
          email: credentials.email,
          display_name: credentials.email.split('@')[0],
          is_staff: credentials.email === '<EMAIL>',
          is_vip: credentials.email.includes('vip'),
          avatar: null,
          date_joined: new Date().toISOString(),
          last_login: new Date().toISOString()
        };

        const mockToken = 'mock_token_' + Date.now();

        // Save user data
        localStorage.setItem('chatflow_user', JSON.stringify(mockUser));
        localStorage.setItem('chatflow_token', mockToken);

        setUser(mockUser);
        setIsAuthenticated(true);

        addNotification({
          type: 'success',
          title: 'Welcome!',
          message: 'Successfully logged in (demo mode)'
        });

        return true;
      }
    } catch (error) {
      console.error('Login error:', error);
      addNotification({
        type: 'error',
        title: 'Login Failed',
        message: error.message
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData) => {
    try {
      setIsLoading(true);

      // Validate passwords match
      if (userData.password !== userData.confirmPassword) {
        throw new Error('Passwords do not match');
      }

      // Try API call first, fallback to mock if backend not available
      try {
        const response = await fetch('http://localhost:8000/api/auth/register/', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            username: userData.username,
            email: userData.email,
            password: userData.password,
            display_name: userData.displayName
          })
        });

        if (response.ok) {
          const data = await response.json();

          // Save user data
          localStorage.setItem('chatflow_user', JSON.stringify(data.user));
          localStorage.setItem('chatflow_token', data.tokens.access);
          localStorage.setItem('chatflow_refresh_token', data.tokens.refresh);

          setUser(data.user);
          setIsAuthenticated(true);

          // Connect WebSocket
          await connectWebSocket(data.tokens.access);

          addNotification({
            type: 'success',
            title: 'Welcome!',
            message: data.message || 'Account created successfully'
          });

          return true;
        } else {
          const error = await response.json();
          throw new Error(error.error || 'Registration failed');
        }
      } catch (fetchError) {
        // Backend not available, use mock registration
        console.log('Backend not available, using mock registration');

        // Mock user data
        const mockUser = {
          id: Date.now(),
          username: userData.username,
          email: userData.email,
          display_name: userData.displayName || userData.username,
          is_staff: false,
          is_vip: false,
          avatar: null,
          date_joined: new Date().toISOString(),
          last_login: new Date().toISOString()
        };

        const mockToken = 'mock_token_' + Date.now();

        // Save user data
        localStorage.setItem('chatflow_user', JSON.stringify(mockUser));
        localStorage.setItem('chatflow_token', mockToken);

        setUser(mockUser);
        setIsAuthenticated(true);

        addNotification({
          type: 'success',
          title: 'Welcome!',
          message: 'Account created successfully (demo mode)'
        });

        return true;
      }
    } catch (error) {
      console.error('Registration error:', error);
      addNotification({
        type: 'error',
        title: 'Registration Failed',
        message: error.message
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      // Try to call backend logout endpoint
      const token = localStorage.getItem('chatflow_token');
      const refreshToken = localStorage.getItem('chatflow_refresh_token');

      if (token) {
        try {
          await fetch('http://localhost:8000/api/auth/logout/', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              refresh_token: refreshToken
            })
          });
        } catch (fetchError) {
          console.log('Backend logout failed, continuing with local logout');
        }
      }

      // Close WebSocket
      if (socket) {
        socket.close();
      }

      // Clear local storage
      localStorage.removeItem('chatflow_user');
      localStorage.removeItem('chatflow_token');
      localStorage.removeItem('chatflow_refresh_token');

      // Reset state
      setUser(null);
      setIsAuthenticated(false);
      setSocket(null);
      setIsConnected(false);
      setOnlineUsers([]);
      setTypingUsers({});
      setUnreadCounts({});
      setActiveCall(null);
      setScreenShare(null);

      addNotification({
        type: 'success',
        title: 'Logged Out',
        message: 'You have been successfully logged out'
      });

    } catch (error) {
      console.error('Logout error:', error);
      addNotification({
        type: 'error',
        title: 'Logout Error',
        message: 'There was an issue logging out'
      });
    }
  };

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    localStorage.setItem('chatflow_theme', newTheme);
    document.documentElement.classList.toggle('dark', newTheme === 'dark');
  };

  const addNotification = (notification) => {
    const id = Date.now().toString();
    const newNotification = { id, ...notification, timestamp: new Date() };

    setNotifications(prev => [newNotification, ...prev.slice(0, 49)]); // Keep last 50

    // Auto-remove after 5 seconds for non-persistent notifications
    if (!notification.persistent) {
      setTimeout(() => {
        removeNotification(id);
      }, 5000);
    }
  };

  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const sendMessage = (roomId, content, options = {}) => {
    if (socket && isConnected) {
      socket.send(JSON.stringify({
        type: 'chat_message',
        room_id: roomId,
        content,
        ...options
      }));
    }
  };

  const startTyping = (roomId) => {
    if (socket && isConnected) {
      socket.send(JSON.stringify({
        type: 'typing_start',
        room_id: roomId
      }));
    }
  };

  const stopTyping = (roomId) => {
    if (socket && isConnected) {
      socket.send(JSON.stringify({
        type: 'typing_stop',
        room_id: roomId
      }));
    }
  };

  const markAsRead = (roomId, messageId) => {
    if (socket && isConnected) {
      socket.send(JSON.stringify({
        type: 'mark_read',
        room_id: roomId,
        message_id: messageId
      }));

      // Update local unread count
      setUnreadCounts(prev => ({
        ...prev,
        [roomId]: Math.max(0, (prev[roomId] || 0) - 1)
      }));
    }
  };

  // App context value
  const contextValue = {
    // User state
    user,
    isAuthenticated,
    isLoading,

    // Theme
    theme,
    toggleTheme,

    // WebSocket
    socket,
    isConnected,
    onlineUsers,
    typingUsers,
    unreadCounts,

    // Notifications
    notifications,
    addNotification,
    removeNotification,

    // Calls & Screen Share
    activeCall,
    setActiveCall,
    screenShare,
    setScreenShare,

    // Actions
    login,
    register,
    logout,
    sendMessage,
    startTyping,
    stopTyping,
    markAsRead,
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">Loading ChatFlow Pro...</p>
        </div>
      </div>
    );
  }

  return (
    <AppContext.Provider value={contextValue}>
      <Router>
        <div className={`min-h-screen ${theme === 'dark' ? 'dark' : ''}`}>
          <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors">
            <Routes>
              {/* Public routes */}
              <Route
                path="/"
                element={
                  isAuthenticated ? <Navigate to="/chat" replace /> : <Navigate to="/login" replace />
                }
              />
              <Route
                path="/login"
                element={
                  isAuthenticated ? <Navigate to="/chat" replace /> : <LoginPage />
                }
              />
              <Route
                path="/register"
                element={
                  isAuthenticated ? <Navigate to="/chat" replace /> : <RegisterPage />
                }
              />

              {/* Protected routes */}
              <Route
                path="/chat/*"
                element={
                  <ProtectedRoute>
                    <ChatPage />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/vip"
                element={
                  <ProtectedRoute>
                    <VIPPage />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/live"
                element={
                  <ProtectedRoute>
                    <LiveEventsPage />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/analytics"
                element={
                  <ProtectedRoute requireAdmin>
                    <AnalyticsPage />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/admin"
                element={
                  <ProtectedRoute requireAdmin>
                    <AdminPage />
                  </ProtectedRoute>
                }
              />

              {/* Catch all route */}
              <Route
                path="*"
                element={<Navigate to="/" replace />}
              />
            </Routes>

            {/* Global Components */}
            <NotificationCenter />

            {/* Voice Call Modal */}
            {activeCall && (
              <VoiceCallModal
                call={activeCall}
                onClose={() => setActiveCall(null)}
              />
            )}

            {/* Screen Share Modal */}
            {screenShare && (
              <ScreenShareModal
                screenShare={screenShare}
                onClose={() => setScreenShare(null)}
              />
            )}

            {/* Guest Chat Widget (for non-authenticated users) */}
            {!isAuthenticated && (
              <GuestChatWidget
                onRegister={() => window.location.href = '/register'}
                onLogin={() => window.location.href = '/login'}
              />
            )}

            {/* Toast Notifications */}
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                className: 'dark:bg-gray-800 dark:text-white',
                style: {
                  background: theme === 'dark' ? '#1f2937' : '#ffffff',
                  color: theme === 'dark' ? '#ffffff' : '#1f2937',
                },
              }}
            />
          </div>
        </div>
      </Router>
    </AppContext.Provider>
  );
}

export default App;
