import React, { useState, useEffect } from 'react';
import { useApp } from '../App';
import {
  Shield,
  Users,
  MessageCircle,
  Settings,
  Bot,
  Crown,
  Radio,
  AlertTriangle,
  CheckCircle,
  X,
  Edit,
  Trash2,
  Plus,
  Search,
  Filter,
  MoreVertical,
  Headphones
} from 'lucide-react';
import AgentQueue from '../components/admin/AgentQueue';

const AdminPage = () => {
  const { user, addNotification } = useApp();
  const [activeTab, setActiveTab] = useState('users');
  const [users, setUsers] = useState([]);
  const [rooms, setRooms] = useState([]);
  const [bots, setBots] = useState([]);
  const [reports, setReports] = useState([]);
  const [systemSettings, setSystemSettings] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedItems, setSelectedItems] = useState([]);

  useEffect(() => {
    loadAdminData();
  }, [activeTab]);

  const loadAdminData = async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('chatflow_token');

      if (!token) {
        addNotification({
          type: 'error',
          title: 'Authentication Error',
          message: 'Please log in to access admin features'
        });
        return;
      }

      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      };

      switch (activeTab) {
        case 'users':
          const usersResponse = await fetch('http://localhost:8000/api/admin/users/', { headers });
          if (usersResponse.ok) {
            const usersData = await usersResponse.json();
            setUsers(usersData.results || []);
          } else {
            setUsers([]);
          }
          break;

        case 'rooms':
          const roomsResponse = await fetch('http://localhost:8000/api/admin/rooms/', { headers });
          if (roomsResponse.ok) {
            const roomsData = await roomsResponse.json();
            setRooms(roomsData.results || []);
          } else {
            setRooms([]);
          }
          break;

        case 'bots':
          const botsResponse = await fetch('http://localhost:8000/api/admin/bots/', { headers });
          if (botsResponse.ok) {
            const botsData = await botsResponse.json();
            setBots(botsData.results || []);
          } else {
            setBots([]);
          }
          break;

        case 'reports':
          const reportsResponse = await fetch('http://localhost:8000/api/admin/reports/', { headers });
          if (reportsResponse.ok) {
            const reportsData = await reportsResponse.json();
            setReports(reportsData.results || []);
          } else {
            setReports([]);
          }
          break;

        case 'settings':
          const settingsResponse = await fetch('http://localhost:8000/api/admin/settings/', { headers });
          if (settingsResponse.ok) {
            const settingsData = await settingsResponse.json();
            setSystemSettings(settingsData);
          } else {
            setSystemSettings({});
          }
          break;
      }

    } catch (error) {
      console.error('Failed to load admin data:', error);
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to load admin data. Please check your connection.'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUserAction = async (userId, action) => {
    try {
      const token = localStorage.getItem('chatflow_token');
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      };

      let response;
      switch (action) {
        case 'edit':
          // Open edit modal (implement later)
          console.log('Edit user:', userId);
          return;
        case 'suspend':
          response = await fetch(`http://localhost:8000/api/admin/users/${userId}/suspend/`, {
            method: 'POST',
            headers
          });
          break;
        case 'activate':
          response = await fetch(`http://localhost:8000/api/admin/users/${userId}/activate/`, {
            method: 'POST',
            headers
          });
          break;
        case 'delete':
          if (!window.confirm('Are you sure you want to delete this user?')) return;
          response = await fetch(`http://localhost:8000/api/admin/users/${userId}/`, {
            method: 'DELETE',
            headers
          });
          break;
        default:
          throw new Error(`Unknown action: ${action}`);
      }

      if (response && response.ok) {
        addNotification({
          type: 'success',
          title: 'Action Complete',
          message: `User ${action} successful`
        });
        loadAdminData();
      } else {
        throw new Error(`Failed to ${action} user`);
      }
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Error',
        message: error.message
      });
    }
  };

  const handleRoomAction = async (roomId, action) => {
    try {
      const token = localStorage.getItem('chatflow_token');
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      };

      let response;
      switch (action) {
        case 'edit':
          // Open edit modal (implement later)
          console.log('Edit room:', roomId);
          return;
        case 'delete':
          if (!window.confirm('Are you sure you want to delete this room?')) return;
          response = await fetch(`http://localhost:8000/api/admin/rooms/${roomId}/`, {
            method: 'DELETE',
            headers
          });
          break;
        default:
          throw new Error(`Unknown action: ${action}`);
      }

      if (response && response.ok) {
        addNotification({
          type: 'success',
          title: 'Action Complete',
          message: `Room ${action} successful`
        });
        loadAdminData();
      } else {
        throw new Error(`Failed to ${action} room`);
      }
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Error',
        message: error.message
      });
    }
  };

  const UsersTab = () => (
    <div className="space-y-6">
      {/* Users Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          User Management ({users.length})
        </h2>
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search users..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <button
            onClick={() => {
              // TODO: Implement add user modal
              addNotification({
                id: Date.now(),
                type: 'info',
                title: 'Add User',
                message: 'Add user functionality coming soon!'
              });
            }}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add User
          </button>
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Joined
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Last Active
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {users.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          {user.username[0].toUpperCase()}
                        </span>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {user.display_name || user.username}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {user.email}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      user.is_active 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    }`}>
                      {user.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      {user.user_type === 'admin' && (
                        <Shield className="w-4 h-4 text-red-500" />
                      )}
                      {user.is_vip && (
                        <Crown className="w-4 h-4 text-purple-500" />
                      )}
                      <span className="text-sm text-gray-900 dark:text-white capitalize">
                        {user.user_type}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {new Date(user.date_joined).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end gap-2">
                      <button
                        onClick={() => handleUserAction(user.id, 'edit')}
                        className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleUserAction(user.id, user.is_active ? 'suspend' : 'activate')}
                        className={`${
                          user.is_active 
                            ? 'text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300'
                            : 'text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300'
                        }`}
                      >
                        {user.is_active ? <X className="w-4 h-4" /> : <CheckCircle className="w-4 h-4" />}
                      </button>
                      <button
                        onClick={() => handleUserAction(user.id, 'delete')}
                        className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const RoomsTab = () => (
    <div className="space-y-6">
      {/* Rooms Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          Room Management ({rooms.length})
        </h2>
        <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2">
          <Plus className="w-4 h-4" />
          Create Room
        </button>
      </div>

      {/* Rooms Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {rooms.map((room) => (
          <div key={room.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border p-6">
            <div className="flex items-start justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {room.name}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 capitalize">
                  {room.room_type} • {room.is_public ? 'Public' : 'Private'}
                </p>
              </div>
              <div className="flex items-center gap-2">
                {room.is_vip_only && (
                  <Crown className="w-4 h-4 text-purple-500" />
                )}
                <button className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                  <MoreVertical className="w-4 h-4" />
                </button>
              </div>
            </div>
            
            <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2">
              {room.description}
            </p>
            
            <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
              <span>{room.member_count} members</span>
              <span>{room.message_count} messages</span>
            </div>
            
            <div className="flex items-center gap-2">
              <button
                onClick={() => handleRoomAction(room.id, 'edit')}
                className="flex-1 bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-900/30 text-blue-600 dark:text-blue-400 px-3 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                Edit
              </button>
              <button
                onClick={() => handleRoomAction(room.id, 'delete')}
                className="flex-1 bg-red-50 hover:bg-red-100 dark:bg-red-900/20 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400 px-3 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <Shield className="w-8 h-8 text-red-600" />
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Admin Dashboard
              </h1>
            </div>
            
            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Welcome, {user?.display_name || user?.username}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white dark:bg-gray-800 border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            {[
              { id: 'users', label: 'Users', icon: Users },
              { id: 'rooms', label: 'Rooms', icon: MessageCircle },
              { id: 'queue', label: 'Agent Queue', icon: Headphones },
              { id: 'bots', label: 'Bots', icon: Bot },
              { id: 'reports', label: 'Reports', icon: AlertTriangle },
              { id: 'settings', label: 'Settings', icon: Settings },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-red-500 text-red-600 dark:text-red-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'users' && <UsersTab />}
        {activeTab === 'rooms' && <RoomsTab />}
        {activeTab === 'queue' && <AgentQueue user={user} />}
        {/* Other tabs will be implemented similarly */}
      </div>
    </div>
  );
};

export default AdminPage;
