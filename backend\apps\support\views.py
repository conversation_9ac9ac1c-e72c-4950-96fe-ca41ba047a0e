from rest_framework import status, permissions, viewsets, generics
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter
from django.shortcuts import get_object_or_404
from django.db.models import Q
from django.utils import timezone

from .models import SupportTicket, SupportTicketMessage, FAQ, ContactMessage
from .serializers import (
    SupportTicketSerializer, SupportTicketListSerializer, SupportTicketMessageSerializer,
    FAQSerializer, ContactMessageSerializer, FAQVoteSerializer
)


class StandardResultsSetPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100


class SupportTicketViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing support tickets
    """
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'priority', 'category']
    search_fields = ['subject', 'description']
    ordering_fields = ['created_at', 'updated_at', 'priority']
    ordering = ['-created_at']

    def get_serializer_class(self):
        if self.action == 'list':
            return SupportTicketListSerializer
        return SupportTicketSerializer

    def get_queryset(self):
        user = self.request.user
        if user.is_staff:
            # Staff can see all tickets
            return SupportTicket.objects.all().select_related('user', 'assigned_to')
        else:
            # Users can only see their own tickets
            return SupportTicket.objects.filter(user=user).select_related('user', 'assigned_to')

    @action(detail=True, methods=['post'])
    def add_message(self, request, pk=None):
        """Add a message to a support ticket"""
        ticket = self.get_object()

        # Check permissions
        if not request.user.is_staff and ticket.user != request.user:
            return Response(
                {'error': 'You can only add messages to your own tickets'},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = SupportTicketMessageSerializer(
            data=request.data,
            context={'request': request}
        )

        if serializer.is_valid():
            message = serializer.save(ticket=ticket)

            # Update first response time if this is staff's first response
            if request.user.is_staff and not ticket.first_response_at:
                ticket.first_response_at = timezone.now()
                ticket.save(update_fields=['first_response_at'])

            return Response(serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['patch'])
    def update_status(self, request, pk=None):
        """Update ticket status (staff only)"""
        if not request.user.is_staff:
            return Response(
                {'error': 'Only staff can update ticket status'},
                status=status.HTTP_403_FORBIDDEN
            )

        ticket = self.get_object()
        new_status = request.data.get('status')

        if new_status not in dict(SupportTicket.STATUS_CHOICES):
            return Response(
                {'error': 'Invalid status'},
                status=status.HTTP_400_BAD_REQUEST
            )

        old_status = ticket.status
        ticket.status = new_status

        # Set resolved_at when ticket is resolved
        if new_status == 'resolved' and old_status != 'resolved':
            ticket.resolved_at = timezone.now()
        elif new_status != 'resolved':
            ticket.resolved_at = None

        ticket.save()

        serializer = self.get_serializer(ticket)
        return Response(serializer.data)

    @action(detail=True, methods=['patch'])
    def assign(self, request, pk=None):
        """Assign ticket to staff member (staff only)"""
        if not request.user.is_staff:
            return Response(
                {'error': 'Only staff can assign tickets'},
                status=status.HTTP_403_FORBIDDEN
            )

        ticket = self.get_object()
        assigned_to_id = request.data.get('assigned_to')

        if assigned_to_id:
            from django.contrib.auth import get_user_model
            User = get_user_model()
            try:
                assigned_user = User.objects.get(id=assigned_to_id, is_staff=True)
                ticket.assigned_to = assigned_user
            except User.DoesNotExist:
                return Response(
                    {'error': 'Invalid staff user'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        else:
            ticket.assigned_to = None

        ticket.save()

        serializer = self.get_serializer(ticket)
        return Response(serializer.data)


class SupportTicketListView(generics.ListCreateAPIView):
    """
    List support tickets for the current user or create new ticket
    """
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'priority', 'category']
    search_fields = ['subject', 'description']
    ordering = ['-created_at']

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return SupportTicketSerializer
        return SupportTicketListSerializer

    def get_queryset(self):
        user = self.request.user
        if user.is_staff:
            return SupportTicket.objects.all().select_related('user', 'assigned_to')
        else:
            return SupportTicket.objects.filter(user=user).select_related('user', 'assigned_to')


class CreateSupportTicketView(generics.CreateAPIView):
    """
    Create a new support ticket
    """
    serializer_class = SupportTicketSerializer
    permission_classes = [permissions.IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)


class SupportTicketDetailView(generics.RetrieveUpdateAPIView):
    """
    Get and update support ticket details
    """
    serializer_class = SupportTicketSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'
    lookup_url_kwarg = 'ticket_id'

    def get_queryset(self):
        user = self.request.user
        if user.is_staff:
            return SupportTicket.objects.all().select_related('user', 'assigned_to').prefetch_related('messages__sender')
        else:
            return SupportTicket.objects.filter(user=user).select_related('user', 'assigned_to').prefetch_related('messages__sender')


class FAQViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for FAQ management (read-only for users, full CRUD for staff)
    """
    serializer_class = FAQSerializer
    permission_classes = [permissions.AllowAny]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['category', 'is_published']
    search_fields = ['question', 'answer']
    ordering_fields = ['order', 'created_at', 'view_count']
    ordering = ['category', 'order']

    def get_queryset(self):
        if self.request.user.is_staff:
            return FAQ.objects.all()
        return FAQ.objects.filter(is_published=True)

    def retrieve(self, request, *args, **kwargs):
        """Increment view count when FAQ is viewed"""
        instance = self.get_object()
        instance.view_count += 1
        instance.save(update_fields=['view_count'])
        return super().retrieve(request, *args, **kwargs)

    @action(detail=True, methods=['post'])
    def vote(self, request, pk=None):
        """Vote on FAQ helpfulness"""
        faq = self.get_object()
        serializer = FAQVoteSerializer(data=request.data)

        if serializer.is_valid():
            if serializer.validated_data['helpful']:
                faq.helpful_count += 1
            else:
                faq.not_helpful_count += 1
            faq.save()

            return Response({
                'helpful_count': faq.helpful_count,
                'not_helpful_count': faq.not_helpful_count,
                'helpfulness_ratio': faq.helpfulness_ratio
            })

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class FAQListView(generics.ListAPIView):
    """
    List frequently asked questions
    """
    serializer_class = FAQSerializer
    permission_classes = [permissions.AllowAny]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['category']
    search_fields = ['question', 'answer']
    ordering = ['category', 'order']

    def get_queryset(self):
        return FAQ.objects.filter(is_published=True)


class ContactFormView(generics.CreateAPIView):
    """
    Handle contact form submissions
    """
    serializer_class = ContactMessageSerializer
    permission_classes = [permissions.AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            contact_message = serializer.save()

            # TODO: Send email notification to staff
            # TODO: Send confirmation email to user

            return Response({
                'message': 'Your message has been sent successfully. We will get back to you soon.',
                'id': contact_message.id
            }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ContactMessageViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for managing contact messages (staff only)
    """
    serializer_class = ContactMessageSerializer
    permission_classes = [permissions.IsAuthenticated, permissions.IsAdminUser]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['is_read', 'is_replied']
    search_fields = ['name', 'email', 'subject', 'message']
    ordering = ['-created_at']

    def get_queryset(self):
        return ContactMessage.objects.all().select_related('user')

    @action(detail=True, methods=['patch'])
    def mark_read(self, request, pk=None):
        """Mark contact message as read"""
        message = self.get_object()
        message.is_read = True
        message.save(update_fields=['is_read'])

        serializer = self.get_serializer(message)
        return Response(serializer.data)

    @action(detail=True, methods=['patch'])
    def mark_replied(self, request, pk=None):
        """Mark contact message as replied"""
        message = self.get_object()
        message.is_replied = True
        message.save(update_fields=['is_replied'])

        serializer = self.get_serializer(message)
        return Response(serializer.data)
