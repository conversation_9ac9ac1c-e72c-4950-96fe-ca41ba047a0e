import React, { useState, useEffect, useRef } from 'react';
import { Search, Filter, Calendar, User, FileText, Image, Mic, X, ChevronDown, ChevronUp } from 'lucide-react';

const ChatSearch = ({ isOpen, onClose, messages, rooms, users, onMessageSelect }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [filters, setFilters] = useState({
    dateRange: 'all', // all, today, week, month, custom
    messageType: 'all', // all, text, file, image, voice
    sender: 'all', // all, me, specific user
    room: 'all', // all, current, specific room
    hasAttachments: false,
    isEdited: false,
    isPinned: false
  });
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [customDateRange, setCustomDateRange] = useState({
    start: '',
    end: ''
  });
  const [selectedResult, setSelectedResult] = useState(null);
  const searchInputRef = useRef(null);

  // Mock search function (in real app, this would be an API call)
  const performSearch = async (query, searchFilters) => {
    setIsLoading(true);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    let results = messages.filter(message => {
      // Text search
      const matchesQuery = !query || 
        message.content.toLowerCase().includes(query.toLowerCase()) ||
        message.sender?.username?.toLowerCase().includes(query.toLowerCase()) ||
        message.sender?.display_name?.toLowerCase().includes(query.toLowerCase());
      
      // Date filter
      let matchesDate = true;
      if (searchFilters.dateRange !== 'all') {
        const messageDate = new Date(message.created_at);
        const now = new Date();
        
        switch (searchFilters.dateRange) {
          case 'today':
            matchesDate = messageDate.toDateString() === now.toDateString();
            break;
          case 'week':
            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            matchesDate = messageDate >= weekAgo;
            break;
          case 'month':
            const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            matchesDate = messageDate >= monthAgo;
            break;
          case 'custom':
            if (customDateRange.start && customDateRange.end) {
              const startDate = new Date(customDateRange.start);
              const endDate = new Date(customDateRange.end);
              matchesDate = messageDate >= startDate && messageDate <= endDate;
            }
            break;
        }
      }
      
      // Message type filter
      const matchesType = searchFilters.messageType === 'all' || 
        message.message_type === searchFilters.messageType;
      
      // Sender filter
      let matchesSender = true;
      if (searchFilters.sender !== 'all') {
        if (searchFilters.sender === 'me') {
          matchesSender = message.sender?.id === 'current_user_id'; // Replace with actual user ID
        } else {
          matchesSender = message.sender?.id === searchFilters.sender;
        }
      }
      
      // Room filter
      const matchesRoom = searchFilters.room === 'all' || 
        message.room_id === searchFilters.room;
      
      // Additional filters
      const matchesAttachments = !searchFilters.hasAttachments || 
        (message.attachments && message.attachments.length > 0);
      
      const matchesEdited = !searchFilters.isEdited || message.is_edited;
      const matchesPinned = !searchFilters.isPinned || message.is_pinned;
      
      return matchesQuery && matchesDate && matchesType && matchesSender && 
             matchesRoom && matchesAttachments && matchesEdited && matchesPinned;
    });
    
    // Sort by relevance and date
    results = results.sort((a, b) => {
      // Prioritize exact matches
      const aExact = a.content.toLowerCase().includes(query.toLowerCase());
      const bExact = b.content.toLowerCase().includes(query.toLowerCase());
      
      if (aExact && !bExact) return -1;
      if (!aExact && bExact) return 1;
      
      // Then sort by date (newest first)
      return new Date(b.created_at) - new Date(a.created_at);
    });
    
    setSearchResults(results);
    setIsLoading(false);
  };

  useEffect(() => {
    if (searchQuery.trim() || Object.values(filters).some(f => f !== 'all' && f !== false)) {
      const debounceTimer = setTimeout(() => {
        performSearch(searchQuery, filters);
      }, 300);
      
      return () => clearTimeout(debounceTimer);
    } else {
      setSearchResults([]);
    }
  }, [searchQuery, filters, customDateRange]);

  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({
      dateRange: 'all',
      messageType: 'all',
      sender: 'all',
      room: 'all',
      hasAttachments: false,
      isEdited: false,
      isPinned: false
    });
    setCustomDateRange({ start: '', end: '' });
  };

  const highlightText = (text, query) => {
    if (!query) return text;
    
    const regex = new RegExp(`(${query})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">
          {part}
        </mark>
      ) : part
    );
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString([], {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getMessageTypeIcon = (type) => {
    switch (type) {
      case 'file':
        return <FileText className="w-4 h-4" />;
      case 'image':
        return <Image className="w-4 h-4" />;
      case 'voice':
        return <Mic className="w-4 h-4" />;
      default:
        return null;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl h-5/6 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            Search Messages
          </h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Search Input */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              ref={searchInputRef}
              type="text"
              placeholder="Search messages, users, or content..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Filters */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-3">
            <button
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
            >
              <Filter className="w-4 h-4" />
              <span>Advanced Filters</span>
              {showAdvancedFilters ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
            </button>
            
            <button
              onClick={clearFilters}
              className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
            >
              Clear Filters
            </button>
          </div>

          {showAdvancedFilters && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Date Range */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Date Range
                </label>
                <select
                  value={filters.dateRange}
                  onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                >
                  <option value="all">All Time</option>
                  <option value="today">Today</option>
                  <option value="week">Past Week</option>
                  <option value="month">Past Month</option>
                  <option value="custom">Custom Range</option>
                </select>
                
                {filters.dateRange === 'custom' && (
                  <div className="mt-2 space-y-2">
                    <input
                      type="date"
                      value={customDateRange.start}
                      onChange={(e) => setCustomDateRange(prev => ({ ...prev, start: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                    />
                    <input
                      type="date"
                      value={customDateRange.end}
                      onChange={(e) => setCustomDateRange(prev => ({ ...prev, end: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                    />
                  </div>
                )}
              </div>

              {/* Message Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Message Type
                </label>
                <select
                  value={filters.messageType}
                  onChange={(e) => handleFilterChange('messageType', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                >
                  <option value="all">All Types</option>
                  <option value="text">Text Messages</option>
                  <option value="file">Files</option>
                  <option value="image">Images</option>
                  <option value="voice">Voice Messages</option>
                </select>
              </div>

              {/* Sender */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Sender
                </label>
                <select
                  value={filters.sender}
                  onChange={(e) => handleFilterChange('sender', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                >
                  <option value="all">All Users</option>
                  <option value="me">My Messages</option>
                  {users.map(user => (
                    <option key={user.id} value={user.id}>
                      {user.display_name || user.username}
                    </option>
                  ))}
                </select>
              </div>

              {/* Additional Filters */}
              <div className="md:col-span-3">
                <div className="flex flex-wrap gap-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.hasAttachments}
                      onChange={(e) => handleFilterChange('hasAttachments', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Has Attachments</span>
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.isEdited}
                      onChange={(e) => handleFilterChange('isEdited', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Edited Messages</span>
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.isPinned}
                      onChange={(e) => handleFilterChange('isPinned', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Pinned Messages</span>
                  </label>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Results */}
        <div className="flex-1 overflow-y-auto p-4">
          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : searchResults.length > 0 ? (
            <div className="space-y-3">
              <div className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Found {searchResults.length} result{searchResults.length !== 1 ? 's' : ''}
              </div>
              
              {searchResults.map((message) => (
                <div
                  key={message.id}
                  onClick={() => {
                    setSelectedResult(message.id);
                    onMessageSelect(message);
                  }}
                  className={`border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer transition-colors ${
                    selectedResult === message.id ? 'ring-2 ring-blue-500' : ''
                  }`}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900 dark:text-white">
                        {message.sender?.display_name || message.sender?.username}
                      </span>
                      {message.sender?.is_vip && (
                        <span className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 px-2 py-1 rounded-full text-xs">
                          VIP
                        </span>
                      )}
                      {getMessageTypeIcon(message.message_type)}
                    </div>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {formatDate(message.created_at)}
                    </span>
                  </div>
                  
                  <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    {highlightText(message.content, searchQuery)}
                  </div>
                  
                  <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                    <span>Room: {message.room?.name || 'Unknown'}</span>
                    <div className="flex items-center space-x-2">
                      {message.is_edited && <span>Edited</span>}
                      {message.is_pinned && <span>Pinned</span>}
                      {message.attachments?.length > 0 && (
                        <span>{message.attachments.length} attachment{message.attachments.length !== 1 ? 's' : ''}</span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : searchQuery || Object.values(filters).some(f => f !== 'all' && f !== false) ? (
            <div className="text-center py-8">
              <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No results found
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                Try adjusting your search terms or filters.
              </p>
            </div>
          ) : (
            <div className="text-center py-8">
              <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Search Messages
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                Enter a search term to find messages, users, or content.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatSearch;
