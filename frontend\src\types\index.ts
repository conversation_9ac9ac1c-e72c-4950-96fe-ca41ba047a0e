// User Types
export interface User {
  id: string;
  username: string;
  email: string;
  display_name?: string;
  bio?: string;
  avatar?: string;
  cover_image?: string;
  user_type: 'regular' | 'vip' | 'agent' | 'admin';
  status?: 'online' | 'away' | 'busy' | 'offline';
  is_online?: boolean;
  is_vip: boolean;
  is_staff?: boolean;
  last_seen?: string;
  created_at?: string;
  updated_at?: string;
  date_joined?: string;
  last_login?: string;
}

export interface UserProfile {
  user: string;
  website?: string;
  twitter?: string;
  linkedin?: string;
  github?: string;
  country?: string;
  city?: string;
  company?: string;
  job_title?: string;
  total_messages: number;
  total_files_shared: number;
  total_reactions_given: number;
  total_reactions_received: number;
}

// Chat Room Types
export interface ChatRoom {
  id: string;
  name: string;
  description?: string;
  room_type: 'private' | 'group' | 'channel' | 'live' | 'vip' | 'support';
  is_public: boolean;
  is_archived: boolean;
  max_members: number;
  allow_file_sharing: boolean;
  allow_voice_messages: boolean;
  allow_reactions: boolean;
  is_vip_only: boolean;
  vip_level_required: number;
  is_live: boolean;
  live_viewer_count: number;
  avatar?: string;
  created_by: User;
  created_at: string;
  updated_at: string;
  member_count: number;
  last_message?: Message;
}

export interface RoomMembership {
  user: User;
  room: ChatRoom;
  role: 'member' | 'moderator' | 'admin' | 'owner';
  is_active: boolean;
  is_muted: boolean;
  is_banned: boolean;
  joined_at: string;
  last_read_at: string;
  unread_count: number;
}

// Message Types
export interface Message {
  id: string;
  room: string;
  sender: User;
  content: string;
  message_type: 'text' | 'file' | 'image' | 'voice' | 'system' | 'bot';
  file?: string;
  file_name?: string;
  file_size?: number;
  file_type?: string;
  is_edited: boolean;
  is_deleted: boolean;
  is_pinned: boolean;
  reply_to?: Message;
  thread_id?: string;
  created_at: string;
  updated_at: string;
  edited_at?: string;
  reactions: MessageReaction[];
  read_by: MessageRead[];
}

export interface MessageReaction {
  id: string;
  message: string;
  user: User;
  emoji: string;
  created_at: string;
}

export interface MessageRead {
  message: string;
  user: User;
  read_at: string;
}

export interface TypingIndicator {
  room: string;
  user: User;
  started_at: string;
}

// WebSocket Types
export interface WebSocketMessage {
  type: string;
  data?: any;
  message?: Message;
  user_id?: string;
  username?: string;
  display_name?: string;
  avatar?: string;
  message_id?: string;
  emoji?: string;
  action?: 'add' | 'remove';
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T> {
  count: number;
  next?: string;
  previous?: string;
  results: T[];
}

// Authentication Types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  password_confirm: string;
  display_name?: string;
}

export interface AuthTokens {
  access: string;
  refresh: string;
}

export interface AuthState {
  user: User | null;
  tokens: AuthTokens | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

// Chat State Types
export interface ChatState {
  rooms: ChatRoom[];
  activeRoom: ChatRoom | null;
  messages: Record<string, Message[]>;
  typingUsers: Record<string, User[]>;
  onlineUsers: User[];
  isConnected: boolean;
  isLoading: boolean;
}

// UI State Types
export interface UIState {
  theme: 'light' | 'dark';
  sidebarOpen: boolean;
  rightSidebarOpen: boolean;
  emojiPickerOpen: boolean;
  fileUploadProgress: Record<string, number>;
  notifications: Notification[];
}

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary' | 'danger';
}

// File Upload Types
export interface FileUpload {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  url?: string;
  error?: string;
}

// Live Room Types
export interface LiveRoom extends ChatRoom {
  is_live: true;
  live_viewer_count: number;
  live_started_at?: string;
  live_ended_at?: string;
  live_host: User;
  live_moderators: User[];
}

// VIP Types
export interface VIPFeatures {
  exclusive_rooms: boolean;
  priority_support: boolean;
  custom_emojis: boolean;
  file_size_limit: number;
  early_access: boolean;
  custom_badges: boolean;
}

// Analytics Types
export interface ChatAnalytics {
  total_messages: number;
  total_users: number;
  active_users: number;
  popular_rooms: ChatRoom[];
  message_trends: MessageTrend[];
  user_activity: UserActivity[];
}

export interface MessageTrend {
  date: string;
  count: number;
}

export interface UserActivity {
  user: User;
  message_count: number;
  last_active: string;
}

// Search Types
export interface SearchResult {
  type: 'message' | 'user' | 'room';
  id: string;
  title: string;
  description: string;
  avatar?: string;
  room?: ChatRoom;
  user?: User;
  message?: Message;
}

// Settings Types
export interface UserSettings {
  notifications: {
    email: boolean;
    push: boolean;
    sound: boolean;
    desktop: boolean;
  };
  privacy: {
    show_online_status: boolean;
    allow_direct_messages: boolean;
    show_read_receipts: boolean;
  };
  appearance: {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    timezone: string;
    font_size: 'small' | 'medium' | 'large';
  };
  chat: {
    enter_to_send: boolean;
    show_typing_indicators: boolean;
    auto_play_media: boolean;
    emoji_suggestions: boolean;
  };
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}
