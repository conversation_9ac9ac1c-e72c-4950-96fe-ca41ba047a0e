import React, { useState, useEffect, useRef } from 'react';
import { Routes, Route, useNavigate, useParams } from 'react-router-dom';
import { useApp } from '../App';
import {
  Search,
  Settings,
  Bell,
  Users,
  Plus,
  Hash,
  Lock,
  Crown,
  Radio,
  MessageCircle,
  Phone,
  Video,
  MoreVertical,
  X
} from 'lucide-react';

// Component imports
import Sidebar from '../components/chat/Sidebar';
import ChatWindow from '../components/chat/ChatWindow';
import RoomList from '../components/chat/RoomList';
import ChatSearch from '../components/chat/ChatSearch';
import CreateRoomModal from '../components/modals/CreateRoomModal';
import WelcomeScreen from '../components/welcome/WelcomeScreen';
import LoadingScreen from '../components/loading/LoadingScreen';

const ChatPage = () => {
  const {
    user,
    socket,
    isConnected,
    onlineUsers,
    typingUsers,
    unreadCounts,
    sendMessage,
    startTyping,
    stopTyping,
    markAsRead,
    addNotification
  } = useApp();

  const navigate = useNavigate();
  const { roomId } = useParams();

  // State management
  const [selectedRoom, setSelectedRoom] = useState(null);
  const [rooms, setRooms] = useState([]);
  const [messages, setMessages] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [roomMembers, setRoomMembers] = useState([]);
  const [pinnedMessages, setPinnedMessages] = useState([]);
  const [showSearch, setShowSearch] = useState(false);
  const [showRoomList, setShowRoomList] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showCreateRoom, setShowCreateRoom] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [demoMode, setDemoMode] = useState(false);

  const messagesEndRef = useRef(null);
  const typingTimeoutRef = useRef(null);
  const messageInputRef = useRef(null);

  // Demo data for when backend is not available
  const demoRooms = [
    {
      id: 1,
      name: 'General Chat',
      description: 'General discussion for everyone',
      room_type: 'public',
      member_count: 1247,
      is_active: true,
      has_unread: false,
      last_message: 'Welcome to ChatFlow Pro!',
      last_activity: new Date().toISOString(),
      created_at: new Date().toISOString()
    },
    {
      id: 2,
      name: 'Random',
      description: 'Random conversations and fun topics',
      room_type: 'public',
      member_count: 892,
      is_active: false,
      has_unread: true,
      last_message: 'Anyone up for a game?',
      last_activity: new Date(Date.now() - 3600000).toISOString(),
      created_at: new Date(Date.now() - 86400000).toISOString()
    },
    {
      id: 3,
      name: 'Live Events',
      description: 'Live streaming and events',
      room_type: 'live',
      member_count: 2156,
      is_active: true,
      has_unread: false,
      is_live: true,
      last_message: 'Live event starting now!',
      last_activity: new Date().toISOString(),
      created_at: new Date(Date.now() - 172800000).toISOString()
    },
    {
      id: 4,
      name: 'VIP Lounge',
      description: 'Exclusive VIP member area',
      room_type: 'vip',
      member_count: 89,
      is_active: false,
      has_unread: false,
      requires_vip: true,
      last_message: 'VIP exclusive content available',
      last_activity: new Date(Date.now() - 7200000).toISOString(),
      created_at: new Date(Date.now() - 259200000).toISOString()
    }
  ];

  const demoMessages = [
    {
      id: 1,
      content: 'Welcome to ChatFlow Pro! This is a demo of our professional chat platform.',
      sender: {
        id: 'system',
        username: 'system',
        display_name: 'ChatFlow System',
        avatar: null,
        is_vip: false,
        is_staff: true
      },
      created_at: new Date(Date.now() - 86400000).toISOString(),
      message_type: 'text',
      is_edited: false,
      is_pinned: true,
      reactions: [
        { emoji: '👍', count: 12, users: ['user1', 'user2'] },
        { emoji: '❤️', count: 8, users: ['user3', 'user4'] }
      ]
    },
    {
      id: 2,
      content: 'This is a demo message to show how the chat interface works. You can create rooms, send messages, and interact with other users!',
      sender: {
        id: 'demo_user',
        username: 'demo_user',
        display_name: 'Demo User',
        avatar: null,
        is_vip: false,
        is_staff: false
      },
      created_at: new Date(Date.now() - 3600000).toISOString(),
      message_type: 'text',
      is_edited: false,
      is_pinned: false,
      reactions: [
        { emoji: '👋', count: 5, users: ['user2', 'user3'] }
      ]
    }
  ];

  const enableDemoMode = () => {
    setDemoMode(true);
    setRooms(demoRooms);
    setMessages(demoMessages);
    setSelectedRoom(demoRooms[0]);
    setIsLoading(false);

    if (addNotification) {
      addNotification({
        type: 'info',
        title: 'Demo Mode',
        message: 'Running in demo mode. Create an account to access full features!'
      });
    }
  };

  // Load rooms from API
  const loadRooms = async () => {
    try {
      setIsLoading(true);

      // Check if we have a token
      const token = localStorage.getItem('chatflow_token');
      if (!token) {
        console.log('No auth token found, proceeding without authentication');
        setRooms([]);
        setIsLoading(false);
        return;
      }

      const response = await fetch('http://localhost:8000/api/chat/rooms/', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setRooms(data.results || []);
      } else {
        console.error('Failed to load rooms, status:', response.status);
        setRooms([]);

        // If unauthorized, clear token and proceed without auth
        if (response.status === 401) {
          localStorage.removeItem('chatflow_token');
        }
      }
    } catch (error) {
      console.error('Error loading rooms:', error);

      // Enable demo mode when backend is not available
      console.log('Backend not available, enabling demo mode');
      enableDemoMode();
      return;
    } finally {
      setIsLoading(false);
    }
  };

  // Load messages for a specific room
  const loadMessages = async (roomId) => {
    if (!roomId) return;

    try {
      setIsLoading(true);
      const response = await fetch(`http://localhost:8000/api/chat/rooms/${roomId}/messages/`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('chatflow_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setMessages(data.results || []);
      } else {
        console.error('Failed to load messages');
        setMessages([]);
      }
    } catch (error) {
      console.error('Error loading messages:', error);
      setMessages([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Load room members
  const loadRoomMembers = async (roomId) => {
    if (!roomId) return;

    try {
      const response = await fetch(`http://localhost:8000/api/chat/rooms/${roomId}/members/`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('chatflow_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setRoomMembers(data.results || []);
      } else {
        console.error('Failed to load room members');
        setRoomMembers([]);
      }
    } catch (error) {
      console.error('Error loading room members:', error);
      setRoomMembers([]);
    }
  };

  // Initialize data
  useEffect(() => {
    loadRooms();

    // Fallback timeout to prevent infinite loading
    const timeout = setTimeout(() => {
      if (isLoading) {
        console.log('Loading timeout reached, stopping loading state');
        setIsLoading(false);
      }
    }, 10000); // 10 second timeout

    return () => clearTimeout(timeout);
  }, []);

  // Load room-specific data when room changes
  useEffect(() => {
    if (roomId) {
      const room = rooms.find(r => r.id.toString() === roomId);
      if (room) {
        setSelectedRoom(room);
        loadMessages(room.id);
        loadRoomMembers(room.id);
      }
    } else if (rooms.length > 0) {
      // Auto-select first room if no room specified
      const firstRoom = rooms[0];
      setSelectedRoom(firstRoom);
      loadMessages(firstRoom.id);
      loadRoomMembers(firstRoom.id);
      navigate(`/chat/${firstRoom.id}`);
    }
  }, [roomId, rooms, navigate]);

  // Handle room selection
  const handleRoomSelect = (room) => {
    setSelectedRoom(room);
    loadMessages(room.id);
    loadRoomMembers(room.id);
    navigate(`/chat/${room.id}`);
    if (markAsRead) {
      markAsRead(room.id);
    }
  };

  // Handle message sending
  const handleSendMessage = async (content, replyToId = null, type = 'send') => {
    if (!content.trim() && type === 'send') return;
    if (!selectedRoom) return;

    try {
      if (type === 'send') {
        // Create optimistic message
        const tempMessage = {
          id: `temp_${Date.now()}`,
          content: content.trim(),
          sender: user,
          created_at: new Date().toISOString(),
          message_type: 'text',
          is_edited: false,
          is_pinned: false,
          reactions: [],
          status: 'sending'
        };

        setMessages(prev => [...prev, tempMessage]);

        // Send to API
        const response = await fetch(`http://localhost:8000/api/chat/rooms/${selectedRoom.id}/messages/`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('chatflow_token')}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            content: content.trim(),
            message_type: 'text',
            reply_to: replyToId
          })
        });

        if (response.ok) {
          const newMessage = await response.json();
          // Replace temp message with real message
          setMessages(prev => prev.map(m =>
            m.id === tempMessage.id ? { ...newMessage, status: 'sent' } : m
          ));

          // Use WebSocket if available
          if (sendMessage) {
            sendMessage(content, selectedRoom.id);
          }
        } else {
          // Remove temp message on error
          setMessages(prev => prev.filter(m => m.id !== tempMessage.id));
          addNotification({
            type: 'error',
            title: 'Error',
            message: 'Failed to send message'
          });
        }
      } else if (type === 'edit') {
        const response = await fetch(`http://localhost:8000/api/chat/messages/${replyToId}/`, {
          method: 'PATCH',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('chatflow_token')}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ content })
        });

        if (response.ok) {
          setMessages(prev => prev.map(m =>
            m.id === replyToId ? { ...m, content, is_edited: true } : m
          ));
        }
      } else if (type === 'delete') {
        const response = await fetch(`http://localhost:8000/api/chat/messages/${replyToId}/`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('chatflow_token')}`
          }
        });

        if (response.ok) {
          setMessages(prev => prev.filter(m => m.id !== replyToId));
        }
      } else if (type === 'pin') {
        const response = await fetch(`http://localhost:8000/api/chat/messages/${replyToId}/pin/`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('chatflow_token')}`
          }
        });

        if (response.ok) {
          setMessages(prev => prev.map(m =>
            m.id === replyToId ? { ...m, is_pinned: !m.is_pinned } : m
          ));
        }
      }
    } catch (error) {
      console.error('Error handling message:', error);
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to process message'
      });
    }
  };

  // Handle typing
  const handleTyping = () => {
    if (startTyping) {
      startTyping(selectedRoom?.id);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing
    typingTimeoutRef.current = setTimeout(() => {
      if (stopTyping) {
        stopTyping(selectedRoom?.id);
      }
    }, 1000);
  };

  // Handle room creation
  const handleCreateRoom = async (roomData) => {
    try {
      console.log('Creating room with data:', roomData);
      const response = await fetch('http://localhost:8000/api/chat/rooms/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('chatflow_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(roomData)
      });

      console.log('Room creation response status:', response.status);

      if (response.ok) {
        const newRoom = await response.json();
        setRooms(prev => [newRoom, ...prev]);
        addNotification({
          type: 'success',
          title: 'Room Created',
          message: `Successfully created room "${newRoom.name}"`
        });

        // Auto-select the new room
        handleRoomSelect(newRoom);
      } else {
        const errorData = await response.json();
        console.log('Room creation error response:', errorData);
        throw new Error(errorData.message || 'Failed to create room');
      }
    } catch (error) {
      console.error('Error creating room:', error);
      addNotification({
        type: 'error',
        title: 'Error',
        message: error.message || 'Failed to create room'
      });
      throw error;
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/');
    } catch (error) {
      console.error('Logout error:', error);
      addNotification({
        type: 'error',
        title: 'Logout Failed',
        message: 'Failed to logout properly'
      });
    }
  };

  // Show loading screen while initializing
  if (isLoading && rooms.length === 0) {
    return <LoadingScreen message="Loading your chat rooms..." />;
  }

  return (
    <div className="h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 z-10">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-3">
            <div className="flex items-center space-x-4">
              <div className="h-8 w-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">CF</span>
              </div>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">ChatFlow Pro</h1>

              {/* Demo Mode Indicator */}
              {demoMode && (
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 rounded-full bg-orange-500 animate-pulse"></div>
                  <span className="text-xs text-orange-600 dark:text-orange-400 font-medium">
                    Demo Mode
                  </span>
                </div>
              )}

              {/* Connection Status */}
              {!demoMode && (
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {isConnected ? 'Connected' : 'Disconnected'}
                  </span>
                </div>
              )}
            </div>

            {/* Header Actions */}
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setShowSearch(true)}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                title="Search Messages"
              >
                <Search className="w-5 h-5" />
              </button>

              <button
                onClick={() => setShowCreateRoom(true)}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                title="Create Room"
              >
                <Plus className="w-5 h-5" />
              </button>

              <button
                onClick={() => {
                  // TODO: Implement notifications panel
                  addNotification({
                    id: Date.now(),
                    type: 'info',
                    title: 'Notifications',
                    message: 'Notifications panel coming soon!'
                  });
                }}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                title="Notifications"
              >
                <Bell className="w-5 h-5" />
              </button>

              <button
                onClick={() => setShowSettings(true)}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                title="Settings"
              >
                <Settings className="w-5 h-5" />
              </button>

              {/* User Menu */}
              <div className="flex items-center space-x-3 pl-3 border-l border-gray-200 dark:border-gray-700">
                <div className="flex items-center space-x-2">
                  <div className="h-8 w-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">
                      {user?.display_name?.[0] || user?.username?.[0] || 'U'}
                    </span>
                  </div>
                  <div className="hidden sm:block">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {user?.display_name || user?.username || 'User'}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {user?.is_vip ? 'VIP Member' : 'Member'}
                    </div>
                  </div>
                  {user?.is_vip && (
                    <Crown className="w-4 h-4 text-yellow-500" />
                  )}
                </div>

                <button
                  onClick={handleLogout}
                  className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                >
                  Sign Out
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        <div className={`${sidebarCollapsed ? 'w-16' : 'w-80'} transition-all duration-200 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex-shrink-0`}>
          <Sidebar
            rooms={rooms}
            selectedRoom={selectedRoom}
            onRoomSelect={handleRoomSelect}
            onlineUsers={onlineUsers}
            unreadCounts={unreadCounts}
            user={user}
            isCollapsed={sidebarCollapsed}
            onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
            onCreateRoom={() => setShowCreateRoom(true)}
          />
        </div>

        {/* Chat Area */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {rooms.length === 0 && !demoMode ? (
            <WelcomeScreen
              user={user}
              onCreateRoom={() => setShowCreateRoom(true)}
              onBrowseRooms={() => setShowRoomList(true)}
              onTryDemo={enableDemoMode}
              showDemoOption={true}
            />
          ) : selectedRoom ? (
            <ChatWindow
              room={selectedRoom}
              messages={messages}
              onSendMessage={handleSendMessage}
              onTyping={handleTyping}
              user={user}
              roomMembers={roomMembers}
              typingUsers={typingUsers}
              pinnedMessages={pinnedMessages}
              isLoading={isLoading}
              onReply={(message) => console.log('Reply to:', message)}
              onEdit={(message) => console.log('Edit:', message)}
              onDelete={(messageId) => console.log('Delete:', messageId)}
              onPin={(messageId) => console.log('Pin:', messageId)}
              onReaction={(messageId, emoji) => console.log('React:', messageId, emoji)}
            />
          ) : (
            <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900">
              <div className="text-center">
                <MessageCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Select a Room
                </h3>
                <p className="text-gray-500 dark:text-gray-400 max-w-md">
                  Choose a room from the sidebar to start chatting with your community.
                </p>
              </div>
            </div>
          )}
        </div>

      </div>

      {/* Modal Components */}
      {showSearch && (
        <ChatSearch
          isOpen={showSearch}
          onClose={() => setShowSearch(false)}
          messages={messages}
          rooms={rooms}
          users={roomMembers}
          onMessageSelect={(message) => {
            console.log('Navigate to message:', message);
            setShowSearch(false);
          }}
        />
      )}

      {showRoomList && (
        <RoomList
          isOpen={showRoomList}
          onClose={() => setShowRoomList(false)}
          onRoomSelect={(room) => {
            handleRoomSelect(room);
            setShowRoomList(false);
          }}
          user={user}
          currentRoom={selectedRoom}
        />
      )}

      {showCreateRoom && (
        <CreateRoomModal
          isOpen={showCreateRoom}
          onClose={() => setShowCreateRoom(false)}
          onCreateRoom={handleCreateRoom}
          user={user}
        />
      )}

      {/* Settings Modal */}
      {showSettings && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-5/6 overflow-y-auto">
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                Chat Settings
              </h2>
              <button
                onClick={() => setShowSettings(false)}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    Notification Preferences
                  </h3>
                  <div className="space-y-3">
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" defaultChecked />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Desktop notifications</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" defaultChecked />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Sound notifications</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Email notifications</span>
                    </label>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    Privacy Settings
                  </h3>
                  <div className="space-y-3">
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" defaultChecked />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Show online status</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" defaultChecked />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Allow direct messages</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Read receipts</span>
                    </label>
                  </div>
                </div>
              </div>

              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={() => setShowSettings(false)}
                  className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
                >
                  Cancel
                </button>
                <button
                  onClick={() => setShowSettings(false)}
                  className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg"
                >
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatPage;
