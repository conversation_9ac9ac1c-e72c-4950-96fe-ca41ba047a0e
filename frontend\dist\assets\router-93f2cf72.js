import{r as s,R as ue}from"./vendor-746985b6.js";/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function L(){return L=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},L.apply(this,arguments)}var w;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(w||(w={}));const J="popstate";function ce(e){e===void 0&&(e={});function t(r,a){let{pathname:i,search:l,hash:u}=r.location;return $("",{pathname:i,search:l,hash:u},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function n(r,a){return typeof a=="string"?a:T(a)}return he(t,n,null,e)}function v(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Z(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function fe(){return Math.random().toString(36).substr(2,8)}function A(e,t){return{usr:e.state,key:e.key,idx:t}}function $(e,t,n,r){return n===void 0&&(n=null),L({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?O(t):t,{state:n,key:t&&t.key||r||fe()})}function T(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function O(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function he(e,t,n,r){r===void 0&&(r={});let{window:a=document.defaultView,v5Compat:i=!1}=r,l=a.history,u=w.Pop,o=null,f=h();f==null&&(f=0,l.replaceState(L({},l.state,{idx:f}),""));function h(){return(l.state||{idx:null}).idx}function c(){u=w.Pop;let d=h(),x=d==null?null:d-f;f=d,o&&o({action:u,location:m.location,delta:x})}function p(d,x){u=w.Push;let E=$(m.location,d,x);n&&n(E,d),f=h()+1;let C=A(E,f),R=m.createHref(E);try{l.pushState(C,"",R)}catch(U){if(U instanceof DOMException&&U.name==="DataCloneError")throw U;a.location.assign(R)}i&&o&&o({action:u,location:m.location,delta:1})}function y(d,x){u=w.Replace;let E=$(m.location,d,x);n&&n(E,d),f=h();let C=A(E,f),R=m.createHref(E);l.replaceState(C,"",R),i&&o&&o({action:u,location:m.location,delta:0})}function g(d){let x=a.location.origin!=="null"?a.location.origin:a.location.href,E=typeof d=="string"?d:T(d);return E=E.replace(/ $/,"%20"),v(x,"No window.location.(origin|href) available to create URL for href: "+E),new URL(E,x)}let m={get action(){return u},get location(){return e(a,l)},listen(d){if(o)throw new Error("A history only accepts one active listener");return a.addEventListener(J,c),o=d,()=>{a.removeEventListener(J,c),o=null}},createHref(d){return t(a,d)},createURL:g,encodeLocation(d){let x=g(d);return{pathname:x.pathname,search:x.search,hash:x.hash}},push:p,replace:y,go(d){return l.go(d)}};return m}var K;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(K||(K={}));function de(e,t,n){return n===void 0&&(n="/"),pe(e,t,n,!1)}function pe(e,t,n,r){let a=typeof t=="string"?O(t):t,i=F(a.pathname||"/",n);if(i==null)return null;let l=ee(e);me(l);let u=null;for(let o=0;u==null&&o<l.length;++o){let f=Se(i);u=we(l[o],f,r)}return u}function ee(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let a=(i,l,u)=>{let o={relativePath:u===void 0?i.path||"":u,caseSensitive:i.caseSensitive===!0,childrenIndex:l,route:i};o.relativePath.startsWith("/")&&(v(o.relativePath.startsWith(r),'Absolute route path "'+o.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),o.relativePath=o.relativePath.slice(r.length));let f=b([r,o.relativePath]),h=n.concat(o);i.children&&i.children.length>0&&(v(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+f+'".')),ee(i.children,t,h,f)),!(i.path==null&&!i.index)&&t.push({path:f,score:Re(f,i.index),routesMeta:h})};return e.forEach((i,l)=>{var u;if(i.path===""||!((u=i.path)!=null&&u.includes("?")))a(i,l);else for(let o of te(i.path))a(i,l,o)}),t}function te(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,a=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return a?[i,""]:[i];let l=te(r.join("/")),u=[];return u.push(...l.map(o=>o===""?i:[i,o].join("/"))),a&&u.push(...l),u.map(o=>e.startsWith("/")&&o===""?"/":o)}function me(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Pe(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const ve=/^:[\w-]+$/,ge=3,ye=2,xe=1,Ce=10,Ee=-2,q=e=>e==="*";function Re(e,t){let n=e.split("/"),r=n.length;return n.some(q)&&(r+=Ee),t&&(r+=ye),n.filter(a=>!q(a)).reduce((a,i)=>a+(ve.test(i)?ge:i===""?xe:Ce),r)}function Pe(e,t){return e.length===t.length&&e.slice(0,-1).every((r,a)=>r===t[a])?e[e.length-1]-t[t.length-1]:0}function we(e,t,n){n===void 0&&(n=!1);let{routesMeta:r}=e,a={},i="/",l=[];for(let u=0;u<r.length;++u){let o=r[u],f=u===r.length-1,h=i==="/"?t:t.slice(i.length)||"/",c=G({path:o.relativePath,caseSensitive:o.caseSensitive,end:f},h),p=o.route;if(!c&&f&&n&&!r[r.length-1].route.index&&(c=G({path:o.relativePath,caseSensitive:o.caseSensitive,end:!1},h)),!c)return null;Object.assign(a,c.params),l.push({params:a,pathname:b([i,c.pathname]),pathnameBase:Le(b([i,c.pathnameBase])),route:p}),c.pathnameBase!=="/"&&(i=b([i,c.pathnameBase]))}return l}function G(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=be(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let i=a[0],l=i.replace(/(.)\/+$/,"$1"),u=a.slice(1);return{params:r.reduce((f,h,c)=>{let{paramName:p,isOptional:y}=h;if(p==="*"){let m=u[c]||"";l=i.slice(0,i.length-m.length).replace(/(.)\/+$/,"$1")}const g=u[c];return y&&!g?f[p]=void 0:f[p]=(g||"").replace(/%2F/g,"/"),f},{}),pathname:i,pathnameBase:l,pattern:e}}function be(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Z(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(l,u,o)=>(r.push({paramName:u,isOptional:o!=null}),o?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function Se(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Z(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function F(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function Ue(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:a=""}=typeof e=="string"?O(e):e;return{pathname:n?n.startsWith("/")?n:Oe(n,t):t,search:Ie(r),hash:Ne(a)}}function Oe(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?n.length>1&&n.pop():a!=="."&&n.push(a)}),n.length>1?n.join("/"):"/"}function k(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Be(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function V(e,t){let n=Be(e);return t?n.map((r,a)=>a===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function z(e,t,n,r){r===void 0&&(r=!1);let a;typeof e=="string"?a=O(e):(a=L({},e),v(!a.pathname||!a.pathname.includes("?"),k("?","pathname","search",a)),v(!a.pathname||!a.pathname.includes("#"),k("#","pathname","hash",a)),v(!a.search||!a.search.includes("#"),k("#","search","hash",a)));let i=e===""||a.pathname==="",l=i?"/":a.pathname,u;if(l==null)u=n;else{let c=t.length-1;if(!r&&l.startsWith("..")){let p=l.split("/");for(;p[0]==="..";)p.shift(),c-=1;a.pathname=p.join("/")}u=c>=0?t[c]:"/"}let o=Ue(a,u),f=l&&l!=="/"&&l.endsWith("/"),h=(i||l===".")&&n.endsWith("/");return!o.pathname.endsWith("/")&&(f||h)&&(o.pathname+="/"),o}const b=e=>e.join("/").replace(/\/\/+/g,"/"),Le=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Ie=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ne=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Te(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const ne=["post","put","patch","delete"];new Set(ne);const _e=["get",...ne];new Set(_e);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function I(){return I=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},I.apply(this,arguments)}const D=s.createContext(null),je=s.createContext(null),S=s.createContext(null),j=s.createContext(null),P=s.createContext({outlet:null,matches:[],isDataRoute:!1}),re=s.createContext(null);function ke(e,t){let{relative:n}=t===void 0?{}:t;B()||v(!1);let{basename:r,navigator:a}=s.useContext(S),{hash:i,pathname:l,search:u}=le(e,{relative:n}),o=l;return r!=="/"&&(o=l==="/"?r:b([r,l])),a.createHref({pathname:o,search:u,hash:i})}function B(){return s.useContext(j)!=null}function N(){return B()||v(!1),s.useContext(j).location}function ae(e){s.useContext(S).static||s.useLayoutEffect(e)}function ie(){let{isDataRoute:e}=s.useContext(P);return e?He():$e()}function $e(){B()||v(!1);let e=s.useContext(D),{basename:t,future:n,navigator:r}=s.useContext(S),{matches:a}=s.useContext(P),{pathname:i}=N(),l=JSON.stringify(V(a,n.v7_relativeSplatPath)),u=s.useRef(!1);return ae(()=>{u.current=!0}),s.useCallback(function(f,h){if(h===void 0&&(h={}),!u.current)return;if(typeof f=="number"){r.go(f);return}let c=z(f,JSON.parse(l),i,h.relative==="path");e==null&&t!=="/"&&(c.pathname=c.pathname==="/"?t:b([t,c.pathname])),(h.replace?r.replace:r.push)(c,h.state,h)},[t,r,l,i,e])}function ct(){let{matches:e}=s.useContext(P),t=e[e.length-1];return t?t.params:{}}function le(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=s.useContext(S),{matches:a}=s.useContext(P),{pathname:i}=N(),l=JSON.stringify(V(a,r.v7_relativeSplatPath));return s.useMemo(()=>z(e,JSON.parse(l),i,n==="path"),[e,l,i,n])}function We(e,t){return Me(e,t)}function Me(e,t,n,r){B()||v(!1);let{navigator:a}=s.useContext(S),{matches:i}=s.useContext(P),l=i[i.length-1],u=l?l.params:{};l&&l.pathname;let o=l?l.pathnameBase:"/";l&&l.route;let f=N(),h;if(t){var c;let d=typeof t=="string"?O(t):t;o==="/"||(c=d.pathname)!=null&&c.startsWith(o)||v(!1),h=d}else h=f;let p=h.pathname||"/",y=p;if(o!=="/"){let d=o.replace(/^\//,"").split("/");y="/"+p.replace(/^\//,"").split("/").slice(d.length).join("/")}let g=de(e,{pathname:y}),m=Je(g&&g.map(d=>Object.assign({},d,{params:Object.assign({},u,d.params),pathname:b([o,a.encodeLocation?a.encodeLocation(d.pathname).pathname:d.pathname]),pathnameBase:d.pathnameBase==="/"?o:b([o,a.encodeLocation?a.encodeLocation(d.pathnameBase).pathname:d.pathnameBase])})),i,n,r);return t&&m?s.createElement(j.Provider,{value:{location:I({pathname:"/",search:"",hash:"",state:null,key:"default"},h),navigationType:w.Pop}},m):m}function Fe(){let e=Ge(),t=Te(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},i=null;return s.createElement(s.Fragment,null,s.createElement("h2",null,"Unexpected Application Error!"),s.createElement("h3",{style:{fontStyle:"italic"}},t),n?s.createElement("pre",{style:a},n):null,i)}const Ve=s.createElement(Fe,null);class ze extends s.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?s.createElement(P.Provider,{value:this.props.routeContext},s.createElement(re.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function De(e){let{routeContext:t,match:n,children:r}=e,a=s.useContext(D);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),s.createElement(P.Provider,{value:t},r)}function Je(e,t,n,r){var a;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var i;if(!n)return null;if(n.errors)e=n.matches;else if((i=r)!=null&&i.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let l=e,u=(a=n)==null?void 0:a.errors;if(u!=null){let h=l.findIndex(c=>c.route.id&&(u==null?void 0:u[c.route.id])!==void 0);h>=0||v(!1),l=l.slice(0,Math.min(l.length,h+1))}let o=!1,f=-1;if(n&&r&&r.v7_partialHydration)for(let h=0;h<l.length;h++){let c=l[h];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(f=h),c.route.id){let{loaderData:p,errors:y}=n,g=c.route.loader&&p[c.route.id]===void 0&&(!y||y[c.route.id]===void 0);if(c.route.lazy||g){o=!0,f>=0?l=l.slice(0,f+1):l=[l[0]];break}}}return l.reduceRight((h,c,p)=>{let y,g=!1,m=null,d=null;n&&(y=u&&c.route.id?u[c.route.id]:void 0,m=c.route.errorElement||Ve,o&&(f<0&&p===0?(Xe("route-fallback",!1),g=!0,d=null):f===p&&(g=!0,d=c.route.hydrateFallbackElement||null)));let x=t.concat(l.slice(0,p+1)),E=()=>{let C;return y?C=m:g?C=d:c.route.Component?C=s.createElement(c.route.Component,null):c.route.element?C=c.route.element:C=h,s.createElement(De,{match:c,routeContext:{outlet:h,matches:x,isDataRoute:n!=null},children:C})};return n&&(c.route.ErrorBoundary||c.route.errorElement||p===0)?s.createElement(ze,{location:n.location,revalidation:n.revalidation,component:m,error:y,children:E(),routeContext:{outlet:null,matches:x,isDataRoute:!0}}):E()},null)}var oe=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(oe||{}),_=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(_||{});function Ae(e){let t=s.useContext(D);return t||v(!1),t}function Ke(e){let t=s.useContext(je);return t||v(!1),t}function qe(e){let t=s.useContext(P);return t||v(!1),t}function se(e){let t=qe(),n=t.matches[t.matches.length-1];return n.route.id||v(!1),n.route.id}function Ge(){var e;let t=s.useContext(re),n=Ke(_.UseRouteError),r=se(_.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function He(){let{router:e}=Ae(oe.UseNavigateStable),t=se(_.UseNavigateStable),n=s.useRef(!1);return ae(()=>{n.current=!0}),s.useCallback(function(a,i){i===void 0&&(i={}),n.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,I({fromRouteId:t},i)))},[e,t])}const H={};function Xe(e,t,n){!t&&!H[e]&&(H[e]=!0)}function Qe(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function ft(e){let{to:t,replace:n,state:r,relative:a}=e;B()||v(!1);let{future:i,static:l}=s.useContext(S),{matches:u}=s.useContext(P),{pathname:o}=N(),f=ie(),h=z(t,V(u,i.v7_relativeSplatPath),o,a==="path"),c=JSON.stringify(h);return s.useEffect(()=>f(JSON.parse(c),{replace:n,state:r,relative:a}),[f,c,a,n,r]),null}function Ye(e){v(!1)}function Ze(e){let{basename:t="/",children:n=null,location:r,navigationType:a=w.Pop,navigator:i,static:l=!1,future:u}=e;B()&&v(!1);let o=t.replace(/^\/*/,"/"),f=s.useMemo(()=>({basename:o,navigator:i,static:l,future:I({v7_relativeSplatPath:!1},u)}),[o,u,i,l]);typeof r=="string"&&(r=O(r));let{pathname:h="/",search:c="",hash:p="",state:y=null,key:g="default"}=r,m=s.useMemo(()=>{let d=F(h,o);return d==null?null:{location:{pathname:d,search:c,hash:p,state:y,key:g},navigationType:a}},[o,h,c,p,y,g,a]);return m==null?null:s.createElement(S.Provider,{value:f},s.createElement(j.Provider,{children:n,value:m}))}function ht(e){let{children:t,location:n}=e;return We(W(t),n)}new Promise(()=>{});function W(e,t){t===void 0&&(t=[]);let n=[];return s.Children.forEach(e,(r,a)=>{if(!s.isValidElement(r))return;let i=[...t,a];if(r.type===s.Fragment){n.push.apply(n,W(r.props.children,i));return}r.type!==Ye&&v(!1),!r.props.index||!r.props.children||v(!1);let l={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(l.children=W(r.props.children,i)),n.push(l)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function M(){return M=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},M.apply(this,arguments)}function et(e,t){if(e==null)return{};var n={},r=Object.keys(e),a,i;for(i=0;i<r.length;i++)a=r[i],!(t.indexOf(a)>=0)&&(n[a]=e[a]);return n}function tt(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function nt(e,t){return e.button===0&&(!t||t==="_self")&&!tt(e)}const rt=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],at="6";try{window.__reactRouterVersion=at}catch{}const it="startTransition",X=ue[it];function dt(e){let{basename:t,children:n,future:r,window:a}=e,i=s.useRef();i.current==null&&(i.current=ce({window:a,v5Compat:!0}));let l=i.current,[u,o]=s.useState({action:l.action,location:l.location}),{v7_startTransition:f}=r||{},h=s.useCallback(c=>{f&&X?X(()=>o(c)):o(c)},[o,f]);return s.useLayoutEffect(()=>l.listen(h),[l,h]),s.useEffect(()=>Qe(r),[r]),s.createElement(Ze,{basename:t,children:n,location:u.location,navigationType:u.action,navigator:l,future:r})}const lt=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",ot=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,pt=s.forwardRef(function(t,n){let{onClick:r,relative:a,reloadDocument:i,replace:l,state:u,target:o,to:f,preventScrollReset:h,viewTransition:c}=t,p=et(t,rt),{basename:y}=s.useContext(S),g,m=!1;if(typeof f=="string"&&ot.test(f)&&(g=f,lt))try{let C=new URL(window.location.href),R=f.startsWith("//")?new URL(C.protocol+f):new URL(f),U=F(R.pathname,y);R.origin===C.origin&&U!=null?f=U+R.search+R.hash:m=!0}catch{}let d=ke(f,{relative:a}),x=st(f,{replace:l,state:u,target:o,preventScrollReset:h,relative:a,viewTransition:c});function E(C){r&&r(C),C.defaultPrevented||x(C)}return s.createElement("a",M({},p,{href:g||d,onClick:m||i?r:E,ref:n,target:o}))});var Q;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Q||(Q={}));var Y;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Y||(Y={}));function st(e,t){let{target:n,replace:r,state:a,preventScrollReset:i,relative:l,viewTransition:u}=t===void 0?{}:t,o=ie(),f=N(),h=le(e,{relative:l});return s.useCallback(c=>{if(nt(c,n)){c.preventDefault();let p=r!==void 0?r:T(f)===T(h);o(e,{replace:p,state:a,preventScrollReset:i,relative:l,viewTransition:u})}},[f,o,h,r,a,n,e,i,l,u])}export{dt as B,pt as L,ft as N,ht as R,ct as a,N as b,Ye as c,ie as u};
//# sourceMappingURL=router-93f2cf72.js.map
