import { WebSocketMessage, Message, User } from '../types';
import { apiService } from './api';

export type WebSocketEventHandler = (data: any) => void;

class WebSocketService {
  private socket: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;
  private eventHandlers: Map<string, WebSocketEventHandler[]> = new Map();

  constructor() {
    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    // Initialize event handler maps
    const events = [
      'connect',
      'disconnect',
      'chat_message',
      'user_joined',
      'user_left',
      'typing_start',
      'typing_stop',
      'message_reaction',
      'message_edit',
      'message_delete',
      'user_status_change',
      'notification',
      'error',
    ];

    events.forEach(event => {
      this.eventHandlers.set(event, []);
    });
  }

  async connectToRoom(roomId: string): Promise<void> {
    if (this.socket?.readyState === WebSocket.OPEN || this.isConnecting) {
      return;
    }

    this.isConnecting = true;

    try {
      const token = apiService.getAccessToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8000';
      const fullUrl = `${wsUrl}/ws/chat/${roomId}/?token=${token}`;

      this.socket = new WebSocket(fullUrl);
      this.setupSocketEventListeners();

      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'));
        }, 10000);

        this.socket!.onopen = () => {
          clearTimeout(timeout);
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          console.log('WebSocket connected to room:', roomId);
          this.emit('connect', { roomId });
          resolve();
        };

        this.socket!.onerror = (error) => {
          clearTimeout(timeout);
          this.isConnecting = false;
          console.error('WebSocket connection error:', error);
          reject(error);
        };
      });
    } catch (error) {
      this.isConnecting = false;
      throw error;
    }
  }

  // Legacy connect method for backward compatibility
  async connect(): Promise<void> {
    console.warn('WebSocket connect() called without room ID. Use connectToRoom(roomId) instead.');
    // For now, just mark as connected without actual connection
    this.emit('connect', null);
  }

  private setupSocketEventListeners() {
    if (!this.socket) return;

    this.socket.onclose = (event) => {
      console.log('WebSocket disconnected:', event.code, event.reason);
      this.emit('disconnect', { code: event.code, reason: event.reason });

      // Attempt to reconnect
      this.handleReconnect();
    };

    this.socket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        const messageType = data.type;

        switch (messageType) {
          case 'chat_message':
            this.emit('chat_message', data);
            break;
          case 'user_joined':
            this.emit('user_joined', data);
            break;
          case 'user_left':
            this.emit('user_left', data);
            break;
          case 'typing_start':
            this.emit('typing_start', data);
            break;
          case 'typing_stop':
            this.emit('typing_stop', data);
            break;
          case 'message_reaction':
            this.emit('message_reaction', data);
            break;
          case 'user_status_change':
            this.emit('user_status_change', data);
            break;
          case 'notification':
            this.emit('notification', data);
            break;
          case 'error':
            console.error('WebSocket error:', data);
            this.emit('error', data);
            break;
          default:
            console.log('Unknown WebSocket message type:', messageType, data);
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    this.socket.onerror = (error) => {
      console.error('WebSocket error:', error);
      this.emit('error', error);
    };
  }

  private handleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);

    setTimeout(() => {
      // For reconnection, we need the room ID. This is a limitation of the current design.
      // In a real implementation, we should store the current room ID
      console.warn('WebSocket reconnection requires room ID. Manual reconnection needed.');
    }, delay);
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
    this.isConnecting = false;
    this.reconnectAttempts = 0;
  }

  // Event handling
  on(event: string, handler: WebSocketEventHandler): void {
    const handlers = this.eventHandlers.get(event) || [];
    handlers.push(handler);
    this.eventHandlers.set(event, handlers);
  }

  off(event: string, handler: WebSocketEventHandler): void {
    const handlers = this.eventHandlers.get(event) || [];
    const index = handlers.indexOf(handler);
    if (index > -1) {
      handlers.splice(index, 1);
      this.eventHandlers.set(event, handlers);
    }
  }

  private emit(event: string, data: any): void {
    const handlers = this.eventHandlers.get(event) || [];
    handlers.forEach(handler => {
      try {
        handler(data);
      } catch (error) {
        console.error(`Error in ${event} handler:`, error);
      }
    });
  }

  // Chat room methods
  joinRoom(roomId: string): void {
    // For native WebSocket, joining is handled by connecting to the room-specific URL
    // This method is kept for compatibility but doesn't need to do anything
    console.log('Room joined via WebSocket connection:', roomId);
  }

  leaveRoom(roomId: string): void {
    // For native WebSocket, leaving is handled by disconnecting
    this.disconnect();
  }

  // Message methods
  sendMessage(roomId: string, content: string, replyTo?: string): void {
    if (this.socket?.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify({
        type: 'chat_message',
        content,
        reply_to: replyTo,
      }));
    }
  }

  sendTypingStart(roomId: string): void {
    if (this.socket?.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify({
        type: 'typing_start',
      }));
    }
  }

  sendTypingStop(roomId: string): void {
    if (this.socket?.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify({
        type: 'typing_stop',
      }));
    }
  }

  reactToMessage(roomId: string, messageId: string, emoji: string): void {
    if (this.socket?.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify({
        type: 'message_reaction',
        message_id: messageId,
        emoji,
      }));
    }
  }

  editMessage(roomId: string, messageId: string, content: string): void {
    if (this.socket?.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify({
        type: 'message_edit',
        message_id: messageId,
        content,
      }));
    }
  }

  deleteMessage(roomId: string, messageId: string): void {
    if (this.socket?.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify({
        type: 'message_delete',
        message_id: messageId,
      }));
    }
  }

  markMessageAsRead(roomId: string, messageId: string): void {
    if (this.socket?.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify({
        type: 'mark_read',
        message_id: messageId,
      }));
    }
  }

  // Status methods
  updateUserStatus(status: 'online' | 'away' | 'busy' | 'offline'): void {
    if (this.socket?.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify({
        type: 'user_status_change',
        status,
      }));
    }
  }

  // Connection status
  isConnected(): boolean {
    return this.socket?.readyState === WebSocket.OPEN || false;
  }

  getConnectionState(): 'connected' | 'connecting' | 'disconnected' {
    if (this.socket?.readyState === WebSocket.OPEN) return 'connected';
    if (this.isConnecting) return 'connecting';
    return 'disconnected';
  }
}

// Create and export singleton instance
export const wsService = new WebSocketService();
export default wsService;
