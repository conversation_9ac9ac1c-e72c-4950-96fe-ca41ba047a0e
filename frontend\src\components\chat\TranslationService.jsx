import React, { useState, useEffect } from 'react';
import { Globe, Languages, Volume2, Copy, Check, Loader } from 'lucide-react';

const TranslationService = ({ message, onTranslate, user }) => {
  const [isTranslating, setIsTranslating] = useState(false);
  const [translations, setTranslations] = useState({});
  const [selectedLanguage, setSelectedLanguage] = useState('auto');
  const [detectedLanguage, setDetectedLanguage] = useState(null);
  const [showTranslation, setShowTranslation] = useState(false);
  const [copied, setCopied] = useState(false);

  // Supported languages
  const languages = [
    { code: 'auto', name: 'Auto Detect', flag: '🌐' },
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'es', name: 'Spanish', flag: '🇪🇸' },
    { code: 'fr', name: 'French', flag: '🇫🇷' },
    { code: 'de', name: 'German', flag: '🇩🇪' },
    { code: 'it', name: 'Italian', flag: '🇮🇹' },
    { code: 'pt', name: 'Portuguese', flag: '🇵🇹' },
    { code: 'ru', name: 'Russian', flag: '🇷🇺' },
    { code: 'ja', name: 'Japanese', flag: '🇯🇵' },
    { code: 'ko', name: 'Korean', flag: '🇰🇷' },
    { code: 'zh', name: 'Chinese', flag: '🇨🇳' },
    { code: 'ar', name: 'Arabic', flag: '🇸🇦' },
    { code: 'hi', name: 'Hindi', flag: '🇮🇳' },
    { code: 'th', name: 'Thai', flag: '🇹🇭' },
    { code: 'vi', name: 'Vietnamese', flag: '🇻🇳' },
    { code: 'nl', name: 'Dutch', flag: '🇳🇱' },
    { code: 'sv', name: 'Swedish', flag: '🇸🇪' },
    { code: 'da', name: 'Danish', flag: '🇩🇰' },
    { code: 'no', name: 'Norwegian', flag: '🇳🇴' },
    { code: 'fi', name: 'Finnish', flag: '🇫🇮' }
  ];

  // Mock translation API (in real app, use Google Translate, Azure Translator, etc.)
  const mockTranslate = async (text, targetLang, sourceLang = 'auto') => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000));
    
    // Mock translations for demonstration
    const mockTranslations = {
      'Hello, how can I help you?': {
        es: 'Hola, ¿cómo puedo ayudarte?',
        fr: 'Bonjour, comment puis-je vous aider?',
        de: 'Hallo, wie kann ich Ihnen helfen?',
        it: 'Ciao, come posso aiutarti?',
        pt: 'Olá, como posso ajudá-lo?',
        ru: 'Привет, как я могу вам помочь?',
        ja: 'こんにちは、どのようにお手伝いできますか？',
        ko: '안녕하세요, 어떻게 도와드릴까요?',
        zh: '你好，我能为您做些什么？',
        ar: 'مرحبا، كيف يمكنني مساعدتك؟',
        hi: 'नमस्ते, मैं आपकी कैसे सहायता कर सकता हूँ?'
      },
      'Thank you for your help': {
        es: 'Gracias por tu ayuda',
        fr: 'Merci pour votre aide',
        de: 'Danke für Ihre Hilfe',
        it: 'Grazie per il tuo aiuto',
        pt: 'Obrigado pela sua ajuda',
        ru: 'Спасибо за вашу помощь',
        ja: 'ご協力ありがとうございます',
        ko: '도움을 주셔서 감사합니다',
        zh: '谢谢你的帮助',
        ar: 'شكرا لمساعدتك',
        hi: 'आपकी सहायता के लिए धन्यवाद'
      }
    };

    // Check if we have a mock translation
    const translation = mockTranslations[text]?.[targetLang];
    if (translation) {
      return {
        translatedText: translation,
        detectedSourceLanguage: sourceLang === 'auto' ? 'en' : sourceLang,
        confidence: 0.95
      };
    }

    // Generate a mock translation for other texts
    return {
      translatedText: `[${targetLang.toUpperCase()}] ${text}`,
      detectedSourceLanguage: sourceLang === 'auto' ? 'en' : sourceLang,
      confidence: 0.85
    };
  };

  const detectLanguage = async (text) => {
    // Mock language detection
    const commonWords = {
      en: ['the', 'and', 'is', 'in', 'to', 'of', 'a', 'that', 'it', 'with'],
      es: ['el', 'la', 'de', 'que', 'y', 'a', 'en', 'un', 'es', 'se'],
      fr: ['le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir'],
      de: ['der', 'die', 'und', 'in', 'den', 'von', 'zu', 'das', 'mit', 'sich'],
      it: ['il', 'di', 'che', 'e', 'la', 'per', 'un', 'in', 'con', 'del'],
      pt: ['o', 'de', 'que', 'e', 'do', 'da', 'em', 'um', 'para', 'é'],
      ru: ['в', 'и', 'не', 'на', 'я', 'быть', 'он', 'с', 'что', 'а'],
      ja: ['の', 'に', 'は', 'を', 'た', 'が', 'で', 'て', 'と', 'し'],
      zh: ['的', '一', '是', '在', '不', '了', '有', '和', '人', '这'],
      ar: ['في', 'من', 'إلى', 'على', 'أن', 'هذا', 'هذه', 'التي', 'الذي', 'كان']
    };

    const words = text.toLowerCase().split(/\s+/);
    let bestMatch = 'en';
    let maxMatches = 0;

    for (const [lang, commonWordsInLang] of Object.entries(commonWords)) {
      const matches = words.filter(word => commonWordsInLang.includes(word)).length;
      if (matches > maxMatches) {
        maxMatches = matches;
        bestMatch = lang;
      }
    }

    return bestMatch;
  };

  const handleTranslate = async (targetLang) => {
    if (!message.content || isTranslating) return;

    setIsTranslating(true);
    try {
      // Detect source language if auto
      let sourceLang = selectedLanguage;
      if (selectedLanguage === 'auto') {
        sourceLang = await detectLanguage(message.content);
        setDetectedLanguage(sourceLang);
      }

      // Skip translation if source and target are the same
      if (sourceLang === targetLang) {
        setTranslations(prev => ({
          ...prev,
          [targetLang]: {
            text: message.content,
            confidence: 1.0,
            isOriginal: true
          }
        }));
        setIsTranslating(false);
        return;
      }

      // Translate
      const result = await mockTranslate(message.content, targetLang, sourceLang);
      
      setTranslations(prev => ({
        ...prev,
        [targetLang]: {
          text: result.translatedText,
          confidence: result.confidence,
          detectedSource: result.detectedSourceLanguage,
          isOriginal: false
        }
      }));

      if (onTranslate) {
        onTranslate(message.id, targetLang, result.translatedText);
      }
    } catch (error) {
      console.error('Translation error:', error);
    } finally {
      setIsTranslating(false);
    }
  };

  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  const speakText = (text, lang) => {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = lang;
      speechSynthesis.speak(utterance);
    }
  };

  // Auto-translate for VIP users or if user has auto-translate enabled
  useEffect(() => {
    if (user?.autoTranslate && user?.preferredLanguage && user.preferredLanguage !== 'en') {
      handleTranslate(user.preferredLanguage);
    }
  }, [message.content, user?.autoTranslate, user?.preferredLanguage]);

  return (
    <div className="relative">
      {/* Translation Toggle Button */}
      <button
        onClick={() => setShowTranslation(!showTranslation)}
        className="p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
        title="Translate message"
      >
        <Languages className="w-4 h-4" />
      </button>

      {/* Translation Panel */}
      {showTranslation && (
        <div className="absolute top-8 right-0 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 w-80 z-10">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-medium text-gray-900 dark:text-white">
              Translate Message
            </h4>
            <button
              onClick={() => setShowTranslation(false)}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              ×
            </button>
          </div>

          {/* Source Language Detection */}
          {detectedLanguage && (
            <div className="mb-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded">
              <p className="text-sm text-blue-700 dark:text-blue-300">
                Detected language: {languages.find(l => l.code === detectedLanguage)?.name || detectedLanguage}
              </p>
            </div>
          )}

          {/* Language Selection */}
          <div className="mb-3">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Translate to:
            </label>
            <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
              {languages.filter(lang => lang.code !== 'auto').map((lang) => (
                <button
                  key={lang.code}
                  onClick={() => handleTranslate(lang.code)}
                  disabled={isTranslating}
                  className="flex items-center space-x-2 p-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors disabled:opacity-50"
                >
                  <span>{lang.flag}</span>
                  <span className="text-sm">{lang.name}</span>
                  {isTranslating && (
                    <Loader className="w-3 h-3 animate-spin ml-auto" />
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Translations */}
          {Object.keys(translations).length > 0 && (
            <div className="space-y-3">
              <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Translations:
              </h5>
              {Object.entries(translations).map(([langCode, translation]) => {
                const language = languages.find(l => l.code === langCode);
                return (
                  <div
                    key={langCode}
                    className="border border-gray-200 dark:border-gray-600 rounded p-3"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span>{language?.flag}</span>
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          {language?.name}
                        </span>
                        {translation.isOriginal && (
                          <span className="text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 px-2 py-1 rounded">
                            Original
                          </span>
                        )}
                      </div>
                      <div className="flex items-center space-x-1">
                        <button
                          onClick={() => speakText(translation.text, langCode)}
                          className="p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400"
                          title="Listen"
                        >
                          <Volume2 className="w-3 h-3" />
                        </button>
                        <button
                          onClick={() => copyToClipboard(translation.text)}
                          className="p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400"
                          title="Copy"
                        >
                          {copied ? <Check className="w-3 h-3" /> : <Copy className="w-3 h-3" />}
                        </button>
                      </div>
                    </div>
                    <p className="text-sm text-gray-900 dark:text-white">
                      {translation.text}
                    </p>
                    {!translation.isOriginal && (
                      <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                        Confidence: {Math.round(translation.confidence * 100)}%
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}

          {/* Quick Actions */}
          <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
            <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
              <span>Quick translate:</span>
              <div className="flex space-x-1">
                {['es', 'fr', 'de', 'zh'].map((langCode) => {
                  const lang = languages.find(l => l.code === langCode);
                  return (
                    <button
                      key={langCode}
                      onClick={() => handleTranslate(langCode)}
                      className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
                      title={lang?.name}
                    >
                      {lang?.flag}
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Translation Settings Component
export const TranslationSettings = ({ user, onUpdateSettings }) => {
  const [settings, setSettings] = useState({
    autoTranslate: user?.autoTranslate || false,
    preferredLanguage: user?.preferredLanguage || 'en',
    showOriginal: user?.showOriginal || true,
    translateIncoming: user?.translateIncoming || false,
    translateOutgoing: user?.translateOutgoing || false
  });

  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'es', name: 'Spanish', flag: '🇪🇸' },
    { code: 'fr', name: 'French', flag: '🇫🇷' },
    { code: 'de', name: 'German', flag: '🇩🇪' },
    { code: 'it', name: 'Italian', flag: '🇮🇹' },
    { code: 'pt', name: 'Portuguese', flag: '🇵🇹' },
    { code: 'ru', name: 'Russian', flag: '🇷🇺' },
    { code: 'ja', name: 'Japanese', flag: '🇯🇵' },
    { code: 'ko', name: 'Korean', flag: '🇰🇷' },
    { code: 'zh', name: 'Chinese', flag: '🇨🇳' }
  ];

  const handleSettingChange = (key, value) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    if (onUpdateSettings) {
      onUpdateSettings(newSettings);
    }
  };

  return (
    <div className="p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Translation Settings
      </h3>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Preferred Language
          </label>
          <select
            value={settings.preferredLanguage}
            onChange={(e) => handleSettingChange('preferredLanguage', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            {languages.map((lang) => (
              <option key={lang.code} value={lang.code}>
                {lang.flag} {lang.name}
              </option>
            ))}
          </select>
        </div>

        <div className="space-y-3">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={settings.autoTranslate}
              onChange={(e) => handleSettingChange('autoTranslate', e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
              Auto-translate messages to my preferred language
            </span>
          </label>

          <label className="flex items-center">
            <input
              type="checkbox"
              checked={settings.translateIncoming}
              onChange={(e) => handleSettingChange('translateIncoming', e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
              Auto-translate incoming messages
            </span>
          </label>

          <label className="flex items-center">
            <input
              type="checkbox"
              checked={settings.translateOutgoing}
              onChange={(e) => handleSettingChange('translateOutgoing', e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
              Auto-translate my messages to room language
            </span>
          </label>

          <label className="flex items-center">
            <input
              type="checkbox"
              checked={settings.showOriginal}
              onChange={(e) => handleSettingChange('showOriginal', e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
              Always show original text alongside translation
            </span>
          </label>
        </div>
      </div>
    </div>
  );
};

export default TranslationService;
