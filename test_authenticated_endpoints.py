#!/usr/bin/env python3
"""
Test script to check authenticated API endpoints
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def test_authenticated_flow():
    """Test the full authenticated flow"""
    print("Testing Authenticated API Flow")
    print("=" * 50)
    
    # Step 1: Login to get token
    print("\n1. Logging in...")
    login_response = requests.post(f"{BASE_URL}/api/auth/login/", json={
        'email': '<EMAIL>',
        'password': 'testpass123'
    })
    
    if login_response.status_code == 200:
        login_data = login_response.json()
        token = login_data['tokens']['access']
        user = login_data['user']
        print(f"✅ Login successful! User: {user['username']}")
        
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        # Step 2: Test room creation
        print("\n2. Testing room creation...")
        room_data = {
            'name': 'Test Room',
            'description': 'A test room created via API',
            'room_type': 'group',  # Valid choice: group, private, channel, live, vip, support
            'is_public': True,
            'is_vip_only': False,
            'max_members': 100,
            'tags': ['test', 'api'],
            'welcome_message': 'Welcome to the test room!',
            'rules': 'Be nice to each other'
        }
        
        room_response = requests.post(f"{BASE_URL}/api/chat/rooms/", 
                                    json=room_data, headers=headers)
        
        if room_response.status_code == 201:
            room = room_response.json()
            print(f"✅ Room created successfully! Room ID: {room['id']}")
            
            # Step 3: Test getting rooms
            print("\n3. Testing room list...")
            rooms_response = requests.get(f"{BASE_URL}/api/chat/rooms/", headers=headers)
            
            if rooms_response.status_code == 200:
                rooms = rooms_response.json()
                print(f"✅ Retrieved {len(rooms.get('results', []))} rooms")
                
                # Step 4: Test sending a message
                print("\n4. Testing message sending...")
                message_data = {
                    'content': 'Hello from API test!',
                    'message_type': 'text',
                    'room': room['id']
                }

                message_response = requests.post(
                    f"{BASE_URL}/api/chat/messages/",
                    json=message_data, headers=headers
                )
                
                if message_response.status_code == 201:
                    message = message_response.json()
                    print(f"✅ Message sent successfully! Message ID: {message['id']}")
                else:
                    print(f"❌ Message sending failed: {message_response.status_code}")
                    print(f"   Error: {message_response.text}")
                    
            else:
                print(f"❌ Room list failed: {rooms_response.status_code}")
                print(f"   Error: {rooms_response.text}")
                
        else:
            print(f"❌ Room creation failed: {room_response.status_code}")
            print(f"   Error: {room_response.text}")
            
        # Step 5: Test VIP endpoints
        print("\n5. Testing VIP endpoints...")
        vip_response = requests.get(f"{BASE_URL}/api/vip/subscription/", headers=headers)
        print(f"VIP subscription status: {vip_response.status_code}")
        
        # Step 6: Test admin endpoints (will fail unless user is admin)
        print("\n6. Testing admin endpoints...")
        admin_response = requests.get(f"{BASE_URL}/api/admin/users/", headers=headers)
        print(f"Admin users endpoint: {admin_response.status_code}")
        
        # Step 7: Test logout
        print("\n7. Testing logout...")
        logout_response = requests.post(f"{BASE_URL}/api/auth/logout/", 
                                      json={'refresh_token': login_data['tokens']['refresh']},
                                      headers=headers)
        print(f"Logout status: {logout_response.status_code}")
        
    else:
        print(f"❌ Login failed: {login_response.status_code}")
        print(f"   Error: {login_response.text}")

if __name__ == "__main__":
    test_authenticated_flow()
