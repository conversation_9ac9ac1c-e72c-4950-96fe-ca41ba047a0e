import React, { useState, useEffect } from 'react';
import { 
  MessageCircle, 
  Users, 
  Crown, 
  Radio, 
  Plus, 
  Search, 
  Sparkles,
  Zap,
  Heart,
  Star,
  Globe,
  Shield,
  Bot,
  Headphones
} from 'lucide-react';

const WelcomeScreen = ({ user, onCreateRoom, onBrowseRooms, onTryDemo, showDemoOption = false }) => {
  const [currentTip, setCurrentTip] = useState(0);
  const [showAnimation, setShowAnimation] = useState(true);

  const tips = [
    {
      icon: MessageCircle,
      title: "Start Conversations",
      description: "Create rooms for different topics and invite others to join the discussion."
    },
    {
      icon: Crown,
      title: "VIP Features",
      description: "Upgrade to VIP for exclusive rooms, custom emojis, and priority support."
    },
    {
      icon: Bo<PERSON>,
      title: "AI Assistant",
      description: "Use our intelligent chatbot for quick answers and automated responses."
    },
    {
      icon: Radio,
      title: "Live Events",
      description: "Host live streaming events and real-time discussions with your community."
    },
    {
      icon: Shield,
      title: "Secure & Private",
      description: "Your conversations are protected with end-to-end encryption and privacy controls."
    }
  ];

  const features = [
    { icon: Users, label: "Team Collaboration", color: "text-blue-500" },
    { icon: Globe, label: "Global Community", color: "text-green-500" },
    { icon: Zap, label: "Real-time Messaging", color: "text-yellow-500" },
    { icon: Heart, label: "Reactions & Emojis", color: "text-pink-500" },
    { icon: Star, label: "Premium Features", color: "text-purple-500" },
    { icon: Headphones, label: "24/7 Support", color: "text-indigo-500" }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTip((prev) => (prev + 1) % tips.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [tips.length]);

  useEffect(() => {
    const timer = setTimeout(() => setShowAnimation(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  const currentTipData = tips[currentTip];
  const TipIcon = currentTipData.icon;

  return (
    <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 p-8">
      <div className="max-w-4xl w-full text-center space-y-8">
        {/* Animated Logo */}
        <div className={`transition-all duration-1000 ${showAnimation ? 'scale-0 opacity-0' : 'scale-100 opacity-100'}`}>
          <div className="w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
            <MessageCircle className="w-12 h-12 text-white" />
          </div>
        </div>

        {/* Welcome Message */}
        <div className={`transition-all duration-1000 delay-300 ${showAnimation ? 'translate-y-8 opacity-0' : 'translate-y-0 opacity-100'}`}>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            Welcome to{' '}
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              ChatFlow Pro
            </span>
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 mb-2">
            Hey {user?.display_name || user?.username}! 👋
          </p>
          <p className="text-lg text-gray-500 dark:text-gray-500">
            Ready to start your chat experience? Let's get you connected!
          </p>
        </div>

        {/* Rotating Tips */}
        <div className={`transition-all duration-1000 delay-500 ${showAnimation ? 'translate-y-8 opacity-0' : 'translate-y-0 opacity-100'}`}>
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 max-w-md mx-auto border border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <TipIcon className="w-6 h-6 text-white" />
              </div>
              <div className="flex-1 text-left">
                <h3 className="font-semibold text-gray-900 dark:text-white">
                  {currentTipData.title}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {currentTipData.description}
                </p>
              </div>
            </div>
            
            {/* Tip Indicators */}
            <div className="flex justify-center space-x-2 mt-4">
              {tips.map((_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full transition-all duration-300 ${
                    index === currentTip 
                      ? 'bg-blue-500 w-6' 
                      : 'bg-gray-300 dark:bg-gray-600'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className={`transition-all duration-1000 delay-700 ${showAnimation ? 'translate-y-8 opacity-0' : 'translate-y-0 opacity-100'}`}>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <button
              onClick={onCreateRoom}
              className="group bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center space-x-3"
            >
              <Plus className="w-6 h-6 group-hover:rotate-90 transition-transform duration-300" />
              <span>Create Your First Room</span>
              <Sparkles className="w-5 h-5 group-hover:animate-pulse" />
            </button>

            <button
              onClick={onBrowseRooms}
              className="group bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 px-8 py-4 rounded-xl font-semibold text-lg border-2 border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500 transition-all duration-300 transform hover:scale-105 flex items-center space-x-3"
            >
              <Search className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
              <span>Browse Existing Rooms</span>
            </button>

            {showDemoOption && onTryDemo && (
              <button
                onClick={onTryDemo}
                className="group bg-gradient-to-r from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center space-x-3"
              >
                <Zap className="w-6 h-6 group-hover:animate-bounce transition-transform duration-300" />
                <span>Try Demo Mode</span>
                <Star className="w-5 h-5 group-hover:animate-spin" />
              </button>
            )}
          </div>
        </div>

        {/* Features Grid */}
        <div className={`transition-all duration-1000 delay-900 ${showAnimation ? 'translate-y-8 opacity-0' : 'translate-y-0 opacity-100'}`}>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
            {features.map((feature, index) => {
              const FeatureIcon = feature.icon;
              return (
                <div
                  key={index}
                  className="group bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md hover:border-blue-300 dark:hover:border-blue-500 transition-all duration-300 cursor-pointer"
                >
                  <FeatureIcon className={`w-8 h-8 ${feature.color} mx-auto mb-2 group-hover:scale-110 transition-transform duration-300`} />
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white transition-colors duration-300">
                    {feature.label}
                  </p>
                </div>
              );
            })}
          </div>
        </div>

        {/* Quick Stats */}
        <div className={`transition-all duration-1000 delay-1100 ${showAnimation ? 'translate-y-8 opacity-0' : 'translate-y-0 opacity-100'}`}>
          <div className="flex justify-center space-x-8 text-center">
            <div className="group cursor-pointer">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300">
                10K+
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Active Users</div>
            </div>
            <div className="group cursor-pointer">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 group-hover:scale-110 transition-transform duration-300">
                500+
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Chat Rooms</div>
            </div>
            <div className="group cursor-pointer">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400 group-hover:scale-110 transition-transform duration-300">
                24/7
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Support</div>
            </div>
          </div>
        </div>

        {/* Footer Message */}
        <div className={`transition-all duration-1000 delay-1300 ${showAnimation ? 'translate-y-8 opacity-0' : 'translate-y-0 opacity-100'}`}>
          <p className="text-sm text-gray-400 dark:text-gray-500">
            Need help getting started? Check out our{' '}
            <button className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 underline">
              quick start guide
            </button>{' '}
            or{' '}
            <button className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 underline">
              contact support
            </button>
          </p>
        </div>
      </div>
    </div>
  );
};

export default WelcomeScreen;
