"""
ASGI config for ChatFlow Pro project.

It exposes the ASGI callable as a module-level variable named ``application``.
This supports both HTTP and WebSocket connections for real-time chat functionality.
"""

import os
import django
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter

from channels.security.websocket import AllowedHostsOriginValidator
from channels_auth_token_middlewares.middleware import SimpleJWTAuthTokenMiddlewareStack

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatflow.settings')

# Initialize Django ASGI application early to ensure the AppRegistry
# is populated before importing code that may import ORM models.
django_asgi_app = get_asgi_application()

# Import routing after Django is set up
from apps.chat.routing import websocket_urlpatterns

application = ProtocolTypeRouter({
    "http": django_asgi_app,
    "websocket": AllowedHostsOriginValidator(
        SimpleJWTAuthTokenMiddlewareStack(
            URL<PERSON>outer(
                websocket_urlpatterns
            )
        )
    ),
})
