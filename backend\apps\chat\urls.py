from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views

router = DefaultRouter()
router.register(r'rooms', views.ChatRoomViewSet)
router.register(r'messages', views.MessageViewSet)
router.register(r'agents', views.ChatAgentViewSet)
router.register(r'queue', views.ChatQueueViewSet)
router.register(r'canned-responses', views.CannedResponseViewSet)
router.register(r'bots', views.ChatBotViewSet)
router.register(r'feedback', views.CustomerFeedbackViewSet)
router.register(r'integrations', views.ChatIntegrationViewSet)
router.register(r'voice-calls', views.VoiceCallViewSet)

urlpatterns = [
    path('', include(router.urls)),
    
    # File upload endpoints
    path('upload/', views.FileUploadView.as_view(), name='file_upload'),
    path('upload-voice/', views.VoiceUploadView.as_view(), name='voice_upload'),
    
    # Search endpoints
    path('search/', views.MessageSearchView.as_view(), name='message_search'),
    
    # Analytics endpoints
    path('analytics/', views.ChatAnalyticsView.as_view(), name='chat_analytics'),
    
    # Visitor tracking
    path('visitor-tracking/', views.VisitorTrackingView.as_view(), name='visitor_tracking'),


]
