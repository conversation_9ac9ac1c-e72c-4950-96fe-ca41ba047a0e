from django.urls import path
from apps.authentication.views import (
    AdminUsersListView, AdminUserDetailView, 
    AdminSuspendUserView, AdminActivateUserView
)
from apps.chat.views import (
    AdminRoomsListView, AdminRoomDetailView,
    AdminBotsListView, AdminReportsListView, AdminSettingsView
)

urlpatterns = [
    # User management
    path('users/', AdminUsersListView.as_view(), name='admin_users'),
    path('users/<uuid:user_id>/', AdminUserDetailView.as_view(), name='admin_user_detail'),
    path('users/<uuid:user_id>/suspend/', AdminSuspendUserView.as_view(), name='admin_user_suspend'),
    path('users/<uuid:user_id>/activate/', AdminActivateUserView.as_view(), name='admin_user_activate'),
    
    # Room management
    path('rooms/', AdminRoomsListView.as_view(), name='admin_rooms'),
    path('rooms/<uuid:room_id>/', AdminRoomDetailView.as_view(), name='admin_room_detail'),
    
    # Bot management
    path('bots/', AdminBotsListView.as_view(), name='admin_bots'),
    
    # Reports
    path('reports/', AdminReportsListView.as_view(), name='admin_reports'),
    
    # Settings
    path('settings/', AdminSettingsView.as_view(), name='admin_settings'),
]
