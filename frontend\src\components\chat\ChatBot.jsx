import React, { useState, useEffect } from 'react';
import { Bot, Send, Loader, MessageCircle, X, Minimize2, Maximize2 } from 'lucide-react';
import CannedResponses from './CannedResponses';
import TranslationService from './TranslationService';

const ChatBot = ({ isOpen, onToggle, user, room }) => {
  const [messages, setMessages] = useState([]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [botPersonality, setBotPersonality] = useState('helpful');

  // Predefined responses and patterns
  const botResponses = {
    greetings: [
      "Hello! I'm your AI assistant. How can I help you today?",
      "Hi there! Welcome to our chat platform. What can I do for you?",
      "Greetings! I'm here to assist you with any questions you might have."
    ],
    help: [
      "I can help you with:\n• Navigating the chat platform\n• Finding rooms and users\n• Understanding VIP features\n• Technical support\n• General questions",
      "Here are some things I can assist with:\n• Room management\n• User profiles\n• File sharing\n• Voice/video calls\n• Platform features"
    ],
    vip: [
      "VIP members enjoy exclusive benefits:\n• Access to VIP-only rooms\n• Priority support\n• Custom emojis\n• Early access to features\n• Special badges",
      "Our VIP program offers premium features like exclusive channels, priority support, and custom emojis. Would you like to learn more about upgrading?"
    ],
    features: [
      "Our platform offers:\n• Real-time messaging\n• Voice & video calls\n• File sharing\n• Screen sharing\n• Live events\n• Group chats\n• Private messaging",
      "Key features include real-time chat, multimedia sharing, voice/video calls, live events, and much more!"
    ],
    support: [
      "For technical support, you can:\n• Contact our support team\n• Check the help documentation\n• Report bugs through the feedback system\n• Join the support room",
      "I'm here to help! If you need human assistance, I can connect you with our support team."
    ],
    default: [
      "I'm not sure I understand. Could you rephrase that?",
      "That's an interesting question! Could you provide more details?",
      "I'd be happy to help, but I need a bit more information.",
      "Let me think about that... Could you be more specific?"
    ]
  };

  // Canned responses for quick replies
  const cannedResponses = [
    { text: "How do I join a room?", category: "help" },
    { text: "What are VIP features?", category: "vip" },
    { text: "How do I start a video call?", category: "features" },
    { text: "I need technical support", category: "support" },
    { text: "How do I upload files?", category: "help" },
    { text: "What's the difference between public and private rooms?", category: "help" }
  ];

  useEffect(() => {
    if (isOpen && messages.length === 0) {
      // Send welcome message
      const welcomeMessage = {
        id: Date.now(),
        text: user?.is_vip 
          ? `Welcome back, ${user.display_name}! As a VIP member, you have access to exclusive features. How can I assist you today?`
          : `Hello ${user?.display_name || 'there'}! I'm your AI assistant. How can I help you today?`,
        sender: 'bot',
        timestamp: new Date(),
        type: 'text'
      };
      setMessages([welcomeMessage]);
    }
  }, [isOpen, user]);

  const analyzeMessage = (text) => {
    const lowerText = text.toLowerCase();
    
    if (lowerText.match(/hello|hi|hey|greetings|good morning|good afternoon/)) {
      return 'greetings';
    }
    if (lowerText.match(/help|assist|support|how/)) {
      return 'help';
    }
    if (lowerText.match(/vip|premium|exclusive|upgrade/)) {
      return 'vip';
    }
    if (lowerText.match(/feature|function|capability|what can/)) {
      return 'features';
    }
    if (lowerText.match(/problem|issue|bug|error|technical|support/)) {
      return 'support';
    }
    
    return 'default';
  };

  const getBotResponse = (category) => {
    const responses = botResponses[category] || botResponses.default;
    return responses[Math.floor(Math.random() * responses.length)];
  };

  const handleSendMessage = async (text = inputText) => {
    if (!text.trim()) return;

    // Add user message
    const userMessage = {
      id: Date.now(),
      text: text.trim(),
      sender: 'user',
      timestamp: new Date(),
      type: 'text'
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsTyping(true);

    // Simulate bot thinking time
    setTimeout(() => {
      const category = analyzeMessage(text);
      const botResponse = getBotResponse(category);

      const botMessage = {
        id: Date.now() + 1,
        text: botResponse,
        sender: 'bot',
        timestamp: new Date(),
        type: 'text',
        category
      };

      setMessages(prev => [...prev, botMessage]);
      setIsTyping(false);

      // Add follow-up suggestions for certain categories
      if (category === 'help') {
        setTimeout(() => {
          const suggestionMessage = {
            id: Date.now() + 2,
            text: "Would you like me to connect you with a human agent for more detailed assistance?",
            sender: 'bot',
            timestamp: new Date(),
            type: 'suggestion',
            actions: [
              { text: "Yes, connect me", action: "connect_agent" },
              { text: "No, thanks", action: "dismiss" }
            ]
          };
          setMessages(prev => [...prev, suggestionMessage]);
        }, 1000);
      }
    }, 1000 + Math.random() * 2000);
  };

  const handleAction = (action) => {
    switch (action) {
      case 'connect_agent':
        // Connect to human agent
        const agentMessage = {
          id: Date.now(),
          text: "I'm connecting you with a human agent. Please wait a moment...",
          sender: 'bot',
          timestamp: new Date(),
          type: 'system'
        };
        setMessages(prev => [...prev, agentMessage]);
        break;
      case 'dismiss':
        // Just acknowledge
        break;
    }
  };

  if (!isOpen) {
    return (
      <button
        onClick={onToggle}
        className="fixed bottom-4 right-4 w-14 h-14 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-200 hover:scale-110 z-50"
      >
        <Bot className="w-6 h-6" />
      </button>
    );
  }

  return (
    <div className={`fixed bottom-4 right-4 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50 transition-all duration-200 ${isMinimized ? 'h-14' : 'h-96'}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 bg-blue-500 text-white rounded-t-lg">
        <div className="flex items-center space-x-2">
          <Bot className="w-5 h-5" />
          <span className="font-medium">AI Assistant</span>
          <div className="w-2 h-2 bg-green-400 rounded-full"></div>
        </div>
        <div className="flex items-center space-x-1">
          <button
            onClick={() => setIsMinimized(!isMinimized)}
            className="p-1 hover:bg-blue-600 rounded"
          >
            {isMinimized ? <Maximize2 className="w-4 h-4" /> : <Minimize2 className="w-4 h-4" />}
          </button>
          <button
            onClick={onToggle}
            className="p-1 hover:bg-blue-600 rounded"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>

      {!isMinimized && (
        <>
          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-3 space-y-3 h-64">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs px-3 py-2 rounded-lg ${
                    message.sender === 'user'
                      ? 'bg-blue-500 text-white'
                      : message.type === 'system'
                      ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
                  }`}
                >
                  <p className="text-sm whitespace-pre-wrap">{message.text}</p>
                  
                  {message.actions && (
                    <div className="mt-2 space-y-1">
                      {message.actions.map((action, index) => (
                        <button
                          key={index}
                          onClick={() => handleAction(action.action)}
                          className="block w-full text-left text-xs bg-white/20 hover:bg-white/30 px-2 py-1 rounded"
                        >
                          {action.text}
                        </button>
                      ))}
                    </div>
                  )}
                  
                  <div className="text-xs opacity-70 mt-1">
                    {message.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              </div>
            ))}
            
            {isTyping && (
              <div className="flex justify-start">
                <div className="bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Quick Replies */}
          <div className="px-3 py-2 border-t border-gray-200 dark:border-gray-700">
            <div className="flex flex-wrap gap-1 mb-2">
              {cannedResponses.slice(0, 3).map((response, index) => (
                <button
                  key={index}
                  onClick={() => handleSendMessage(response.text)}
                  className="text-xs bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 px-2 py-1 rounded transition-colors"
                >
                  {response.text}
                </button>
              ))}
            </div>
          </div>

          {/* Input */}
          <div className="p-3 border-t border-gray-200 dark:border-gray-700">
            <form
              onSubmit={(e) => {
                e.preventDefault();
                handleSendMessage();
              }}
              className="flex space-x-2"
            >
              <input
                type="text"
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                placeholder="Type your message..."
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button
                type="submit"
                disabled={!inputText.trim() || isTyping}
                className="px-3 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
              >
                <Send className="w-4 h-4" />
              </button>
            </form>
          </div>
        </>
      )}
    </div>
  );
};

export default ChatBot;
