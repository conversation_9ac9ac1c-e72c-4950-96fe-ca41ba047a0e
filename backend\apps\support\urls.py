from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>outer
from . import views

router = DefaultRouter()

urlpatterns = [
    path('', include(router.urls)),
    
    # Support ticket endpoints
    path('tickets/', views.SupportTicketListView.as_view(), name='support_tickets'),
    path('tickets/create/', views.CreateSupportTicketView.as_view(), name='create_support_ticket'),
    path('tickets/<int:ticket_id>/', views.SupportTicketDetailView.as_view(), name='support_ticket_detail'),
    
    # FAQ endpoints
    path('faq/', views.FAQListView.as_view(), name='faq_list'),
    
    # Contact endpoints
    path('contact/', views.ContactFormView.as_view(), name='contact_form'),
]
