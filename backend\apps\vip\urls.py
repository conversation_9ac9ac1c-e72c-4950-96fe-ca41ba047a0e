from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(r'tiers', views.VIPTierViewSet)
router.register(r'subscriptions', views.VIPSubscriptionViewSet)
router.register(r'exclusive-rooms', views.VIPExclusiveRoomViewSet)
router.register(r'custom-emojis', views.VIPCustomEmojiViewSet)
router.register(r'benefits', views.VIPBenefitViewSet)
router.register(r'support-tickets', views.VIPSupportTicketViewSet)

urlpatterns = [
    path('', include(router.urls)),
    
    # Subscription management
    path('subscribe/', views.SubscribeView.as_view(), name='vip_subscribe'),
    path('cancel/', views.CancelSubscriptionView.as_view(), name='vip_cancel'),
    path('upgrade/', views.UpgradeSubscriptionView.as_view(), name='vip_upgrade'),
    
    # Payment webhooks
    path('webhooks/stripe/', views.StripeWebhookView.as_view(), name='stripe_webhook'),
    
    # VIP status check
    path('status/', views.VIPStatusView.as_view(), name='vip_status'),

    # Frontend compatibility aliases
    path('subscription/', views.VIPStatusView.as_view(), name='vip_subscription_alias'),
]
