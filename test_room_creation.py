#!/usr/bin/env python
"""
Test script to debug room creation issues
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def test_room_creation():
    print("Testing Room Creation Debug")
    print("=" * 50)
    
    # Step 1: Login
    print("\n1. Logging in...")
    login_data = {
        'username': 'testuser2',
        'password': 'testpass123'
    }
    
    login_response = requests.post(f"{BASE_URL}/api/auth/login/", json=login_data)
    
    if login_response.status_code == 200:
        token = login_response.json()['access']
        print(f"✅ Login successful! Token: {token[:20]}...")
        
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        # Step 2: Test minimal room creation
        print("\n2. Testing minimal room creation...")
        minimal_room_data = {
            'name': 'Test Room',
            'description': 'A test room',
            'room_type': 'group'
        }
        
        room_response = requests.post(
            f"{BASE_URL}/api/chat/rooms/",
            json=minimal_room_data,
            headers=headers
        )
        
        print(f"Status Code: {room_response.status_code}")
        print(f"Response: {room_response.text}")
        
        if room_response.status_code != 201:
            print("❌ Minimal room creation failed")
            
            # Step 3: Test with full frontend data
            print("\n3. Testing with full frontend data...")
            full_room_data = {
                'name': 'Full Test Room',
                'description': 'A full test room with all fields',
                'room_type': 'group',
                'is_public': True,
                'is_vip_only': False,
                'max_members': 1000,
                'password': '',
                'tags': ['test', 'debug'],
                'welcome_message': 'Welcome to the test room!',
                'rules': 'Be nice to each other'
            }
            
            full_response = requests.post(
                f"{BASE_URL}/api/chat/rooms/",
                json=full_room_data,
                headers=headers
            )
            
            print(f"Full Data Status Code: {full_response.status_code}")
            print(f"Full Data Response: {full_response.text}")
        else:
            print("✅ Room creation successful!")
            room = room_response.json()
            print(f"Created room: {room['name']} (ID: {room['id']})")
            
    else:
        print(f"❌ Login failed: {login_response.status_code}")
        print(f"Error: {login_response.text}")

if __name__ == "__main__":
    test_room_creation()
