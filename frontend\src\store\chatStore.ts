import { create } from 'zustand';
import { ChatRoom, Message, User, RoomMembership } from '@/types';
import { apiService } from '@/services/api';
import { wsService } from '@/services/websocket';

interface ChatState {
  // Rooms
  rooms: ChatRoom[];
  activeRoom: ChatRoom | null;
  roomMemberships: Record<string, RoomMembership>;
  
  // Messages
  messages: Record<string, Message[]>;
  loadingMessages: Record<string, boolean>;
  hasMoreMessages: Record<string, boolean>;
  
  // Real-time features
  typingUsers: Record<string, User[]>;
  onlineUsers: User[];
  
  // Connection status
  isConnected: boolean;
  isLoading: boolean;
  error: string | null;
}

interface ChatActions {
  // Room actions
  loadRooms: () => Promise<void>;
  joinRoom: (roomId: string) => Promise<void>;
  leaveRoom: (roomId: string) => void;
  setActiveRoom: (room: ChatRoom | null) => void;
  createRoom: (roomData: Partial<ChatRoom>) => Promise<ChatRoom | null>;
  
  // Message actions
  loadMessages: (roomId: string, page?: number) => Promise<void>;
  sendMessage: (roomId: string, content: string, replyTo?: string) => void;
  addMessage: (message: Message) => void;
  updateMessage: (messageId: string, updates: Partial<Message>) => void;
  deleteMessage: (messageId: string) => void;
  
  // Real-time actions
  handleUserJoined: (user: User, roomId: string) => void;
  handleUserLeft: (userId: string, roomId: string) => void;
  handleTypingStart: (user: User, roomId: string) => void;
  handleTypingStop: (userId: string, roomId: string) => void;
  handleMessageReaction: (messageId: string, emoji: string, userId: string, action: 'add' | 'remove') => void;
  
  // Utility actions
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  updateConnectionStatus: (connected: boolean) => void;
}

type ChatStore = ChatState & ChatActions;

export const useChatStore = create<ChatStore>((set, get) => ({
  // Initial state
  rooms: [],
  activeRoom: null,
  roomMemberships: {},
  messages: {},
  loadingMessages: {},
  hasMoreMessages: {},
  typingUsers: {},
  onlineUsers: [],
  isConnected: false,
  isLoading: false,
  error: null,

  // Room actions
  loadRooms: async () => {
    set({ isLoading: true, error: null });

    try {
      const response = await apiService.get<ChatRoom[]>('/chat/rooms/');
      
      if (response.success && response.data) {
        set({ 
          rooms: response.data,
          isLoading: false 
        });
      } else {
        set({ 
          error: response.message || 'Failed to load rooms',
          isLoading: false 
        });
      }
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to load rooms',
        isLoading: false 
      });
    }
  },

  joinRoom: async (roomId: string) => {
    try {
      // Join room via API
      const response = await apiService.post(`/chat/rooms/${roomId}/join/`);
      
      if (response.success) {
        // Connect to room via WebSocket
        try {
          await wsService.connectToRoom(roomId);
        } catch (wsError) {
          console.error('Failed to connect to room WebSocket:', wsError);
        }

        // Load room details
        const roomResponse = await apiService.get<ChatRoom>(`/chat/rooms/${roomId}/`);
        if (roomResponse.success && roomResponse.data) {
          const { rooms } = get();
          const updatedRooms = rooms.some(r => r.id === roomId)
            ? rooms.map(r => r.id === roomId ? roomResponse.data! : r)
            : [...rooms, roomResponse.data];

          set({ rooms: updatedRooms });
        }

        // Load messages for the room
        await get().loadMessages(roomId);
      }
    } catch (error: any) {
      set({ error: error.message || 'Failed to join room' });
    }
  },

  leaveRoom: (roomId: string) => {
    wsService.leaveRoom(roomId);
    
    const { activeRoom } = get();
    if (activeRoom?.id === roomId) {
      set({ activeRoom: null });
    }
  },

  setActiveRoom: (room: ChatRoom | null) => {
    const { activeRoom } = get();

    // Leave previous room
    if (activeRoom) {
      wsService.leaveRoom(activeRoom.id);
    }

    // Join new room
    if (room) {
      // Connect to the specific room's WebSocket
      wsService.connectToRoom(room.id).catch(error => {
        console.error('Failed to connect to room WebSocket:', error);
      });
      get().loadMessages(room.id);
    }

    set({ activeRoom: room });
  },

  createRoom: async (roomData: Partial<ChatRoom>) => {
    try {
      const response = await apiService.post<ChatRoom>('/chat/rooms/', roomData);
      
      if (response.success && response.data) {
        const { rooms } = get();
        set({ rooms: [...rooms, response.data] });
        return response.data;
      } else {
        set({ error: response.message || 'Failed to create room' });
        return null;
      }
    } catch (error: any) {
      set({ error: error.message || 'Failed to create room' });
      return null;
    }
  },

  // Message actions
  loadMessages: async (roomId: string, page = 1) => {
    const { loadingMessages } = get();
    if (loadingMessages[roomId]) return;

    set({ 
      loadingMessages: { ...loadingMessages, [roomId]: true },
      error: null 
    });

    try {
      const response = await apiService.get<{
        results: Message[];
        next: string | null;
      }>(`/chat/rooms/${roomId}/messages/?page=${page}`);
      
      if (response.success && response.data) {
        const { messages, hasMoreMessages } = get();
        const existingMessages = messages[roomId] || [];
        
        const newMessages = page === 1 
          ? response.data.results
          : [...existingMessages, ...response.data.results];
        
        set({
          messages: { ...messages, [roomId]: newMessages },
          hasMoreMessages: { ...hasMoreMessages, [roomId]: !!response.data.next },
          loadingMessages: { ...loadingMessages, [roomId]: false },
        });
      } else {
        set({ 
          error: response.message || 'Failed to load messages',
          loadingMessages: { ...loadingMessages, [roomId]: false },
        });
      }
    } catch (error: any) {
      set({ 
        error: error.message || 'Failed to load messages',
        loadingMessages: { ...loadingMessages, [roomId]: false },
      });
    }
  },

  sendMessage: (roomId: string, content: string, replyTo?: string) => {
    wsService.sendMessage(roomId, content, replyTo);
  },

  addMessage: (message: Message) => {
    const { messages } = get();
    const roomMessages = messages[message.room] || [];
    
    // Check if message already exists (avoid duplicates)
    if (!roomMessages.find(m => m.id === message.id)) {
      set({
        messages: {
          ...messages,
          [message.room]: [...roomMessages, message],
        },
      });
    }
  },

  updateMessage: (messageId: string, updates: Partial<Message>) => {
    const { messages } = get();
    const updatedMessages = { ...messages };
    
    Object.keys(updatedMessages).forEach(roomId => {
      updatedMessages[roomId] = updatedMessages[roomId].map(message =>
        message.id === messageId ? { ...message, ...updates } : message
      );
    });
    
    set({ messages: updatedMessages });
  },

  deleteMessage: (messageId: string) => {
    const { messages } = get();
    const updatedMessages = { ...messages };
    
    Object.keys(updatedMessages).forEach(roomId => {
      updatedMessages[roomId] = updatedMessages[roomId].filter(
        message => message.id !== messageId
      );
    });
    
    set({ messages: updatedMessages });
  },

  // Real-time actions
  handleUserJoined: (user: User, roomId: string) => {
    // Update online users if not already present
    const { onlineUsers } = get();
    if (!onlineUsers.find(u => u.id === user.id)) {
      set({ onlineUsers: [...onlineUsers, user] });
    }
  },

  handleUserLeft: (userId: string, roomId: string) => {
    const { onlineUsers, typingUsers } = get();
    
    // Remove from online users
    set({ 
      onlineUsers: onlineUsers.filter(u => u.id !== userId),
      typingUsers: {
        ...typingUsers,
        [roomId]: (typingUsers[roomId] || []).filter(u => u.id !== userId),
      },
    });
  },

  handleTypingStart: (user: User, roomId: string) => {
    const { typingUsers } = get();
    const roomTypingUsers = typingUsers[roomId] || [];
    
    if (!roomTypingUsers.find(u => u.id === user.id)) {
      set({
        typingUsers: {
          ...typingUsers,
          [roomId]: [...roomTypingUsers, user],
        },
      });
    }
  },

  handleTypingStop: (userId: string, roomId: string) => {
    const { typingUsers } = get();
    
    set({
      typingUsers: {
        ...typingUsers,
        [roomId]: (typingUsers[roomId] || []).filter(u => u.id !== userId),
      },
    });
  },

  handleMessageReaction: (messageId: string, emoji: string, userId: string, action: 'add' | 'remove') => {
    // This would update the message reactions in the messages state
    // Implementation depends on how reactions are structured in the Message type
    console.log('Message reaction:', { messageId, emoji, userId, action });
  },

  // Utility actions
  clearError: () => {
    set({ error: null });
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  updateConnectionStatus: (connected: boolean) => {
    set({ isConnected: connected });
  },
}));

// WebSocket event handlers
export const setupChatWebSocketHandlers = () => {
  const store = useChatStore.getState();

  wsService.on('connect', () => {
    store.updateConnectionStatus(true);
  });

  wsService.on('disconnect', () => {
    store.updateConnectionStatus(false);
  });

  wsService.on('chat_message', (data: any) => {
    if (data.message) {
      store.addMessage(data.message);
    }
  });

  wsService.on('user_joined', (data: any) => {
    if (data.user_id && data.room_id) {
      const user: User = {
        id: data.user_id,
        username: data.username,
        display_name: data.display_name,
        avatar: data.avatar,
      } as User;
      store.handleUserJoined(user, data.room_id);
    }
  });

  wsService.on('user_left', (data: any) => {
    if (data.user_id && data.room_id) {
      store.handleUserLeft(data.user_id, data.room_id);
    }
  });

  wsService.on('typing_start', (data: any) => {
    if (data.user_id && data.room_id) {
      const user: User = {
        id: data.user_id,
        username: data.username,
      } as User;
      store.handleTypingStart(user, data.room_id);
    }
  });

  wsService.on('typing_stop', (data: any) => {
    if (data.user_id && data.room_id) {
      store.handleTypingStop(data.user_id, data.room_id);
    }
  });

  wsService.on('message_reaction', (data: any) => {
    if (data.message_id && data.emoji && data.user_id && data.action) {
      store.handleMessageReaction(data.message_id, data.emoji, data.user_id, data.action);
    }
  });
};
