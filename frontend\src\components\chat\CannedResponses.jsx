import React, { useState, useEffect } from 'react';
import { MessageSquare, Plus, Edit3, Trash2, Search, Tag, Clock, Star } from 'lucide-react';

const CannedResponses = ({ isOpen, onClose, onSelectResponse, user }) => {
  const [responses, setResponses] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [isCreating, setIsCreating] = useState(false);
  const [editingResponse, setEditingResponse] = useState(null);
  const [newResponse, setNewResponse] = useState({
    title: '',
    content: '',
    category: 'general',
    tags: [],
    isPrivate: false
  });

  const categories = [
    { id: 'all', name: 'All Responses', icon: MessageSquare },
    { id: 'general', name: 'General', icon: MessageSquare },
    { id: 'support', name: 'Support', icon: MessageSquare },
    { id: 'sales', name: 'Sales', icon: MessageSquare },
    { id: 'technical', name: 'Technical', icon: MessageSquare },
    { id: 'vip', name: 'VIP', icon: Star },
    { id: 'greeting', name: 'Greetings', icon: MessageSquare },
    { id: 'closing', name: 'Closing', icon: MessageSquare }
  ];

  // Default canned responses
  const defaultResponses = [
    {
      id: 1,
      title: 'Welcome Message',
      content: 'Hello! Welcome to our chat platform. How can I assist you today?',
      category: 'greeting',
      tags: ['welcome', 'greeting'],
      isPrivate: false,
      usageCount: 45,
      lastUsed: new Date(Date.now() - 86400000),
      createdBy: 'System'
    },
    {
      id: 2,
      title: 'Technical Support',
      content: 'I understand you\'re experiencing a technical issue. Let me help you resolve this. Could you please provide more details about the problem?',
      category: 'technical',
      tags: ['support', 'technical', 'help'],
      isPrivate: false,
      usageCount: 32,
      lastUsed: new Date(Date.now() - 3600000),
      createdBy: 'Support Team'
    },
    {
      id: 3,
      title: 'VIP Welcome',
      content: 'Welcome to our VIP support! As a valued VIP member, you have priority access to our premium features and dedicated support team.',
      category: 'vip',
      tags: ['vip', 'welcome', 'premium'],
      isPrivate: false,
      usageCount: 18,
      lastUsed: new Date(Date.now() - 7200000),
      createdBy: 'VIP Team'
    },
    {
      id: 4,
      title: 'File Upload Help',
      content: 'To upload files, simply click the paperclip icon in the chat input area. You can upload images, documents, and other files up to 10MB.',
      category: 'support',
      tags: ['files', 'upload', 'help'],
      isPrivate: false,
      usageCount: 28,
      lastUsed: new Date(Date.now() - 1800000),
      createdBy: user?.username || 'Agent'
    },
    {
      id: 5,
      title: 'Video Call Instructions',
      content: 'To start a video call, click the video camera icon in the chat header. Make sure you have granted camera and microphone permissions.',
      category: 'technical',
      tags: ['video', 'call', 'instructions'],
      isPrivate: false,
      usageCount: 22,
      lastUsed: new Date(Date.now() - 5400000),
      createdBy: user?.username || 'Agent'
    },
    {
      id: 6,
      title: 'Closing Message',
      content: 'Thank you for contacting us! If you have any other questions, please don\'t hesitate to reach out. Have a great day!',
      category: 'closing',
      tags: ['closing', 'thank you', 'goodbye'],
      isPrivate: false,
      usageCount: 67,
      lastUsed: new Date(Date.now() - 900000),
      createdBy: 'System'
    }
  ];

  useEffect(() => {
    // Load responses from localStorage or use defaults
    const savedResponses = localStorage.getItem('cannedResponses');
    if (savedResponses) {
      setResponses(JSON.parse(savedResponses));
    } else {
      setResponses(defaultResponses);
      localStorage.setItem('cannedResponses', JSON.stringify(defaultResponses));
    }
  }, []);

  const filteredResponses = responses.filter(response => {
    const matchesSearch = response.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         response.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         response.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || response.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const handleCreateResponse = () => {
    if (!newResponse.title.trim() || !newResponse.content.trim()) return;

    const response = {
      id: Date.now(),
      ...newResponse,
      tags: newResponse.tags.filter(tag => tag.trim()),
      usageCount: 0,
      lastUsed: null,
      createdBy: user?.username || 'Agent',
      createdAt: new Date()
    };

    const updatedResponses = [...responses, response];
    setResponses(updatedResponses);
    localStorage.setItem('cannedResponses', JSON.stringify(updatedResponses));
    
    setNewResponse({
      title: '',
      content: '',
      category: 'general',
      tags: [],
      isPrivate: false
    });
    setIsCreating(false);
  };

  const handleEditResponse = (response) => {
    setEditingResponse(response);
    setNewResponse({
      title: response.title,
      content: response.content,
      category: response.category,
      tags: response.tags,
      isPrivate: response.isPrivate
    });
  };

  const handleUpdateResponse = () => {
    if (!newResponse.title.trim() || !newResponse.content.trim()) return;

    const updatedResponses = responses.map(response =>
      response.id === editingResponse.id
        ? { ...response, ...newResponse, tags: newResponse.tags.filter(tag => tag.trim()) }
        : response
    );

    setResponses(updatedResponses);
    localStorage.setItem('cannedResponses', JSON.stringify(updatedResponses));
    
    setEditingResponse(null);
    setNewResponse({
      title: '',
      content: '',
      category: 'general',
      tags: [],
      isPrivate: false
    });
  };

  const handleDeleteResponse = (responseId) => {
    if (window.confirm('Are you sure you want to delete this response?')) {
      const updatedResponses = responses.filter(response => response.id !== responseId);
      setResponses(updatedResponses);
      localStorage.setItem('cannedResponses', JSON.stringify(updatedResponses));
    }
  };

  const handleSelectResponse = (response) => {
    // Update usage statistics
    const updatedResponses = responses.map(r =>
      r.id === response.id
        ? { ...r, usageCount: r.usageCount + 1, lastUsed: new Date() }
        : r
    );
    setResponses(updatedResponses);
    localStorage.setItem('cannedResponses', JSON.stringify(updatedResponses));
    
    // Send response to chat
    onSelectResponse(response.content);
    onClose();
  };

  const addTag = (tag) => {
    if (tag.trim() && !newResponse.tags.includes(tag.trim())) {
      setNewResponse(prev => ({
        ...prev,
        tags: [...prev.tags, tag.trim()]
      }));
    }
  };

  const removeTag = (tagToRemove) => {
    setNewResponse(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl h-5/6 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            Canned Responses
          </h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setIsCreating(true)}
              className="px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm flex items-center space-x-1"
            >
              <Plus className="w-4 h-4" />
              <span>New Response</span>
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              ×
            </button>
          </div>
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* Sidebar */}
          <div className="w-64 border-r border-gray-200 dark:border-gray-700 p-4">
            {/* Search */}
            <div className="relative mb-4">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search responses..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
              />
            </div>

            {/* Categories */}
            <div className="space-y-1">
              {categories.map((category) => {
                const Icon = category.icon;
                const count = category.id === 'all' 
                  ? responses.length 
                  : responses.filter(r => r.category === category.id).length;
                
                return (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm transition-colors ${
                      selectedCategory === category.id
                        ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <Icon className="w-4 h-4" />
                      <span>{category.name}</span>
                    </div>
                    <span className="text-xs bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded-full">
                      {count}
                    </span>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            {(isCreating || editingResponse) ? (
              /* Create/Edit Form */
              <div className="p-4 space-y-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  {editingResponse ? 'Edit Response' : 'Create New Response'}
                </h3>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Title
                    </label>
                    <input
                      type="text"
                      value={newResponse.title}
                      onChange={(e) => setNewResponse(prev => ({ ...prev, title: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="Response title..."
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Category
                    </label>
                    <select
                      value={newResponse.category}
                      onChange={(e) => setNewResponse(prev => ({ ...prev, category: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      {categories.slice(1).map((category) => (
                        <option key={category.id} value={category.id}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Content
                  </label>
                  <textarea
                    value={newResponse.content}
                    onChange={(e) => setNewResponse(prev => ({ ...prev, content: e.target.value }))}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Response content..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Tags
                  </label>
                  <div className="flex flex-wrap gap-2 mb-2">
                    {newResponse.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 px-2 py-1 rounded-full text-xs flex items-center space-x-1"
                      >
                        <span>{tag}</span>
                        <button
                          onClick={() => removeTag(tag)}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                  <input
                    type="text"
                    placeholder="Add tags (press Enter)"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        addTag(e.target.value);
                        e.target.value = '';
                      }
                    }}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={newResponse.isPrivate}
                      onChange={(e) => setNewResponse(prev => ({ ...prev, isPrivate: e.target.checked }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700 dark:text-gray-300">Private (only visible to me)</span>
                  </label>

                  <div className="flex space-x-2">
                    <button
                      onClick={() => {
                        setIsCreating(false);
                        setEditingResponse(null);
                        setNewResponse({
                          title: '',
                          content: '',
                          category: 'general',
                          tags: [],
                          isPrivate: false
                        });
                      }}
                      className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={editingResponse ? handleUpdateResponse : handleCreateResponse}
                      className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg"
                    >
                      {editingResponse ? 'Update' : 'Create'}
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              /* Responses List */
              <div className="flex-1 overflow-y-auto p-4">
                <div className="space-y-3">
                  {filteredResponses.map((response) => (
                    <div
                      key={response.id}
                      className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h4 className="font-medium text-gray-900 dark:text-white">
                              {response.title}
                            </h4>
                            <span className="text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full">
                              {response.category}
                            </span>
                            {response.isPrivate && (
                              <span className="text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300 px-2 py-1 rounded-full">
                                Private
                              </span>
                            )}
                          </div>
                          
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2 line-clamp-2">
                            {response.content}
                          </p>
                          
                          <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                            <div className="flex items-center space-x-1">
                              <Clock className="w-3 h-3" />
                              <span>Used {response.usageCount} times</span>
                            </div>
                            {response.lastUsed && (
                              <span>Last used {response.lastUsed.toLocaleDateString()}</span>
                            )}
                            <span>by {response.createdBy}</span>
                          </div>
                          
                          {response.tags.length > 0 && (
                            <div className="flex flex-wrap gap-1 mt-2">
                              {response.tags.map((tag, index) => (
                                <span
                                  key={index}
                                  className="text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full"
                                >
                                  #{tag}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                        
                        <div className="flex items-center space-x-1 ml-4">
                          <button
                            onClick={() => handleSelectResponse(response)}
                            className="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm"
                          >
                            Use
                          </button>
                          <button
                            onClick={() => handleEditResponse(response)}
                            className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                          >
                            <Edit3 className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteResponse(response.id)}
                            className="p-1 text-gray-400 hover:text-red-500"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                
                {filteredResponses.length === 0 && (
                  <div className="text-center py-8">
                    <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                      No responses found
                    </h3>
                    <p className="text-gray-500 dark:text-gray-400">
                      {searchTerm ? 'Try adjusting your search terms.' : 'Create your first canned response to get started.'}
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CannedResponses;
