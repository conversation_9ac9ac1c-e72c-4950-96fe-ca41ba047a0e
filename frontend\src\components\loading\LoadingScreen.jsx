import React, { useState, useEffect } from 'react';
import { MessageCircle, Users, Crown, Radio, Zap, Heart } from 'lucide-react';

const LoadingScreen = ({ message = "Loading ChatFlow Pro..." }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);

  const loadingSteps = [
    { icon: MessageCircle, text: "Initializing chat engine...", color: "text-blue-500" },
    { icon: Users, text: "Connecting to community...", color: "text-green-500" },
    { icon: Crown, text: "Loading VIP features...", color: "text-purple-500" },
    { icon: Radio, text: "Setting up live events...", color: "text-red-500" },
    { icon: Zap, text: "Optimizing performance...", color: "text-yellow-500" },
    { icon: Heart, text: "Almost ready!", color: "text-pink-500" }
  ];

  useEffect(() => {
    const stepInterval = setInterval(() => {
      setCurrentStep((prev) => {
        if (prev < loadingSteps.length - 1) {
          return prev + 1;
        }
        return 0; // Loop back to start
      });
    }, 800);

    const progressInterval = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          return 0; // Reset progress
        }
        return prev + 2;
      });
    }, 50);

    return () => {
      clearInterval(stepInterval);
      clearInterval(progressInterval);
    };
  }, [loadingSteps.length]);

  const currentStepData = loadingSteps[currentStep];
  const StepIcon = currentStepData.icon;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-8">
      <div className="text-center space-y-8 max-w-md w-full">
        {/* Animated Logo */}
        <div className="relative">
          <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto shadow-lg animate-pulse">
            <MessageCircle className="w-10 h-10 text-white" />
          </div>
          
          {/* Floating Icons */}
          <div className="absolute inset-0 animate-spin" style={{ animationDuration: '8s' }}>
            {loadingSteps.map((step, index) => {
              const Icon = step.icon;
              const angle = (index * 360) / loadingSteps.length;
              const radius = 50;
              const x = Math.cos((angle * Math.PI) / 180) * radius;
              const y = Math.sin((angle * Math.PI) / 180) * radius;
              
              return (
                <div
                  key={index}
                  className={`absolute w-8 h-8 rounded-full bg-white dark:bg-gray-800 shadow-md flex items-center justify-center transition-all duration-500 ${
                    index === currentStep ? 'scale-125 shadow-lg' : 'scale-100'
                  }`}
                  style={{
                    transform: `translate(${x}px, ${y}px)`,
                    left: '50%',
                    top: '50%',
                    marginLeft: '-16px',
                    marginTop: '-16px'
                  }}
                >
                  <Icon className={`w-4 h-4 ${index === currentStep ? step.color : 'text-gray-400'}`} />
                </div>
              );
            })}
          </div>
        </div>

        {/* Brand Name */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              ChatFlow Pro
            </span>
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Professional Chat Platform
          </p>
        </div>

        {/* Loading Steps */}
        <div className="space-y-4">
          <div className="flex items-center justify-center space-x-3">
            <StepIcon className={`w-5 h-5 ${currentStepData.color} animate-bounce`} />
            <span className="text-gray-700 dark:text-gray-300 font-medium">
              {currentStepData.text}
            </span>
          </div>

          {/* Progress Bar */}
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden">
            <div 
              className="h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${progress}%` }}
            />
          </div>

          {/* Progress Percentage */}
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {Math.round(progress)}% Complete
          </div>
        </div>

        {/* Loading Dots */}
        <div className="flex justify-center space-x-2">
          {[0, 1, 2].map((index) => (
            <div
              key={index}
              className="w-3 h-3 bg-blue-500 rounded-full animate-bounce"
              style={{
                animationDelay: `${index * 0.2}s`,
                animationDuration: '1s'
              }}
            />
          ))}
        </div>

        {/* Loading Message */}
        <p className="text-gray-500 dark:text-gray-400 text-sm">
          {message}
        </p>

        {/* Feature Highlights */}
        <div className="grid grid-cols-3 gap-4 pt-4">
          {[
            { icon: MessageCircle, label: "Real-time Chat" },
            { icon: Users, label: "Team Collaboration" },
            { icon: Crown, label: "Premium Features" }
          ].map((feature, index) => {
            const FeatureIcon = feature.icon;
            return (
              <div
                key={index}
                className="text-center opacity-60 hover:opacity-100 transition-opacity duration-300"
              >
                <FeatureIcon className="w-6 h-6 text-gray-400 mx-auto mb-1" />
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {feature.label}
                </p>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;
