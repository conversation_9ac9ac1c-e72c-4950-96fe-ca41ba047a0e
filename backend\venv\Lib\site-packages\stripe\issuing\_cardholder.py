# -*- coding: utf-8 -*-
# File generated from our OpenAPI spec
from stripe._createable_api_resource import CreateableAPIResource
from stripe._expandable_field import Expanda<PERSON><PERSON><PERSON>
from stripe._list_object import ListObject
from stripe._listable_api_resource import ListableAPIResource
from stripe._request_options import RequestOptions
from stripe._stripe_object import StripeObject
from stripe._updateable_api_resource import UpdateableAPIResource
from typing import ClassVar, Dict, List, Optional, cast
from typing_extensions import (
    Literal,
    NotRequired,
    TypedDict,
    Unpack,
    TYPE_CHECKING,
)
from urllib.parse import quote_plus

if TYPE_CHECKING:
    from stripe._file import File


class Cardholder(
    CreateableAPIResource["Cardholder"],
    ListableAPIResource["Cardholder"],
    UpdateableAPIResource["Cardholder"],
):
    """
    An Issuing `Cardholder` object represents an individual or business entity who is [issued](https://stripe.com/docs/issuing) cards.

    Related guide: [How to create a cardholder](https://stripe.com/docs/issuing/cards#create-cardholder)
    """

    OBJECT_NAME: ClassVar[Literal["issuing.cardholder"]] = "issuing.cardholder"

    class Billing(StripeObject):
        class Address(StripeObject):
            city: Optional[str]
            """
            City, district, suburb, town, or village.
            """
            country: Optional[str]
            """
            Two-letter country code ([ISO 3166-1 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2)).
            """
            line1: Optional[str]
            """
            Address line 1 (e.g., street, PO Box, or company name).
            """
            line2: Optional[str]
            """
            Address line 2 (e.g., apartment, suite, unit, or building).
            """
            postal_code: Optional[str]
            """
            ZIP or postal code.
            """
            state: Optional[str]
            """
            State, county, province, or region.
            """

        address: Address
        _inner_class_types = {"address": Address}

    class Company(StripeObject):
        tax_id_provided: bool
        """
        Whether the company's business ID number was provided.
        """

    class Individual(StripeObject):
        class CardIssuing(StripeObject):
            class UserTermsAcceptance(StripeObject):
                date: Optional[int]
                """
                The Unix timestamp marking when the cardholder accepted the Authorized User Terms. Required for Celtic Spend Card users.
                """
                ip: Optional[str]
                """
                The IP address from which the cardholder accepted the Authorized User Terms. Required for Celtic Spend Card users.
                """
                user_agent: Optional[str]
                """
                The user agent of the browser from which the cardholder accepted the Authorized User Terms.
                """

            user_terms_acceptance: Optional[UserTermsAcceptance]
            """
            Information about cardholder acceptance of [Authorized User Terms](https://stripe.com/docs/issuing/cards).
            """
            _inner_class_types = {"user_terms_acceptance": UserTermsAcceptance}

        class Dob(StripeObject):
            day: Optional[int]
            """
            The day of birth, between 1 and 31.
            """
            month: Optional[int]
            """
            The month of birth, between 1 and 12.
            """
            year: Optional[int]
            """
            The four-digit year of birth.
            """

        class Verification(StripeObject):
            class Document(StripeObject):
                back: Optional[ExpandableField["File"]]
                """
                The back of a document returned by a [file upload](https://stripe.com/docs/api#create_file) with a `purpose` value of `identity_document`.
                """
                front: Optional[ExpandableField["File"]]
                """
                The front of a document returned by a [file upload](https://stripe.com/docs/api#create_file) with a `purpose` value of `identity_document`.
                """

            document: Optional[Document]
            """
            An identifying document, either a passport or local ID card.
            """
            _inner_class_types = {"document": Document}

        card_issuing: Optional[CardIssuing]
        """
        Information related to the card_issuing program for this cardholder.
        """
        dob: Optional[Dob]
        """
        The date of birth of this cardholder.
        """
        first_name: Optional[str]
        """
        The first name of this cardholder. Required before activating Cards. This field cannot contain any numbers, special characters (except periods, commas, hyphens, spaces and apostrophes) or non-latin letters.
        """
        last_name: Optional[str]
        """
        The last name of this cardholder. Required before activating Cards. This field cannot contain any numbers, special characters (except periods, commas, hyphens, spaces and apostrophes) or non-latin letters.
        """
        verification: Optional[Verification]
        """
        Government-issued ID document for this cardholder.
        """
        _inner_class_types = {
            "card_issuing": CardIssuing,
            "dob": Dob,
            "verification": Verification,
        }

    class Requirements(StripeObject):
        disabled_reason: Optional[
            Literal[
                "listed",
                "rejected.listed",
                "requirements.past_due",
                "under_review",
            ]
        ]
        """
        If `disabled_reason` is present, all cards will decline authorizations with `cardholder_verification_required` reason.
        """
        past_due: Optional[
            List[
                Literal[
                    "company.tax_id",
                    "individual.card_issuing.user_terms_acceptance.date",
                    "individual.card_issuing.user_terms_acceptance.ip",
                    "individual.dob.day",
                    "individual.dob.month",
                    "individual.dob.year",
                    "individual.first_name",
                    "individual.last_name",
                    "individual.verification.document",
                ]
            ]
        ]
        """
        Array of fields that need to be collected in order to verify and re-enable the cardholder.
        """

    class SpendingControls(StripeObject):
        class SpendingLimit(StripeObject):
            amount: int
            """
            Maximum amount allowed to spend per interval. This amount is in the card's currency and in the [smallest currency unit](https://stripe.com/docs/currencies#zero-decimal).
            """
            categories: Optional[
                List[
                    Literal[
                        "ac_refrigeration_repair",
                        "accounting_bookkeeping_services",
                        "advertising_services",
                        "agricultural_cooperative",
                        "airlines_air_carriers",
                        "airports_flying_fields",
                        "ambulance_services",
                        "amusement_parks_carnivals",
                        "antique_reproductions",
                        "antique_shops",
                        "aquariums",
                        "architectural_surveying_services",
                        "art_dealers_and_galleries",
                        "artists_supply_and_craft_shops",
                        "auto_and_home_supply_stores",
                        "auto_body_repair_shops",
                        "auto_paint_shops",
                        "auto_service_shops",
                        "automated_cash_disburse",
                        "automated_fuel_dispensers",
                        "automobile_associations",
                        "automotive_parts_and_accessories_stores",
                        "automotive_tire_stores",
                        "bail_and_bond_payments",
                        "bakeries",
                        "bands_orchestras",
                        "barber_and_beauty_shops",
                        "betting_casino_gambling",
                        "bicycle_shops",
                        "billiard_pool_establishments",
                        "boat_dealers",
                        "boat_rentals_and_leases",
                        "book_stores",
                        "books_periodicals_and_newspapers",
                        "bowling_alleys",
                        "bus_lines",
                        "business_secretarial_schools",
                        "buying_shopping_services",
                        "cable_satellite_and_other_pay_television_and_radio",
                        "camera_and_photographic_supply_stores",
                        "candy_nut_and_confectionery_stores",
                        "car_and_truck_dealers_new_used",
                        "car_and_truck_dealers_used_only",
                        "car_rental_agencies",
                        "car_washes",
                        "carpentry_services",
                        "carpet_upholstery_cleaning",
                        "caterers",
                        "charitable_and_social_service_organizations_fundraising",
                        "chemicals_and_allied_products",
                        "child_care_services",
                        "childrens_and_infants_wear_stores",
                        "chiropodists_podiatrists",
                        "chiropractors",
                        "cigar_stores_and_stands",
                        "civic_social_fraternal_associations",
                        "cleaning_and_maintenance",
                        "clothing_rental",
                        "colleges_universities",
                        "commercial_equipment",
                        "commercial_footwear",
                        "commercial_photography_art_and_graphics",
                        "commuter_transport_and_ferries",
                        "computer_network_services",
                        "computer_programming",
                        "computer_repair",
                        "computer_software_stores",
                        "computers_peripherals_and_software",
                        "concrete_work_services",
                        "construction_materials",
                        "consulting_public_relations",
                        "correspondence_schools",
                        "cosmetic_stores",
                        "counseling_services",
                        "country_clubs",
                        "courier_services",
                        "court_costs",
                        "credit_reporting_agencies",
                        "cruise_lines",
                        "dairy_products_stores",
                        "dance_hall_studios_schools",
                        "dating_escort_services",
                        "dentists_orthodontists",
                        "department_stores",
                        "detective_agencies",
                        "digital_goods_applications",
                        "digital_goods_games",
                        "digital_goods_large_volume",
                        "digital_goods_media",
                        "direct_marketing_catalog_merchant",
                        "direct_marketing_combination_catalog_and_retail_merchant",
                        "direct_marketing_inbound_telemarketing",
                        "direct_marketing_insurance_services",
                        "direct_marketing_other",
                        "direct_marketing_outbound_telemarketing",
                        "direct_marketing_subscription",
                        "direct_marketing_travel",
                        "discount_stores",
                        "doctors",
                        "door_to_door_sales",
                        "drapery_window_covering_and_upholstery_stores",
                        "drinking_places",
                        "drug_stores_and_pharmacies",
                        "drugs_drug_proprietaries_and_druggist_sundries",
                        "dry_cleaners",
                        "durable_goods",
                        "duty_free_stores",
                        "eating_places_restaurants",
                        "educational_services",
                        "electric_razor_stores",
                        "electric_vehicle_charging",
                        "electrical_parts_and_equipment",
                        "electrical_services",
                        "electronics_repair_shops",
                        "electronics_stores",
                        "elementary_secondary_schools",
                        "emergency_services_gcas_visa_use_only",
                        "employment_temp_agencies",
                        "equipment_rental",
                        "exterminating_services",
                        "family_clothing_stores",
                        "fast_food_restaurants",
                        "financial_institutions",
                        "fines_government_administrative_entities",
                        "fireplace_fireplace_screens_and_accessories_stores",
                        "floor_covering_stores",
                        "florists",
                        "florists_supplies_nursery_stock_and_flowers",
                        "freezer_and_locker_meat_provisioners",
                        "fuel_dealers_non_automotive",
                        "funeral_services_crematories",
                        "furniture_home_furnishings_and_equipment_stores_except_appliances",
                        "furniture_repair_refinishing",
                        "furriers_and_fur_shops",
                        "general_services",
                        "gift_card_novelty_and_souvenir_shops",
                        "glass_paint_and_wallpaper_stores",
                        "glassware_crystal_stores",
                        "golf_courses_public",
                        "government_licensed_horse_dog_racing_us_region_only",
                        "government_licensed_online_casions_online_gambling_us_region_only",
                        "government_owned_lotteries_non_us_region",
                        "government_owned_lotteries_us_region_only",
                        "government_services",
                        "grocery_stores_supermarkets",
                        "hardware_equipment_and_supplies",
                        "hardware_stores",
                        "health_and_beauty_spas",
                        "hearing_aids_sales_and_supplies",
                        "heating_plumbing_a_c",
                        "hobby_toy_and_game_shops",
                        "home_supply_warehouse_stores",
                        "hospitals",
                        "hotels_motels_and_resorts",
                        "household_appliance_stores",
                        "industrial_supplies",
                        "information_retrieval_services",
                        "insurance_default",
                        "insurance_underwriting_premiums",
                        "intra_company_purchases",
                        "jewelry_stores_watches_clocks_and_silverware_stores",
                        "landscaping_services",
                        "laundries",
                        "laundry_cleaning_services",
                        "legal_services_attorneys",
                        "luggage_and_leather_goods_stores",
                        "lumber_building_materials_stores",
                        "manual_cash_disburse",
                        "marinas_service_and_supplies",
                        "marketplaces",
                        "masonry_stonework_and_plaster",
                        "massage_parlors",
                        "medical_and_dental_labs",
                        "medical_dental_ophthalmic_and_hospital_equipment_and_supplies",
                        "medical_services",
                        "membership_organizations",
                        "mens_and_boys_clothing_and_accessories_stores",
                        "mens_womens_clothing_stores",
                        "metal_service_centers",
                        "miscellaneous",
                        "miscellaneous_apparel_and_accessory_shops",
                        "miscellaneous_auto_dealers",
                        "miscellaneous_business_services",
                        "miscellaneous_food_stores",
                        "miscellaneous_general_merchandise",
                        "miscellaneous_general_services",
                        "miscellaneous_home_furnishing_specialty_stores",
                        "miscellaneous_publishing_and_printing",
                        "miscellaneous_recreation_services",
                        "miscellaneous_repair_shops",
                        "miscellaneous_specialty_retail",
                        "mobile_home_dealers",
                        "motion_picture_theaters",
                        "motor_freight_carriers_and_trucking",
                        "motor_homes_dealers",
                        "motor_vehicle_supplies_and_new_parts",
                        "motorcycle_shops_and_dealers",
                        "motorcycle_shops_dealers",
                        "music_stores_musical_instruments_pianos_and_sheet_music",
                        "news_dealers_and_newsstands",
                        "non_fi_money_orders",
                        "non_fi_stored_value_card_purchase_load",
                        "nondurable_goods",
                        "nurseries_lawn_and_garden_supply_stores",
                        "nursing_personal_care",
                        "office_and_commercial_furniture",
                        "opticians_eyeglasses",
                        "optometrists_ophthalmologist",
                        "orthopedic_goods_prosthetic_devices",
                        "osteopaths",
                        "package_stores_beer_wine_and_liquor",
                        "paints_varnishes_and_supplies",
                        "parking_lots_garages",
                        "passenger_railways",
                        "pawn_shops",
                        "pet_shops_pet_food_and_supplies",
                        "petroleum_and_petroleum_products",
                        "photo_developing",
                        "photographic_photocopy_microfilm_equipment_and_supplies",
                        "photographic_studios",
                        "picture_video_production",
                        "piece_goods_notions_and_other_dry_goods",
                        "plumbing_heating_equipment_and_supplies",
                        "political_organizations",
                        "postal_services_government_only",
                        "precious_stones_and_metals_watches_and_jewelry",
                        "professional_services",
                        "public_warehousing_and_storage",
                        "quick_copy_repro_and_blueprint",
                        "railroads",
                        "real_estate_agents_and_managers_rentals",
                        "record_stores",
                        "recreational_vehicle_rentals",
                        "religious_goods_stores",
                        "religious_organizations",
                        "roofing_siding_sheet_metal",
                        "secretarial_support_services",
                        "security_brokers_dealers",
                        "service_stations",
                        "sewing_needlework_fabric_and_piece_goods_stores",
                        "shoe_repair_hat_cleaning",
                        "shoe_stores",
                        "small_appliance_repair",
                        "snowmobile_dealers",
                        "special_trade_services",
                        "specialty_cleaning",
                        "sporting_goods_stores",
                        "sporting_recreation_camps",
                        "sports_and_riding_apparel_stores",
                        "sports_clubs_fields",
                        "stamp_and_coin_stores",
                        "stationary_office_supplies_printing_and_writing_paper",
                        "stationery_stores_office_and_school_supply_stores",
                        "swimming_pools_sales",
                        "t_ui_travel_germany",
                        "tailors_alterations",
                        "tax_payments_government_agencies",
                        "tax_preparation_services",
                        "taxicabs_limousines",
                        "telecommunication_equipment_and_telephone_sales",
                        "telecommunication_services",
                        "telegraph_services",
                        "tent_and_awning_shops",
                        "testing_laboratories",
                        "theatrical_ticket_agencies",
                        "timeshares",
                        "tire_retreading_and_repair",
                        "tolls_bridge_fees",
                        "tourist_attractions_and_exhibits",
                        "towing_services",
                        "trailer_parks_campgrounds",
                        "transportation_services",
                        "travel_agencies_tour_operators",
                        "truck_stop_iteration",
                        "truck_utility_trailer_rentals",
                        "typesetting_plate_making_and_related_services",
                        "typewriter_stores",
                        "u_s_federal_government_agencies_or_departments",
                        "uniforms_commercial_clothing",
                        "used_merchandise_and_secondhand_stores",
                        "utilities",
                        "variety_stores",
                        "veterinary_services",
                        "video_amusement_game_supplies",
                        "video_game_arcades",
                        "video_tape_rental_stores",
                        "vocational_trade_schools",
                        "watch_jewelry_repair",
                        "welding_repair",
                        "wholesale_clubs",
                        "wig_and_toupee_stores",
                        "wires_money_orders",
                        "womens_accessory_and_specialty_shops",
                        "womens_ready_to_wear_stores",
                        "wrecking_and_salvage_yards",
                    ]
                ]
            ]
            """
            Array of strings containing [categories](https://stripe.com/docs/api#issuing_authorization_object-merchant_data-category) this limit applies to. Omitting this field will apply the limit to all categories.
            """
            interval: Literal[
                "all_time",
                "daily",
                "monthly",
                "per_authorization",
                "weekly",
                "yearly",
            ]
            """
            Interval (or event) to which the amount applies.
            """

        allowed_categories: Optional[
            List[
                Literal[
                    "ac_refrigeration_repair",
                    "accounting_bookkeeping_services",
                    "advertising_services",
                    "agricultural_cooperative",
                    "airlines_air_carriers",
                    "airports_flying_fields",
                    "ambulance_services",
                    "amusement_parks_carnivals",
                    "antique_reproductions",
                    "antique_shops",
                    "aquariums",
                    "architectural_surveying_services",
                    "art_dealers_and_galleries",
                    "artists_supply_and_craft_shops",
                    "auto_and_home_supply_stores",
                    "auto_body_repair_shops",
                    "auto_paint_shops",
                    "auto_service_shops",
                    "automated_cash_disburse",
                    "automated_fuel_dispensers",
                    "automobile_associations",
                    "automotive_parts_and_accessories_stores",
                    "automotive_tire_stores",
                    "bail_and_bond_payments",
                    "bakeries",
                    "bands_orchestras",
                    "barber_and_beauty_shops",
                    "betting_casino_gambling",
                    "bicycle_shops",
                    "billiard_pool_establishments",
                    "boat_dealers",
                    "boat_rentals_and_leases",
                    "book_stores",
                    "books_periodicals_and_newspapers",
                    "bowling_alleys",
                    "bus_lines",
                    "business_secretarial_schools",
                    "buying_shopping_services",
                    "cable_satellite_and_other_pay_television_and_radio",
                    "camera_and_photographic_supply_stores",
                    "candy_nut_and_confectionery_stores",
                    "car_and_truck_dealers_new_used",
                    "car_and_truck_dealers_used_only",
                    "car_rental_agencies",
                    "car_washes",
                    "carpentry_services",
                    "carpet_upholstery_cleaning",
                    "caterers",
                    "charitable_and_social_service_organizations_fundraising",
                    "chemicals_and_allied_products",
                    "child_care_services",
                    "childrens_and_infants_wear_stores",
                    "chiropodists_podiatrists",
                    "chiropractors",
                    "cigar_stores_and_stands",
                    "civic_social_fraternal_associations",
                    "cleaning_and_maintenance",
                    "clothing_rental",
                    "colleges_universities",
                    "commercial_equipment",
                    "commercial_footwear",
                    "commercial_photography_art_and_graphics",
                    "commuter_transport_and_ferries",
                    "computer_network_services",
                    "computer_programming",
                    "computer_repair",
                    "computer_software_stores",
                    "computers_peripherals_and_software",
                    "concrete_work_services",
                    "construction_materials",
                    "consulting_public_relations",
                    "correspondence_schools",
                    "cosmetic_stores",
                    "counseling_services",
                    "country_clubs",
                    "courier_services",
                    "court_costs",
                    "credit_reporting_agencies",
                    "cruise_lines",
                    "dairy_products_stores",
                    "dance_hall_studios_schools",
                    "dating_escort_services",
                    "dentists_orthodontists",
                    "department_stores",
                    "detective_agencies",
                    "digital_goods_applications",
                    "digital_goods_games",
                    "digital_goods_large_volume",
                    "digital_goods_media",
                    "direct_marketing_catalog_merchant",
                    "direct_marketing_combination_catalog_and_retail_merchant",
                    "direct_marketing_inbound_telemarketing",
                    "direct_marketing_insurance_services",
                    "direct_marketing_other",
                    "direct_marketing_outbound_telemarketing",
                    "direct_marketing_subscription",
                    "direct_marketing_travel",
                    "discount_stores",
                    "doctors",
                    "door_to_door_sales",
                    "drapery_window_covering_and_upholstery_stores",
                    "drinking_places",
                    "drug_stores_and_pharmacies",
                    "drugs_drug_proprietaries_and_druggist_sundries",
                    "dry_cleaners",
                    "durable_goods",
                    "duty_free_stores",
                    "eating_places_restaurants",
                    "educational_services",
                    "electric_razor_stores",
                    "electric_vehicle_charging",
                    "electrical_parts_and_equipment",
                    "electrical_services",
                    "electronics_repair_shops",
                    "electronics_stores",
                    "elementary_secondary_schools",
                    "emergency_services_gcas_visa_use_only",
                    "employment_temp_agencies",
                    "equipment_rental",
                    "exterminating_services",
                    "family_clothing_stores",
                    "fast_food_restaurants",
                    "financial_institutions",
                    "fines_government_administrative_entities",
                    "fireplace_fireplace_screens_and_accessories_stores",
                    "floor_covering_stores",
                    "florists",
                    "florists_supplies_nursery_stock_and_flowers",
                    "freezer_and_locker_meat_provisioners",
                    "fuel_dealers_non_automotive",
                    "funeral_services_crematories",
                    "furniture_home_furnishings_and_equipment_stores_except_appliances",
                    "furniture_repair_refinishing",
                    "furriers_and_fur_shops",
                    "general_services",
                    "gift_card_novelty_and_souvenir_shops",
                    "glass_paint_and_wallpaper_stores",
                    "glassware_crystal_stores",
                    "golf_courses_public",
                    "government_licensed_horse_dog_racing_us_region_only",
                    "government_licensed_online_casions_online_gambling_us_region_only",
                    "government_owned_lotteries_non_us_region",
                    "government_owned_lotteries_us_region_only",
                    "government_services",
                    "grocery_stores_supermarkets",
                    "hardware_equipment_and_supplies",
                    "hardware_stores",
                    "health_and_beauty_spas",
                    "hearing_aids_sales_and_supplies",
                    "heating_plumbing_a_c",
                    "hobby_toy_and_game_shops",
                    "home_supply_warehouse_stores",
                    "hospitals",
                    "hotels_motels_and_resorts",
                    "household_appliance_stores",
                    "industrial_supplies",
                    "information_retrieval_services",
                    "insurance_default",
                    "insurance_underwriting_premiums",
                    "intra_company_purchases",
                    "jewelry_stores_watches_clocks_and_silverware_stores",
                    "landscaping_services",
                    "laundries",
                    "laundry_cleaning_services",
                    "legal_services_attorneys",
                    "luggage_and_leather_goods_stores",
                    "lumber_building_materials_stores",
                    "manual_cash_disburse",
                    "marinas_service_and_supplies",
                    "marketplaces",
                    "masonry_stonework_and_plaster",
                    "massage_parlors",
                    "medical_and_dental_labs",
                    "medical_dental_ophthalmic_and_hospital_equipment_and_supplies",
                    "medical_services",
                    "membership_organizations",
                    "mens_and_boys_clothing_and_accessories_stores",
                    "mens_womens_clothing_stores",
                    "metal_service_centers",
                    "miscellaneous",
                    "miscellaneous_apparel_and_accessory_shops",
                    "miscellaneous_auto_dealers",
                    "miscellaneous_business_services",
                    "miscellaneous_food_stores",
                    "miscellaneous_general_merchandise",
                    "miscellaneous_general_services",
                    "miscellaneous_home_furnishing_specialty_stores",
                    "miscellaneous_publishing_and_printing",
                    "miscellaneous_recreation_services",
                    "miscellaneous_repair_shops",
                    "miscellaneous_specialty_retail",
                    "mobile_home_dealers",
                    "motion_picture_theaters",
                    "motor_freight_carriers_and_trucking",
                    "motor_homes_dealers",
                    "motor_vehicle_supplies_and_new_parts",
                    "motorcycle_shops_and_dealers",
                    "motorcycle_shops_dealers",
                    "music_stores_musical_instruments_pianos_and_sheet_music",
                    "news_dealers_and_newsstands",
                    "non_fi_money_orders",
                    "non_fi_stored_value_card_purchase_load",
                    "nondurable_goods",
                    "nurseries_lawn_and_garden_supply_stores",
                    "nursing_personal_care",
                    "office_and_commercial_furniture",
                    "opticians_eyeglasses",
                    "optometrists_ophthalmologist",
                    "orthopedic_goods_prosthetic_devices",
                    "osteopaths",
                    "package_stores_beer_wine_and_liquor",
                    "paints_varnishes_and_supplies",
                    "parking_lots_garages",
                    "passenger_railways",
                    "pawn_shops",
                    "pet_shops_pet_food_and_supplies",
                    "petroleum_and_petroleum_products",
                    "photo_developing",
                    "photographic_photocopy_microfilm_equipment_and_supplies",
                    "photographic_studios",
                    "picture_video_production",
                    "piece_goods_notions_and_other_dry_goods",
                    "plumbing_heating_equipment_and_supplies",
                    "political_organizations",
                    "postal_services_government_only",
                    "precious_stones_and_metals_watches_and_jewelry",
                    "professional_services",
                    "public_warehousing_and_storage",
                    "quick_copy_repro_and_blueprint",
                    "railroads",
                    "real_estate_agents_and_managers_rentals",
                    "record_stores",
                    "recreational_vehicle_rentals",
                    "religious_goods_stores",
                    "religious_organizations",
                    "roofing_siding_sheet_metal",
                    "secretarial_support_services",
                    "security_brokers_dealers",
                    "service_stations",
                    "sewing_needlework_fabric_and_piece_goods_stores",
                    "shoe_repair_hat_cleaning",
                    "shoe_stores",
                    "small_appliance_repair",
                    "snowmobile_dealers",
                    "special_trade_services",
                    "specialty_cleaning",
                    "sporting_goods_stores",
                    "sporting_recreation_camps",
                    "sports_and_riding_apparel_stores",
                    "sports_clubs_fields",
                    "stamp_and_coin_stores",
                    "stationary_office_supplies_printing_and_writing_paper",
                    "stationery_stores_office_and_school_supply_stores",
                    "swimming_pools_sales",
                    "t_ui_travel_germany",
                    "tailors_alterations",
                    "tax_payments_government_agencies",
                    "tax_preparation_services",
                    "taxicabs_limousines",
                    "telecommunication_equipment_and_telephone_sales",
                    "telecommunication_services",
                    "telegraph_services",
                    "tent_and_awning_shops",
                    "testing_laboratories",
                    "theatrical_ticket_agencies",
                    "timeshares",
                    "tire_retreading_and_repair",
                    "tolls_bridge_fees",
                    "tourist_attractions_and_exhibits",
                    "towing_services",
                    "trailer_parks_campgrounds",
                    "transportation_services",
                    "travel_agencies_tour_operators",
                    "truck_stop_iteration",
                    "truck_utility_trailer_rentals",
                    "typesetting_plate_making_and_related_services",
                    "typewriter_stores",
                    "u_s_federal_government_agencies_or_departments",
                    "uniforms_commercial_clothing",
                    "used_merchandise_and_secondhand_stores",
                    "utilities",
                    "variety_stores",
                    "veterinary_services",
                    "video_amusement_game_supplies",
                    "video_game_arcades",
                    "video_tape_rental_stores",
                    "vocational_trade_schools",
                    "watch_jewelry_repair",
                    "welding_repair",
                    "wholesale_clubs",
                    "wig_and_toupee_stores",
                    "wires_money_orders",
                    "womens_accessory_and_specialty_shops",
                    "womens_ready_to_wear_stores",
                    "wrecking_and_salvage_yards",
                ]
            ]
        ]
        """
        Array of strings containing [categories](https://stripe.com/docs/api#issuing_authorization_object-merchant_data-category) of authorizations to allow. All other categories will be blocked. Cannot be set with `blocked_categories`.
        """
        blocked_categories: Optional[
            List[
                Literal[
                    "ac_refrigeration_repair",
                    "accounting_bookkeeping_services",
                    "advertising_services",
                    "agricultural_cooperative",
                    "airlines_air_carriers",
                    "airports_flying_fields",
                    "ambulance_services",
                    "amusement_parks_carnivals",
                    "antique_reproductions",
                    "antique_shops",
                    "aquariums",
                    "architectural_surveying_services",
                    "art_dealers_and_galleries",
                    "artists_supply_and_craft_shops",
                    "auto_and_home_supply_stores",
                    "auto_body_repair_shops",
                    "auto_paint_shops",
                    "auto_service_shops",
                    "automated_cash_disburse",
                    "automated_fuel_dispensers",
                    "automobile_associations",
                    "automotive_parts_and_accessories_stores",
                    "automotive_tire_stores",
                    "bail_and_bond_payments",
                    "bakeries",
                    "bands_orchestras",
                    "barber_and_beauty_shops",
                    "betting_casino_gambling",
                    "bicycle_shops",
                    "billiard_pool_establishments",
                    "boat_dealers",
                    "boat_rentals_and_leases",
                    "book_stores",
                    "books_periodicals_and_newspapers",
                    "bowling_alleys",
                    "bus_lines",
                    "business_secretarial_schools",
                    "buying_shopping_services",
                    "cable_satellite_and_other_pay_television_and_radio",
                    "camera_and_photographic_supply_stores",
                    "candy_nut_and_confectionery_stores",
                    "car_and_truck_dealers_new_used",
                    "car_and_truck_dealers_used_only",
                    "car_rental_agencies",
                    "car_washes",
                    "carpentry_services",
                    "carpet_upholstery_cleaning",
                    "caterers",
                    "charitable_and_social_service_organizations_fundraising",
                    "chemicals_and_allied_products",
                    "child_care_services",
                    "childrens_and_infants_wear_stores",
                    "chiropodists_podiatrists",
                    "chiropractors",
                    "cigar_stores_and_stands",
                    "civic_social_fraternal_associations",
                    "cleaning_and_maintenance",
                    "clothing_rental",
                    "colleges_universities",
                    "commercial_equipment",
                    "commercial_footwear",
                    "commercial_photography_art_and_graphics",
                    "commuter_transport_and_ferries",
                    "computer_network_services",
                    "computer_programming",
                    "computer_repair",
                    "computer_software_stores",
                    "computers_peripherals_and_software",
                    "concrete_work_services",
                    "construction_materials",
                    "consulting_public_relations",
                    "correspondence_schools",
                    "cosmetic_stores",
                    "counseling_services",
                    "country_clubs",
                    "courier_services",
                    "court_costs",
                    "credit_reporting_agencies",
                    "cruise_lines",
                    "dairy_products_stores",
                    "dance_hall_studios_schools",
                    "dating_escort_services",
                    "dentists_orthodontists",
                    "department_stores",
                    "detective_agencies",
                    "digital_goods_applications",
                    "digital_goods_games",
                    "digital_goods_large_volume",
                    "digital_goods_media",
                    "direct_marketing_catalog_merchant",
                    "direct_marketing_combination_catalog_and_retail_merchant",
                    "direct_marketing_inbound_telemarketing",
                    "direct_marketing_insurance_services",
                    "direct_marketing_other",
                    "direct_marketing_outbound_telemarketing",
                    "direct_marketing_subscription",
                    "direct_marketing_travel",
                    "discount_stores",
                    "doctors",
                    "door_to_door_sales",
                    "drapery_window_covering_and_upholstery_stores",
                    "drinking_places",
                    "drug_stores_and_pharmacies",
                    "drugs_drug_proprietaries_and_druggist_sundries",
                    "dry_cleaners",
                    "durable_goods",
                    "duty_free_stores",
                    "eating_places_restaurants",
                    "educational_services",
                    "electric_razor_stores",
                    "electric_vehicle_charging",
                    "electrical_parts_and_equipment",
                    "electrical_services",
                    "electronics_repair_shops",
                    "electronics_stores",
                    "elementary_secondary_schools",
                    "emergency_services_gcas_visa_use_only",
                    "employment_temp_agencies",
                    "equipment_rental",
                    "exterminating_services",
                    "family_clothing_stores",
                    "fast_food_restaurants",
                    "financial_institutions",
                    "fines_government_administrative_entities",
                    "fireplace_fireplace_screens_and_accessories_stores",
                    "floor_covering_stores",
                    "florists",
                    "florists_supplies_nursery_stock_and_flowers",
                    "freezer_and_locker_meat_provisioners",
                    "fuel_dealers_non_automotive",
                    "funeral_services_crematories",
                    "furniture_home_furnishings_and_equipment_stores_except_appliances",
                    "furniture_repair_refinishing",
                    "furriers_and_fur_shops",
                    "general_services",
                    "gift_card_novelty_and_souvenir_shops",
                    "glass_paint_and_wallpaper_stores",
                    "glassware_crystal_stores",
                    "golf_courses_public",
                    "government_licensed_horse_dog_racing_us_region_only",
                    "government_licensed_online_casions_online_gambling_us_region_only",
                    "government_owned_lotteries_non_us_region",
                    "government_owned_lotteries_us_region_only",
                    "government_services",
                    "grocery_stores_supermarkets",
                    "hardware_equipment_and_supplies",
                    "hardware_stores",
                    "health_and_beauty_spas",
                    "hearing_aids_sales_and_supplies",
                    "heating_plumbing_a_c",
                    "hobby_toy_and_game_shops",
                    "home_supply_warehouse_stores",
                    "hospitals",
                    "hotels_motels_and_resorts",
                    "household_appliance_stores",
                    "industrial_supplies",
                    "information_retrieval_services",
                    "insurance_default",
                    "insurance_underwriting_premiums",
                    "intra_company_purchases",
                    "jewelry_stores_watches_clocks_and_silverware_stores",
                    "landscaping_services",
                    "laundries",
                    "laundry_cleaning_services",
                    "legal_services_attorneys",
                    "luggage_and_leather_goods_stores",
                    "lumber_building_materials_stores",
                    "manual_cash_disburse",
                    "marinas_service_and_supplies",
                    "marketplaces",
                    "masonry_stonework_and_plaster",
                    "massage_parlors",
                    "medical_and_dental_labs",
                    "medical_dental_ophthalmic_and_hospital_equipment_and_supplies",
                    "medical_services",
                    "membership_organizations",
                    "mens_and_boys_clothing_and_accessories_stores",
                    "mens_womens_clothing_stores",
                    "metal_service_centers",
                    "miscellaneous",
                    "miscellaneous_apparel_and_accessory_shops",
                    "miscellaneous_auto_dealers",
                    "miscellaneous_business_services",
                    "miscellaneous_food_stores",
                    "miscellaneous_general_merchandise",
                    "miscellaneous_general_services",
                    "miscellaneous_home_furnishing_specialty_stores",
                    "miscellaneous_publishing_and_printing",
                    "miscellaneous_recreation_services",
                    "miscellaneous_repair_shops",
                    "miscellaneous_specialty_retail",
                    "mobile_home_dealers",
                    "motion_picture_theaters",
                    "motor_freight_carriers_and_trucking",
                    "motor_homes_dealers",
                    "motor_vehicle_supplies_and_new_parts",
                    "motorcycle_shops_and_dealers",
                    "motorcycle_shops_dealers",
                    "music_stores_musical_instruments_pianos_and_sheet_music",
                    "news_dealers_and_newsstands",
                    "non_fi_money_orders",
                    "non_fi_stored_value_card_purchase_load",
                    "nondurable_goods",
                    "nurseries_lawn_and_garden_supply_stores",
                    "nursing_personal_care",
                    "office_and_commercial_furniture",
                    "opticians_eyeglasses",
                    "optometrists_ophthalmologist",
                    "orthopedic_goods_prosthetic_devices",
                    "osteopaths",
                    "package_stores_beer_wine_and_liquor",
                    "paints_varnishes_and_supplies",
                    "parking_lots_garages",
                    "passenger_railways",
                    "pawn_shops",
                    "pet_shops_pet_food_and_supplies",
                    "petroleum_and_petroleum_products",
                    "photo_developing",
                    "photographic_photocopy_microfilm_equipment_and_supplies",
                    "photographic_studios",
                    "picture_video_production",
                    "piece_goods_notions_and_other_dry_goods",
                    "plumbing_heating_equipment_and_supplies",
                    "political_organizations",
                    "postal_services_government_only",
                    "precious_stones_and_metals_watches_and_jewelry",
                    "professional_services",
                    "public_warehousing_and_storage",
                    "quick_copy_repro_and_blueprint",
                    "railroads",
                    "real_estate_agents_and_managers_rentals",
                    "record_stores",
                    "recreational_vehicle_rentals",
                    "religious_goods_stores",
                    "religious_organizations",
                    "roofing_siding_sheet_metal",
                    "secretarial_support_services",
                    "security_brokers_dealers",
                    "service_stations",
                    "sewing_needlework_fabric_and_piece_goods_stores",
                    "shoe_repair_hat_cleaning",
                    "shoe_stores",
                    "small_appliance_repair",
                    "snowmobile_dealers",
                    "special_trade_services",
                    "specialty_cleaning",
                    "sporting_goods_stores",
                    "sporting_recreation_camps",
                    "sports_and_riding_apparel_stores",
                    "sports_clubs_fields",
                    "stamp_and_coin_stores",
                    "stationary_office_supplies_printing_and_writing_paper",
                    "stationery_stores_office_and_school_supply_stores",
                    "swimming_pools_sales",
                    "t_ui_travel_germany",
                    "tailors_alterations",
                    "tax_payments_government_agencies",
                    "tax_preparation_services",
                    "taxicabs_limousines",
                    "telecommunication_equipment_and_telephone_sales",
                    "telecommunication_services",
                    "telegraph_services",
                    "tent_and_awning_shops",
                    "testing_laboratories",
                    "theatrical_ticket_agencies",
                    "timeshares",
                    "tire_retreading_and_repair",
                    "tolls_bridge_fees",
                    "tourist_attractions_and_exhibits",
                    "towing_services",
                    "trailer_parks_campgrounds",
                    "transportation_services",
                    "travel_agencies_tour_operators",
                    "truck_stop_iteration",
                    "truck_utility_trailer_rentals",
                    "typesetting_plate_making_and_related_services",
                    "typewriter_stores",
                    "u_s_federal_government_agencies_or_departments",
                    "uniforms_commercial_clothing",
                    "used_merchandise_and_secondhand_stores",
                    "utilities",
                    "variety_stores",
                    "veterinary_services",
                    "video_amusement_game_supplies",
                    "video_game_arcades",
                    "video_tape_rental_stores",
                    "vocational_trade_schools",
                    "watch_jewelry_repair",
                    "welding_repair",
                    "wholesale_clubs",
                    "wig_and_toupee_stores",
                    "wires_money_orders",
                    "womens_accessory_and_specialty_shops",
                    "womens_ready_to_wear_stores",
                    "wrecking_and_salvage_yards",
                ]
            ]
        ]
        """
        Array of strings containing [categories](https://stripe.com/docs/api#issuing_authorization_object-merchant_data-category) of authorizations to decline. All other categories will be allowed. Cannot be set with `allowed_categories`.
        """
        spending_limits: Optional[List[SpendingLimit]]
        """
        Limit spending with amount-based rules that apply across this cardholder's cards.
        """
        spending_limits_currency: Optional[str]
        """
        Currency of the amounts within `spending_limits`.
        """
        _inner_class_types = {"spending_limits": SpendingLimit}

    class CreateParams(RequestOptions):
        billing: "Cardholder.CreateParamsBilling"
        """
        The cardholder's billing address.
        """
        company: NotRequired["Cardholder.CreateParamsCompany"]
        """
        Additional information about a `company` cardholder.
        """
        email: NotRequired["str"]
        """
        The cardholder's email address.
        """
        expand: NotRequired["List[str]"]
        """
        Specifies which fields in the response should be expanded.
        """
        individual: NotRequired["Cardholder.CreateParamsIndividual"]
        """
        Additional information about an `individual` cardholder.
        """
        metadata: NotRequired["Dict[str, str]"]
        """
        Set of [key-value pairs](https://stripe.com/docs/api/metadata) that you can attach to an object. This can be useful for storing additional information about the object in a structured format. Individual keys can be unset by posting an empty value to them. All keys can be unset by posting an empty value to `metadata`.
        """
        name: str
        """
        The cardholder's name. This will be printed on cards issued to them. The maximum length of this field is 24 characters. This field cannot contain any special characters or numbers.
        """
        phone_number: NotRequired["str"]
        """
        The cardholder's phone number. This will be transformed to [E.164](https://en.wikipedia.org/wiki/E.164) if it is not provided in that format already. This is required for all cardholders who will be creating EU cards. See the [3D Secure documentation](https://stripe.com/docs/issuing/3d-secure#when-is-3d-secure-applied) for more details.
        """
        preferred_locales: NotRequired[
            "List[Literal['de', 'en', 'es', 'fr', 'it']]"
        ]
        """
        The cardholder's preferred locales (languages), ordered by preference. Locales can be `de`, `en`, `es`, `fr`, or `it`.
         This changes the language of the [3D Secure flow](https://stripe.com/docs/issuing/3d-secure) and one-time password messages sent to the cardholder.
        """
        spending_controls: NotRequired[
            "Cardholder.CreateParamsSpendingControls"
        ]
        """
        Rules that control spending across this cardholder's cards. Refer to our [documentation](https://stripe.com/docs/issuing/controls/spending-controls) for more details.
        """
        status: NotRequired["Literal['active', 'inactive']"]
        """
        Specifies whether to permit authorizations on this cardholder's cards. Defaults to `active`.
        """
        type: NotRequired["Literal['company', 'individual']"]
        """
        One of `individual` or `company`. See [Choose a cardholder type](https://stripe.com/docs/issuing/other/choose-cardholder) for more details.
        """

    class CreateParamsSpendingControls(TypedDict):
        allowed_categories: NotRequired[
            "List[Literal['ac_refrigeration_repair', 'accounting_bookkeeping_services', 'advertising_services', 'agricultural_cooperative', 'airlines_air_carriers', 'airports_flying_fields', 'ambulance_services', 'amusement_parks_carnivals', 'antique_reproductions', 'antique_shops', 'aquariums', 'architectural_surveying_services', 'art_dealers_and_galleries', 'artists_supply_and_craft_shops', 'auto_and_home_supply_stores', 'auto_body_repair_shops', 'auto_paint_shops', 'auto_service_shops', 'automated_cash_disburse', 'automated_fuel_dispensers', 'automobile_associations', 'automotive_parts_and_accessories_stores', 'automotive_tire_stores', 'bail_and_bond_payments', 'bakeries', 'bands_orchestras', 'barber_and_beauty_shops', 'betting_casino_gambling', 'bicycle_shops', 'billiard_pool_establishments', 'boat_dealers', 'boat_rentals_and_leases', 'book_stores', 'books_periodicals_and_newspapers', 'bowling_alleys', 'bus_lines', 'business_secretarial_schools', 'buying_shopping_services', 'cable_satellite_and_other_pay_television_and_radio', 'camera_and_photographic_supply_stores', 'candy_nut_and_confectionery_stores', 'car_and_truck_dealers_new_used', 'car_and_truck_dealers_used_only', 'car_rental_agencies', 'car_washes', 'carpentry_services', 'carpet_upholstery_cleaning', 'caterers', 'charitable_and_social_service_organizations_fundraising', 'chemicals_and_allied_products', 'child_care_services', 'childrens_and_infants_wear_stores', 'chiropodists_podiatrists', 'chiropractors', 'cigar_stores_and_stands', 'civic_social_fraternal_associations', 'cleaning_and_maintenance', 'clothing_rental', 'colleges_universities', 'commercial_equipment', 'commercial_footwear', 'commercial_photography_art_and_graphics', 'commuter_transport_and_ferries', 'computer_network_services', 'computer_programming', 'computer_repair', 'computer_software_stores', 'computers_peripherals_and_software', 'concrete_work_services', 'construction_materials', 'consulting_public_relations', 'correspondence_schools', 'cosmetic_stores', 'counseling_services', 'country_clubs', 'courier_services', 'court_costs', 'credit_reporting_agencies', 'cruise_lines', 'dairy_products_stores', 'dance_hall_studios_schools', 'dating_escort_services', 'dentists_orthodontists', 'department_stores', 'detective_agencies', 'digital_goods_applications', 'digital_goods_games', 'digital_goods_large_volume', 'digital_goods_media', 'direct_marketing_catalog_merchant', 'direct_marketing_combination_catalog_and_retail_merchant', 'direct_marketing_inbound_telemarketing', 'direct_marketing_insurance_services', 'direct_marketing_other', 'direct_marketing_outbound_telemarketing', 'direct_marketing_subscription', 'direct_marketing_travel', 'discount_stores', 'doctors', 'door_to_door_sales', 'drapery_window_covering_and_upholstery_stores', 'drinking_places', 'drug_stores_and_pharmacies', 'drugs_drug_proprietaries_and_druggist_sundries', 'dry_cleaners', 'durable_goods', 'duty_free_stores', 'eating_places_restaurants', 'educational_services', 'electric_razor_stores', 'electric_vehicle_charging', 'electrical_parts_and_equipment', 'electrical_services', 'electronics_repair_shops', 'electronics_stores', 'elementary_secondary_schools', 'emergency_services_gcas_visa_use_only', 'employment_temp_agencies', 'equipment_rental', 'exterminating_services', 'family_clothing_stores', 'fast_food_restaurants', 'financial_institutions', 'fines_government_administrative_entities', 'fireplace_fireplace_screens_and_accessories_stores', 'floor_covering_stores', 'florists', 'florists_supplies_nursery_stock_and_flowers', 'freezer_and_locker_meat_provisioners', 'fuel_dealers_non_automotive', 'funeral_services_crematories', 'furniture_home_furnishings_and_equipment_stores_except_appliances', 'furniture_repair_refinishing', 'furriers_and_fur_shops', 'general_services', 'gift_card_novelty_and_souvenir_shops', 'glass_paint_and_wallpaper_stores', 'glassware_crystal_stores', 'golf_courses_public', 'government_licensed_horse_dog_racing_us_region_only', 'government_licensed_online_casions_online_gambling_us_region_only', 'government_owned_lotteries_non_us_region', 'government_owned_lotteries_us_region_only', 'government_services', 'grocery_stores_supermarkets', 'hardware_equipment_and_supplies', 'hardware_stores', 'health_and_beauty_spas', 'hearing_aids_sales_and_supplies', 'heating_plumbing_a_c', 'hobby_toy_and_game_shops', 'home_supply_warehouse_stores', 'hospitals', 'hotels_motels_and_resorts', 'household_appliance_stores', 'industrial_supplies', 'information_retrieval_services', 'insurance_default', 'insurance_underwriting_premiums', 'intra_company_purchases', 'jewelry_stores_watches_clocks_and_silverware_stores', 'landscaping_services', 'laundries', 'laundry_cleaning_services', 'legal_services_attorneys', 'luggage_and_leather_goods_stores', 'lumber_building_materials_stores', 'manual_cash_disburse', 'marinas_service_and_supplies', 'marketplaces', 'masonry_stonework_and_plaster', 'massage_parlors', 'medical_and_dental_labs', 'medical_dental_ophthalmic_and_hospital_equipment_and_supplies', 'medical_services', 'membership_organizations', 'mens_and_boys_clothing_and_accessories_stores', 'mens_womens_clothing_stores', 'metal_service_centers', 'miscellaneous', 'miscellaneous_apparel_and_accessory_shops', 'miscellaneous_auto_dealers', 'miscellaneous_business_services', 'miscellaneous_food_stores', 'miscellaneous_general_merchandise', 'miscellaneous_general_services', 'miscellaneous_home_furnishing_specialty_stores', 'miscellaneous_publishing_and_printing', 'miscellaneous_recreation_services', 'miscellaneous_repair_shops', 'miscellaneous_specialty_retail', 'mobile_home_dealers', 'motion_picture_theaters', 'motor_freight_carriers_and_trucking', 'motor_homes_dealers', 'motor_vehicle_supplies_and_new_parts', 'motorcycle_shops_and_dealers', 'motorcycle_shops_dealers', 'music_stores_musical_instruments_pianos_and_sheet_music', 'news_dealers_and_newsstands', 'non_fi_money_orders', 'non_fi_stored_value_card_purchase_load', 'nondurable_goods', 'nurseries_lawn_and_garden_supply_stores', 'nursing_personal_care', 'office_and_commercial_furniture', 'opticians_eyeglasses', 'optometrists_ophthalmologist', 'orthopedic_goods_prosthetic_devices', 'osteopaths', 'package_stores_beer_wine_and_liquor', 'paints_varnishes_and_supplies', 'parking_lots_garages', 'passenger_railways', 'pawn_shops', 'pet_shops_pet_food_and_supplies', 'petroleum_and_petroleum_products', 'photo_developing', 'photographic_photocopy_microfilm_equipment_and_supplies', 'photographic_studios', 'picture_video_production', 'piece_goods_notions_and_other_dry_goods', 'plumbing_heating_equipment_and_supplies', 'political_organizations', 'postal_services_government_only', 'precious_stones_and_metals_watches_and_jewelry', 'professional_services', 'public_warehousing_and_storage', 'quick_copy_repro_and_blueprint', 'railroads', 'real_estate_agents_and_managers_rentals', 'record_stores', 'recreational_vehicle_rentals', 'religious_goods_stores', 'religious_organizations', 'roofing_siding_sheet_metal', 'secretarial_support_services', 'security_brokers_dealers', 'service_stations', 'sewing_needlework_fabric_and_piece_goods_stores', 'shoe_repair_hat_cleaning', 'shoe_stores', 'small_appliance_repair', 'snowmobile_dealers', 'special_trade_services', 'specialty_cleaning', 'sporting_goods_stores', 'sporting_recreation_camps', 'sports_and_riding_apparel_stores', 'sports_clubs_fields', 'stamp_and_coin_stores', 'stationary_office_supplies_printing_and_writing_paper', 'stationery_stores_office_and_school_supply_stores', 'swimming_pools_sales', 't_ui_travel_germany', 'tailors_alterations', 'tax_payments_government_agencies', 'tax_preparation_services', 'taxicabs_limousines', 'telecommunication_equipment_and_telephone_sales', 'telecommunication_services', 'telegraph_services', 'tent_and_awning_shops', 'testing_laboratories', 'theatrical_ticket_agencies', 'timeshares', 'tire_retreading_and_repair', 'tolls_bridge_fees', 'tourist_attractions_and_exhibits', 'towing_services', 'trailer_parks_campgrounds', 'transportation_services', 'travel_agencies_tour_operators', 'truck_stop_iteration', 'truck_utility_trailer_rentals', 'typesetting_plate_making_and_related_services', 'typewriter_stores', 'u_s_federal_government_agencies_or_departments', 'uniforms_commercial_clothing', 'used_merchandise_and_secondhand_stores', 'utilities', 'variety_stores', 'veterinary_services', 'video_amusement_game_supplies', 'video_game_arcades', 'video_tape_rental_stores', 'vocational_trade_schools', 'watch_jewelry_repair', 'welding_repair', 'wholesale_clubs', 'wig_and_toupee_stores', 'wires_money_orders', 'womens_accessory_and_specialty_shops', 'womens_ready_to_wear_stores', 'wrecking_and_salvage_yards']]"
        ]
        """
        Array of strings containing [categories](https://stripe.com/docs/api#issuing_authorization_object-merchant_data-category) of authorizations to allow. All other categories will be blocked. Cannot be set with `blocked_categories`.
        """
        blocked_categories: NotRequired[
            "List[Literal['ac_refrigeration_repair', 'accounting_bookkeeping_services', 'advertising_services', 'agricultural_cooperative', 'airlines_air_carriers', 'airports_flying_fields', 'ambulance_services', 'amusement_parks_carnivals', 'antique_reproductions', 'antique_shops', 'aquariums', 'architectural_surveying_services', 'art_dealers_and_galleries', 'artists_supply_and_craft_shops', 'auto_and_home_supply_stores', 'auto_body_repair_shops', 'auto_paint_shops', 'auto_service_shops', 'automated_cash_disburse', 'automated_fuel_dispensers', 'automobile_associations', 'automotive_parts_and_accessories_stores', 'automotive_tire_stores', 'bail_and_bond_payments', 'bakeries', 'bands_orchestras', 'barber_and_beauty_shops', 'betting_casino_gambling', 'bicycle_shops', 'billiard_pool_establishments', 'boat_dealers', 'boat_rentals_and_leases', 'book_stores', 'books_periodicals_and_newspapers', 'bowling_alleys', 'bus_lines', 'business_secretarial_schools', 'buying_shopping_services', 'cable_satellite_and_other_pay_television_and_radio', 'camera_and_photographic_supply_stores', 'candy_nut_and_confectionery_stores', 'car_and_truck_dealers_new_used', 'car_and_truck_dealers_used_only', 'car_rental_agencies', 'car_washes', 'carpentry_services', 'carpet_upholstery_cleaning', 'caterers', 'charitable_and_social_service_organizations_fundraising', 'chemicals_and_allied_products', 'child_care_services', 'childrens_and_infants_wear_stores', 'chiropodists_podiatrists', 'chiropractors', 'cigar_stores_and_stands', 'civic_social_fraternal_associations', 'cleaning_and_maintenance', 'clothing_rental', 'colleges_universities', 'commercial_equipment', 'commercial_footwear', 'commercial_photography_art_and_graphics', 'commuter_transport_and_ferries', 'computer_network_services', 'computer_programming', 'computer_repair', 'computer_software_stores', 'computers_peripherals_and_software', 'concrete_work_services', 'construction_materials', 'consulting_public_relations', 'correspondence_schools', 'cosmetic_stores', 'counseling_services', 'country_clubs', 'courier_services', 'court_costs', 'credit_reporting_agencies', 'cruise_lines', 'dairy_products_stores', 'dance_hall_studios_schools', 'dating_escort_services', 'dentists_orthodontists', 'department_stores', 'detective_agencies', 'digital_goods_applications', 'digital_goods_games', 'digital_goods_large_volume', 'digital_goods_media', 'direct_marketing_catalog_merchant', 'direct_marketing_combination_catalog_and_retail_merchant', 'direct_marketing_inbound_telemarketing', 'direct_marketing_insurance_services', 'direct_marketing_other', 'direct_marketing_outbound_telemarketing', 'direct_marketing_subscription', 'direct_marketing_travel', 'discount_stores', 'doctors', 'door_to_door_sales', 'drapery_window_covering_and_upholstery_stores', 'drinking_places', 'drug_stores_and_pharmacies', 'drugs_drug_proprietaries_and_druggist_sundries', 'dry_cleaners', 'durable_goods', 'duty_free_stores', 'eating_places_restaurants', 'educational_services', 'electric_razor_stores', 'electric_vehicle_charging', 'electrical_parts_and_equipment', 'electrical_services', 'electronics_repair_shops', 'electronics_stores', 'elementary_secondary_schools', 'emergency_services_gcas_visa_use_only', 'employment_temp_agencies', 'equipment_rental', 'exterminating_services', 'family_clothing_stores', 'fast_food_restaurants', 'financial_institutions', 'fines_government_administrative_entities', 'fireplace_fireplace_screens_and_accessories_stores', 'floor_covering_stores', 'florists', 'florists_supplies_nursery_stock_and_flowers', 'freezer_and_locker_meat_provisioners', 'fuel_dealers_non_automotive', 'funeral_services_crematories', 'furniture_home_furnishings_and_equipment_stores_except_appliances', 'furniture_repair_refinishing', 'furriers_and_fur_shops', 'general_services', 'gift_card_novelty_and_souvenir_shops', 'glass_paint_and_wallpaper_stores', 'glassware_crystal_stores', 'golf_courses_public', 'government_licensed_horse_dog_racing_us_region_only', 'government_licensed_online_casions_online_gambling_us_region_only', 'government_owned_lotteries_non_us_region', 'government_owned_lotteries_us_region_only', 'government_services', 'grocery_stores_supermarkets', 'hardware_equipment_and_supplies', 'hardware_stores', 'health_and_beauty_spas', 'hearing_aids_sales_and_supplies', 'heating_plumbing_a_c', 'hobby_toy_and_game_shops', 'home_supply_warehouse_stores', 'hospitals', 'hotels_motels_and_resorts', 'household_appliance_stores', 'industrial_supplies', 'information_retrieval_services', 'insurance_default', 'insurance_underwriting_premiums', 'intra_company_purchases', 'jewelry_stores_watches_clocks_and_silverware_stores', 'landscaping_services', 'laundries', 'laundry_cleaning_services', 'legal_services_attorneys', 'luggage_and_leather_goods_stores', 'lumber_building_materials_stores', 'manual_cash_disburse', 'marinas_service_and_supplies', 'marketplaces', 'masonry_stonework_and_plaster', 'massage_parlors', 'medical_and_dental_labs', 'medical_dental_ophthalmic_and_hospital_equipment_and_supplies', 'medical_services', 'membership_organizations', 'mens_and_boys_clothing_and_accessories_stores', 'mens_womens_clothing_stores', 'metal_service_centers', 'miscellaneous', 'miscellaneous_apparel_and_accessory_shops', 'miscellaneous_auto_dealers', 'miscellaneous_business_services', 'miscellaneous_food_stores', 'miscellaneous_general_merchandise', 'miscellaneous_general_services', 'miscellaneous_home_furnishing_specialty_stores', 'miscellaneous_publishing_and_printing', 'miscellaneous_recreation_services', 'miscellaneous_repair_shops', 'miscellaneous_specialty_retail', 'mobile_home_dealers', 'motion_picture_theaters', 'motor_freight_carriers_and_trucking', 'motor_homes_dealers', 'motor_vehicle_supplies_and_new_parts', 'motorcycle_shops_and_dealers', 'motorcycle_shops_dealers', 'music_stores_musical_instruments_pianos_and_sheet_music', 'news_dealers_and_newsstands', 'non_fi_money_orders', 'non_fi_stored_value_card_purchase_load', 'nondurable_goods', 'nurseries_lawn_and_garden_supply_stores', 'nursing_personal_care', 'office_and_commercial_furniture', 'opticians_eyeglasses', 'optometrists_ophthalmologist', 'orthopedic_goods_prosthetic_devices', 'osteopaths', 'package_stores_beer_wine_and_liquor', 'paints_varnishes_and_supplies', 'parking_lots_garages', 'passenger_railways', 'pawn_shops', 'pet_shops_pet_food_and_supplies', 'petroleum_and_petroleum_products', 'photo_developing', 'photographic_photocopy_microfilm_equipment_and_supplies', 'photographic_studios', 'picture_video_production', 'piece_goods_notions_and_other_dry_goods', 'plumbing_heating_equipment_and_supplies', 'political_organizations', 'postal_services_government_only', 'precious_stones_and_metals_watches_and_jewelry', 'professional_services', 'public_warehousing_and_storage', 'quick_copy_repro_and_blueprint', 'railroads', 'real_estate_agents_and_managers_rentals', 'record_stores', 'recreational_vehicle_rentals', 'religious_goods_stores', 'religious_organizations', 'roofing_siding_sheet_metal', 'secretarial_support_services', 'security_brokers_dealers', 'service_stations', 'sewing_needlework_fabric_and_piece_goods_stores', 'shoe_repair_hat_cleaning', 'shoe_stores', 'small_appliance_repair', 'snowmobile_dealers', 'special_trade_services', 'specialty_cleaning', 'sporting_goods_stores', 'sporting_recreation_camps', 'sports_and_riding_apparel_stores', 'sports_clubs_fields', 'stamp_and_coin_stores', 'stationary_office_supplies_printing_and_writing_paper', 'stationery_stores_office_and_school_supply_stores', 'swimming_pools_sales', 't_ui_travel_germany', 'tailors_alterations', 'tax_payments_government_agencies', 'tax_preparation_services', 'taxicabs_limousines', 'telecommunication_equipment_and_telephone_sales', 'telecommunication_services', 'telegraph_services', 'tent_and_awning_shops', 'testing_laboratories', 'theatrical_ticket_agencies', 'timeshares', 'tire_retreading_and_repair', 'tolls_bridge_fees', 'tourist_attractions_and_exhibits', 'towing_services', 'trailer_parks_campgrounds', 'transportation_services', 'travel_agencies_tour_operators', 'truck_stop_iteration', 'truck_utility_trailer_rentals', 'typesetting_plate_making_and_related_services', 'typewriter_stores', 'u_s_federal_government_agencies_or_departments', 'uniforms_commercial_clothing', 'used_merchandise_and_secondhand_stores', 'utilities', 'variety_stores', 'veterinary_services', 'video_amusement_game_supplies', 'video_game_arcades', 'video_tape_rental_stores', 'vocational_trade_schools', 'watch_jewelry_repair', 'welding_repair', 'wholesale_clubs', 'wig_and_toupee_stores', 'wires_money_orders', 'womens_accessory_and_specialty_shops', 'womens_ready_to_wear_stores', 'wrecking_and_salvage_yards']]"
        ]
        """
        Array of strings containing [categories](https://stripe.com/docs/api#issuing_authorization_object-merchant_data-category) of authorizations to decline. All other categories will be allowed. Cannot be set with `allowed_categories`.
        """
        spending_limits: NotRequired[
            "List[Cardholder.CreateParamsSpendingControlsSpendingLimit]"
        ]
        """
        Limit spending with amount-based rules that apply across this cardholder's cards.
        """
        spending_limits_currency: NotRequired["str"]
        """
        Currency of amounts within `spending_limits`. Defaults to your merchant country's currency.
        """

    class CreateParamsSpendingControlsSpendingLimit(TypedDict):
        amount: int
        """
        Maximum amount allowed to spend per interval.
        """
        categories: NotRequired[
            "List[Literal['ac_refrigeration_repair', 'accounting_bookkeeping_services', 'advertising_services', 'agricultural_cooperative', 'airlines_air_carriers', 'airports_flying_fields', 'ambulance_services', 'amusement_parks_carnivals', 'antique_reproductions', 'antique_shops', 'aquariums', 'architectural_surveying_services', 'art_dealers_and_galleries', 'artists_supply_and_craft_shops', 'auto_and_home_supply_stores', 'auto_body_repair_shops', 'auto_paint_shops', 'auto_service_shops', 'automated_cash_disburse', 'automated_fuel_dispensers', 'automobile_associations', 'automotive_parts_and_accessories_stores', 'automotive_tire_stores', 'bail_and_bond_payments', 'bakeries', 'bands_orchestras', 'barber_and_beauty_shops', 'betting_casino_gambling', 'bicycle_shops', 'billiard_pool_establishments', 'boat_dealers', 'boat_rentals_and_leases', 'book_stores', 'books_periodicals_and_newspapers', 'bowling_alleys', 'bus_lines', 'business_secretarial_schools', 'buying_shopping_services', 'cable_satellite_and_other_pay_television_and_radio', 'camera_and_photographic_supply_stores', 'candy_nut_and_confectionery_stores', 'car_and_truck_dealers_new_used', 'car_and_truck_dealers_used_only', 'car_rental_agencies', 'car_washes', 'carpentry_services', 'carpet_upholstery_cleaning', 'caterers', 'charitable_and_social_service_organizations_fundraising', 'chemicals_and_allied_products', 'child_care_services', 'childrens_and_infants_wear_stores', 'chiropodists_podiatrists', 'chiropractors', 'cigar_stores_and_stands', 'civic_social_fraternal_associations', 'cleaning_and_maintenance', 'clothing_rental', 'colleges_universities', 'commercial_equipment', 'commercial_footwear', 'commercial_photography_art_and_graphics', 'commuter_transport_and_ferries', 'computer_network_services', 'computer_programming', 'computer_repair', 'computer_software_stores', 'computers_peripherals_and_software', 'concrete_work_services', 'construction_materials', 'consulting_public_relations', 'correspondence_schools', 'cosmetic_stores', 'counseling_services', 'country_clubs', 'courier_services', 'court_costs', 'credit_reporting_agencies', 'cruise_lines', 'dairy_products_stores', 'dance_hall_studios_schools', 'dating_escort_services', 'dentists_orthodontists', 'department_stores', 'detective_agencies', 'digital_goods_applications', 'digital_goods_games', 'digital_goods_large_volume', 'digital_goods_media', 'direct_marketing_catalog_merchant', 'direct_marketing_combination_catalog_and_retail_merchant', 'direct_marketing_inbound_telemarketing', 'direct_marketing_insurance_services', 'direct_marketing_other', 'direct_marketing_outbound_telemarketing', 'direct_marketing_subscription', 'direct_marketing_travel', 'discount_stores', 'doctors', 'door_to_door_sales', 'drapery_window_covering_and_upholstery_stores', 'drinking_places', 'drug_stores_and_pharmacies', 'drugs_drug_proprietaries_and_druggist_sundries', 'dry_cleaners', 'durable_goods', 'duty_free_stores', 'eating_places_restaurants', 'educational_services', 'electric_razor_stores', 'electric_vehicle_charging', 'electrical_parts_and_equipment', 'electrical_services', 'electronics_repair_shops', 'electronics_stores', 'elementary_secondary_schools', 'emergency_services_gcas_visa_use_only', 'employment_temp_agencies', 'equipment_rental', 'exterminating_services', 'family_clothing_stores', 'fast_food_restaurants', 'financial_institutions', 'fines_government_administrative_entities', 'fireplace_fireplace_screens_and_accessories_stores', 'floor_covering_stores', 'florists', 'florists_supplies_nursery_stock_and_flowers', 'freezer_and_locker_meat_provisioners', 'fuel_dealers_non_automotive', 'funeral_services_crematories', 'furniture_home_furnishings_and_equipment_stores_except_appliances', 'furniture_repair_refinishing', 'furriers_and_fur_shops', 'general_services', 'gift_card_novelty_and_souvenir_shops', 'glass_paint_and_wallpaper_stores', 'glassware_crystal_stores', 'golf_courses_public', 'government_licensed_horse_dog_racing_us_region_only', 'government_licensed_online_casions_online_gambling_us_region_only', 'government_owned_lotteries_non_us_region', 'government_owned_lotteries_us_region_only', 'government_services', 'grocery_stores_supermarkets', 'hardware_equipment_and_supplies', 'hardware_stores', 'health_and_beauty_spas', 'hearing_aids_sales_and_supplies', 'heating_plumbing_a_c', 'hobby_toy_and_game_shops', 'home_supply_warehouse_stores', 'hospitals', 'hotels_motels_and_resorts', 'household_appliance_stores', 'industrial_supplies', 'information_retrieval_services', 'insurance_default', 'insurance_underwriting_premiums', 'intra_company_purchases', 'jewelry_stores_watches_clocks_and_silverware_stores', 'landscaping_services', 'laundries', 'laundry_cleaning_services', 'legal_services_attorneys', 'luggage_and_leather_goods_stores', 'lumber_building_materials_stores', 'manual_cash_disburse', 'marinas_service_and_supplies', 'marketplaces', 'masonry_stonework_and_plaster', 'massage_parlors', 'medical_and_dental_labs', 'medical_dental_ophthalmic_and_hospital_equipment_and_supplies', 'medical_services', 'membership_organizations', 'mens_and_boys_clothing_and_accessories_stores', 'mens_womens_clothing_stores', 'metal_service_centers', 'miscellaneous', 'miscellaneous_apparel_and_accessory_shops', 'miscellaneous_auto_dealers', 'miscellaneous_business_services', 'miscellaneous_food_stores', 'miscellaneous_general_merchandise', 'miscellaneous_general_services', 'miscellaneous_home_furnishing_specialty_stores', 'miscellaneous_publishing_and_printing', 'miscellaneous_recreation_services', 'miscellaneous_repair_shops', 'miscellaneous_specialty_retail', 'mobile_home_dealers', 'motion_picture_theaters', 'motor_freight_carriers_and_trucking', 'motor_homes_dealers', 'motor_vehicle_supplies_and_new_parts', 'motorcycle_shops_and_dealers', 'motorcycle_shops_dealers', 'music_stores_musical_instruments_pianos_and_sheet_music', 'news_dealers_and_newsstands', 'non_fi_money_orders', 'non_fi_stored_value_card_purchase_load', 'nondurable_goods', 'nurseries_lawn_and_garden_supply_stores', 'nursing_personal_care', 'office_and_commercial_furniture', 'opticians_eyeglasses', 'optometrists_ophthalmologist', 'orthopedic_goods_prosthetic_devices', 'osteopaths', 'package_stores_beer_wine_and_liquor', 'paints_varnishes_and_supplies', 'parking_lots_garages', 'passenger_railways', 'pawn_shops', 'pet_shops_pet_food_and_supplies', 'petroleum_and_petroleum_products', 'photo_developing', 'photographic_photocopy_microfilm_equipment_and_supplies', 'photographic_studios', 'picture_video_production', 'piece_goods_notions_and_other_dry_goods', 'plumbing_heating_equipment_and_supplies', 'political_organizations', 'postal_services_government_only', 'precious_stones_and_metals_watches_and_jewelry', 'professional_services', 'public_warehousing_and_storage', 'quick_copy_repro_and_blueprint', 'railroads', 'real_estate_agents_and_managers_rentals', 'record_stores', 'recreational_vehicle_rentals', 'religious_goods_stores', 'religious_organizations', 'roofing_siding_sheet_metal', 'secretarial_support_services', 'security_brokers_dealers', 'service_stations', 'sewing_needlework_fabric_and_piece_goods_stores', 'shoe_repair_hat_cleaning', 'shoe_stores', 'small_appliance_repair', 'snowmobile_dealers', 'special_trade_services', 'specialty_cleaning', 'sporting_goods_stores', 'sporting_recreation_camps', 'sports_and_riding_apparel_stores', 'sports_clubs_fields', 'stamp_and_coin_stores', 'stationary_office_supplies_printing_and_writing_paper', 'stationery_stores_office_and_school_supply_stores', 'swimming_pools_sales', 't_ui_travel_germany', 'tailors_alterations', 'tax_payments_government_agencies', 'tax_preparation_services', 'taxicabs_limousines', 'telecommunication_equipment_and_telephone_sales', 'telecommunication_services', 'telegraph_services', 'tent_and_awning_shops', 'testing_laboratories', 'theatrical_ticket_agencies', 'timeshares', 'tire_retreading_and_repair', 'tolls_bridge_fees', 'tourist_attractions_and_exhibits', 'towing_services', 'trailer_parks_campgrounds', 'transportation_services', 'travel_agencies_tour_operators', 'truck_stop_iteration', 'truck_utility_trailer_rentals', 'typesetting_plate_making_and_related_services', 'typewriter_stores', 'u_s_federal_government_agencies_or_departments', 'uniforms_commercial_clothing', 'used_merchandise_and_secondhand_stores', 'utilities', 'variety_stores', 'veterinary_services', 'video_amusement_game_supplies', 'video_game_arcades', 'video_tape_rental_stores', 'vocational_trade_schools', 'watch_jewelry_repair', 'welding_repair', 'wholesale_clubs', 'wig_and_toupee_stores', 'wires_money_orders', 'womens_accessory_and_specialty_shops', 'womens_ready_to_wear_stores', 'wrecking_and_salvage_yards']]"
        ]
        """
        Array of strings containing [categories](https://stripe.com/docs/api#issuing_authorization_object-merchant_data-category) this limit applies to. Omitting this field will apply the limit to all categories.
        """
        interval: Literal[
            "all_time",
            "daily",
            "monthly",
            "per_authorization",
            "weekly",
            "yearly",
        ]
        """
        Interval (or event) to which the amount applies.
        """

    class CreateParamsIndividual(TypedDict):
        card_issuing: NotRequired[
            "Cardholder.CreateParamsIndividualCardIssuing"
        ]
        """
        Information related to the card_issuing program for this cardholder.
        """
        dob: NotRequired["Cardholder.CreateParamsIndividualDob"]
        """
        The date of birth of this cardholder. Cardholders must be older than 13 years old.
        """
        first_name: NotRequired["str"]
        """
        The first name of this cardholder. Required before activating Cards. This field cannot contain any numbers, special characters (except periods, commas, hyphens, spaces and apostrophes) or non-latin letters.
        """
        last_name: NotRequired["str"]
        """
        The last name of this cardholder. Required before activating Cards. This field cannot contain any numbers, special characters (except periods, commas, hyphens, spaces and apostrophes) or non-latin letters.
        """
        verification: NotRequired[
            "Cardholder.CreateParamsIndividualVerification"
        ]
        """
        Government-issued ID document for this cardholder.
        """

    class CreateParamsIndividualVerification(TypedDict):
        document: NotRequired[
            "Cardholder.CreateParamsIndividualVerificationDocument"
        ]
        """
        An identifying document, either a passport or local ID card.
        """

    class CreateParamsIndividualVerificationDocument(TypedDict):
        back: NotRequired["str"]
        """
        The back of an ID returned by a [file upload](https://stripe.com/docs/api#create_file) with a `purpose` value of `identity_document`.
        """
        front: NotRequired["str"]
        """
        The front of an ID returned by a [file upload](https://stripe.com/docs/api#create_file) with a `purpose` value of `identity_document`.
        """

    class CreateParamsIndividualDob(TypedDict):
        day: int
        """
        The day of birth, between 1 and 31.
        """
        month: int
        """
        The month of birth, between 1 and 12.
        """
        year: int
        """
        The four-digit year of birth.
        """

    class CreateParamsIndividualCardIssuing(TypedDict):
        user_terms_acceptance: NotRequired[
            "Cardholder.CreateParamsIndividualCardIssuingUserTermsAcceptance"
        ]
        """
        Information about cardholder acceptance of [Authorized User Terms](https://stripe.com/docs/issuing/cards).
        """

    class CreateParamsIndividualCardIssuingUserTermsAcceptance(TypedDict):
        date: NotRequired["int"]
        """
        The Unix timestamp marking when the cardholder accepted the Authorized User Terms. Required for Celtic Spend Card users.
        """
        ip: NotRequired["str"]
        """
        The IP address from which the cardholder accepted the Authorized User Terms. Required for Celtic Spend Card users.
        """
        user_agent: NotRequired["Literal['']|str"]
        """
        The user agent of the browser from which the cardholder accepted the Authorized User Terms.
        """

    class CreateParamsCompany(TypedDict):
        tax_id: NotRequired["str"]
        """
        The entity's business ID number.
        """

    class CreateParamsBilling(TypedDict):
        address: "Cardholder.CreateParamsBillingAddress"
        """
        The cardholder's billing address.
        """

    class CreateParamsBillingAddress(TypedDict):
        city: str
        """
        City, district, suburb, town, or village.
        """
        country: str
        """
        Two-letter country code ([ISO 3166-1 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2)).
        """
        line1: str
        """
        Address line 1 (e.g., street, PO Box, or company name).
        """
        line2: NotRequired["str"]
        """
        Address line 2 (e.g., apartment, suite, unit, or building).
        """
        postal_code: str
        """
        ZIP or postal code.
        """
        state: NotRequired["str"]
        """
        State, county, province, or region.
        """

    class ListParams(RequestOptions):
        created: NotRequired["Cardholder.ListParamsCreated|int"]
        """
        Only return cardholders that were created during the given date interval.
        """
        email: NotRequired["str"]
        """
        Only return cardholders that have the given email address.
        """
        ending_before: NotRequired["str"]
        """
        A cursor for use in pagination. `ending_before` is an object ID that defines your place in the list. For instance, if you make a list request and receive 100 objects, starting with `obj_bar`, your subsequent call can include `ending_before=obj_bar` in order to fetch the previous page of the list.
        """
        expand: NotRequired["List[str]"]
        """
        Specifies which fields in the response should be expanded.
        """
        limit: NotRequired["int"]
        """
        A limit on the number of objects to be returned. Limit can range between 1 and 100, and the default is 10.
        """
        phone_number: NotRequired["str"]
        """
        Only return cardholders that have the given phone number.
        """
        starting_after: NotRequired["str"]
        """
        A cursor for use in pagination. `starting_after` is an object ID that defines your place in the list. For instance, if you make a list request and receive 100 objects, ending with `obj_foo`, your subsequent call can include `starting_after=obj_foo` in order to fetch the next page of the list.
        """
        status: NotRequired["Literal['active', 'blocked', 'inactive']"]
        """
        Only return cardholders that have the given status. One of `active`, `inactive`, or `blocked`.
        """
        type: NotRequired["Literal['company', 'individual']"]
        """
        Only return cardholders that have the given type. One of `individual` or `company`.
        """

    class ListParamsCreated(TypedDict):
        gt: NotRequired["int"]
        """
        Minimum value to filter by (exclusive)
        """
        gte: NotRequired["int"]
        """
        Minimum value to filter by (inclusive)
        """
        lt: NotRequired["int"]
        """
        Maximum value to filter by (exclusive)
        """
        lte: NotRequired["int"]
        """
        Maximum value to filter by (inclusive)
        """

    class ModifyParams(RequestOptions):
        billing: NotRequired["Cardholder.ModifyParamsBilling"]
        """
        The cardholder's billing address.
        """
        company: NotRequired["Cardholder.ModifyParamsCompany"]
        """
        Additional information about a `company` cardholder.
        """
        email: NotRequired["str"]
        """
        The cardholder's email address.
        """
        expand: NotRequired["List[str]"]
        """
        Specifies which fields in the response should be expanded.
        """
        individual: NotRequired["Cardholder.ModifyParamsIndividual"]
        """
        Additional information about an `individual` cardholder.
        """
        metadata: NotRequired["Dict[str, str]"]
        """
        Set of [key-value pairs](https://stripe.com/docs/api/metadata) that you can attach to an object. This can be useful for storing additional information about the object in a structured format. Individual keys can be unset by posting an empty value to them. All keys can be unset by posting an empty value to `metadata`.
        """
        phone_number: NotRequired["str"]
        """
        The cardholder's phone number. This is required for all cardholders who will be creating EU cards. See the [3D Secure documentation](https://stripe.com/docs/issuing/3d-secure) for more details.
        """
        preferred_locales: NotRequired[
            "List[Literal['de', 'en', 'es', 'fr', 'it']]"
        ]
        """
        The cardholder's preferred locales (languages), ordered by preference. Locales can be `de`, `en`, `es`, `fr`, or `it`.
         This changes the language of the [3D Secure flow](https://stripe.com/docs/issuing/3d-secure) and one-time password messages sent to the cardholder.
        """
        spending_controls: NotRequired[
            "Cardholder.ModifyParamsSpendingControls"
        ]
        """
        Rules that control spending across this cardholder's cards. Refer to our [documentation](https://stripe.com/docs/issuing/controls/spending-controls) for more details.
        """
        status: NotRequired["Literal['active', 'inactive']"]
        """
        Specifies whether to permit authorizations on this cardholder's cards.
        """

    class ModifyParamsSpendingControls(TypedDict):
        allowed_categories: NotRequired[
            "List[Literal['ac_refrigeration_repair', 'accounting_bookkeeping_services', 'advertising_services', 'agricultural_cooperative', 'airlines_air_carriers', 'airports_flying_fields', 'ambulance_services', 'amusement_parks_carnivals', 'antique_reproductions', 'antique_shops', 'aquariums', 'architectural_surveying_services', 'art_dealers_and_galleries', 'artists_supply_and_craft_shops', 'auto_and_home_supply_stores', 'auto_body_repair_shops', 'auto_paint_shops', 'auto_service_shops', 'automated_cash_disburse', 'automated_fuel_dispensers', 'automobile_associations', 'automotive_parts_and_accessories_stores', 'automotive_tire_stores', 'bail_and_bond_payments', 'bakeries', 'bands_orchestras', 'barber_and_beauty_shops', 'betting_casino_gambling', 'bicycle_shops', 'billiard_pool_establishments', 'boat_dealers', 'boat_rentals_and_leases', 'book_stores', 'books_periodicals_and_newspapers', 'bowling_alleys', 'bus_lines', 'business_secretarial_schools', 'buying_shopping_services', 'cable_satellite_and_other_pay_television_and_radio', 'camera_and_photographic_supply_stores', 'candy_nut_and_confectionery_stores', 'car_and_truck_dealers_new_used', 'car_and_truck_dealers_used_only', 'car_rental_agencies', 'car_washes', 'carpentry_services', 'carpet_upholstery_cleaning', 'caterers', 'charitable_and_social_service_organizations_fundraising', 'chemicals_and_allied_products', 'child_care_services', 'childrens_and_infants_wear_stores', 'chiropodists_podiatrists', 'chiropractors', 'cigar_stores_and_stands', 'civic_social_fraternal_associations', 'cleaning_and_maintenance', 'clothing_rental', 'colleges_universities', 'commercial_equipment', 'commercial_footwear', 'commercial_photography_art_and_graphics', 'commuter_transport_and_ferries', 'computer_network_services', 'computer_programming', 'computer_repair', 'computer_software_stores', 'computers_peripherals_and_software', 'concrete_work_services', 'construction_materials', 'consulting_public_relations', 'correspondence_schools', 'cosmetic_stores', 'counseling_services', 'country_clubs', 'courier_services', 'court_costs', 'credit_reporting_agencies', 'cruise_lines', 'dairy_products_stores', 'dance_hall_studios_schools', 'dating_escort_services', 'dentists_orthodontists', 'department_stores', 'detective_agencies', 'digital_goods_applications', 'digital_goods_games', 'digital_goods_large_volume', 'digital_goods_media', 'direct_marketing_catalog_merchant', 'direct_marketing_combination_catalog_and_retail_merchant', 'direct_marketing_inbound_telemarketing', 'direct_marketing_insurance_services', 'direct_marketing_other', 'direct_marketing_outbound_telemarketing', 'direct_marketing_subscription', 'direct_marketing_travel', 'discount_stores', 'doctors', 'door_to_door_sales', 'drapery_window_covering_and_upholstery_stores', 'drinking_places', 'drug_stores_and_pharmacies', 'drugs_drug_proprietaries_and_druggist_sundries', 'dry_cleaners', 'durable_goods', 'duty_free_stores', 'eating_places_restaurants', 'educational_services', 'electric_razor_stores', 'electric_vehicle_charging', 'electrical_parts_and_equipment', 'electrical_services', 'electronics_repair_shops', 'electronics_stores', 'elementary_secondary_schools', 'emergency_services_gcas_visa_use_only', 'employment_temp_agencies', 'equipment_rental', 'exterminating_services', 'family_clothing_stores', 'fast_food_restaurants', 'financial_institutions', 'fines_government_administrative_entities', 'fireplace_fireplace_screens_and_accessories_stores', 'floor_covering_stores', 'florists', 'florists_supplies_nursery_stock_and_flowers', 'freezer_and_locker_meat_provisioners', 'fuel_dealers_non_automotive', 'funeral_services_crematories', 'furniture_home_furnishings_and_equipment_stores_except_appliances', 'furniture_repair_refinishing', 'furriers_and_fur_shops', 'general_services', 'gift_card_novelty_and_souvenir_shops', 'glass_paint_and_wallpaper_stores', 'glassware_crystal_stores', 'golf_courses_public', 'government_licensed_horse_dog_racing_us_region_only', 'government_licensed_online_casions_online_gambling_us_region_only', 'government_owned_lotteries_non_us_region', 'government_owned_lotteries_us_region_only', 'government_services', 'grocery_stores_supermarkets', 'hardware_equipment_and_supplies', 'hardware_stores', 'health_and_beauty_spas', 'hearing_aids_sales_and_supplies', 'heating_plumbing_a_c', 'hobby_toy_and_game_shops', 'home_supply_warehouse_stores', 'hospitals', 'hotels_motels_and_resorts', 'household_appliance_stores', 'industrial_supplies', 'information_retrieval_services', 'insurance_default', 'insurance_underwriting_premiums', 'intra_company_purchases', 'jewelry_stores_watches_clocks_and_silverware_stores', 'landscaping_services', 'laundries', 'laundry_cleaning_services', 'legal_services_attorneys', 'luggage_and_leather_goods_stores', 'lumber_building_materials_stores', 'manual_cash_disburse', 'marinas_service_and_supplies', 'marketplaces', 'masonry_stonework_and_plaster', 'massage_parlors', 'medical_and_dental_labs', 'medical_dental_ophthalmic_and_hospital_equipment_and_supplies', 'medical_services', 'membership_organizations', 'mens_and_boys_clothing_and_accessories_stores', 'mens_womens_clothing_stores', 'metal_service_centers', 'miscellaneous', 'miscellaneous_apparel_and_accessory_shops', 'miscellaneous_auto_dealers', 'miscellaneous_business_services', 'miscellaneous_food_stores', 'miscellaneous_general_merchandise', 'miscellaneous_general_services', 'miscellaneous_home_furnishing_specialty_stores', 'miscellaneous_publishing_and_printing', 'miscellaneous_recreation_services', 'miscellaneous_repair_shops', 'miscellaneous_specialty_retail', 'mobile_home_dealers', 'motion_picture_theaters', 'motor_freight_carriers_and_trucking', 'motor_homes_dealers', 'motor_vehicle_supplies_and_new_parts', 'motorcycle_shops_and_dealers', 'motorcycle_shops_dealers', 'music_stores_musical_instruments_pianos_and_sheet_music', 'news_dealers_and_newsstands', 'non_fi_money_orders', 'non_fi_stored_value_card_purchase_load', 'nondurable_goods', 'nurseries_lawn_and_garden_supply_stores', 'nursing_personal_care', 'office_and_commercial_furniture', 'opticians_eyeglasses', 'optometrists_ophthalmologist', 'orthopedic_goods_prosthetic_devices', 'osteopaths', 'package_stores_beer_wine_and_liquor', 'paints_varnishes_and_supplies', 'parking_lots_garages', 'passenger_railways', 'pawn_shops', 'pet_shops_pet_food_and_supplies', 'petroleum_and_petroleum_products', 'photo_developing', 'photographic_photocopy_microfilm_equipment_and_supplies', 'photographic_studios', 'picture_video_production', 'piece_goods_notions_and_other_dry_goods', 'plumbing_heating_equipment_and_supplies', 'political_organizations', 'postal_services_government_only', 'precious_stones_and_metals_watches_and_jewelry', 'professional_services', 'public_warehousing_and_storage', 'quick_copy_repro_and_blueprint', 'railroads', 'real_estate_agents_and_managers_rentals', 'record_stores', 'recreational_vehicle_rentals', 'religious_goods_stores', 'religious_organizations', 'roofing_siding_sheet_metal', 'secretarial_support_services', 'security_brokers_dealers', 'service_stations', 'sewing_needlework_fabric_and_piece_goods_stores', 'shoe_repair_hat_cleaning', 'shoe_stores', 'small_appliance_repair', 'snowmobile_dealers', 'special_trade_services', 'specialty_cleaning', 'sporting_goods_stores', 'sporting_recreation_camps', 'sports_and_riding_apparel_stores', 'sports_clubs_fields', 'stamp_and_coin_stores', 'stationary_office_supplies_printing_and_writing_paper', 'stationery_stores_office_and_school_supply_stores', 'swimming_pools_sales', 't_ui_travel_germany', 'tailors_alterations', 'tax_payments_government_agencies', 'tax_preparation_services', 'taxicabs_limousines', 'telecommunication_equipment_and_telephone_sales', 'telecommunication_services', 'telegraph_services', 'tent_and_awning_shops', 'testing_laboratories', 'theatrical_ticket_agencies', 'timeshares', 'tire_retreading_and_repair', 'tolls_bridge_fees', 'tourist_attractions_and_exhibits', 'towing_services', 'trailer_parks_campgrounds', 'transportation_services', 'travel_agencies_tour_operators', 'truck_stop_iteration', 'truck_utility_trailer_rentals', 'typesetting_plate_making_and_related_services', 'typewriter_stores', 'u_s_federal_government_agencies_or_departments', 'uniforms_commercial_clothing', 'used_merchandise_and_secondhand_stores', 'utilities', 'variety_stores', 'veterinary_services', 'video_amusement_game_supplies', 'video_game_arcades', 'video_tape_rental_stores', 'vocational_trade_schools', 'watch_jewelry_repair', 'welding_repair', 'wholesale_clubs', 'wig_and_toupee_stores', 'wires_money_orders', 'womens_accessory_and_specialty_shops', 'womens_ready_to_wear_stores', 'wrecking_and_salvage_yards']]"
        ]
        """
        Array of strings containing [categories](https://stripe.com/docs/api#issuing_authorization_object-merchant_data-category) of authorizations to allow. All other categories will be blocked. Cannot be set with `blocked_categories`.
        """
        blocked_categories: NotRequired[
            "List[Literal['ac_refrigeration_repair', 'accounting_bookkeeping_services', 'advertising_services', 'agricultural_cooperative', 'airlines_air_carriers', 'airports_flying_fields', 'ambulance_services', 'amusement_parks_carnivals', 'antique_reproductions', 'antique_shops', 'aquariums', 'architectural_surveying_services', 'art_dealers_and_galleries', 'artists_supply_and_craft_shops', 'auto_and_home_supply_stores', 'auto_body_repair_shops', 'auto_paint_shops', 'auto_service_shops', 'automated_cash_disburse', 'automated_fuel_dispensers', 'automobile_associations', 'automotive_parts_and_accessories_stores', 'automotive_tire_stores', 'bail_and_bond_payments', 'bakeries', 'bands_orchestras', 'barber_and_beauty_shops', 'betting_casino_gambling', 'bicycle_shops', 'billiard_pool_establishments', 'boat_dealers', 'boat_rentals_and_leases', 'book_stores', 'books_periodicals_and_newspapers', 'bowling_alleys', 'bus_lines', 'business_secretarial_schools', 'buying_shopping_services', 'cable_satellite_and_other_pay_television_and_radio', 'camera_and_photographic_supply_stores', 'candy_nut_and_confectionery_stores', 'car_and_truck_dealers_new_used', 'car_and_truck_dealers_used_only', 'car_rental_agencies', 'car_washes', 'carpentry_services', 'carpet_upholstery_cleaning', 'caterers', 'charitable_and_social_service_organizations_fundraising', 'chemicals_and_allied_products', 'child_care_services', 'childrens_and_infants_wear_stores', 'chiropodists_podiatrists', 'chiropractors', 'cigar_stores_and_stands', 'civic_social_fraternal_associations', 'cleaning_and_maintenance', 'clothing_rental', 'colleges_universities', 'commercial_equipment', 'commercial_footwear', 'commercial_photography_art_and_graphics', 'commuter_transport_and_ferries', 'computer_network_services', 'computer_programming', 'computer_repair', 'computer_software_stores', 'computers_peripherals_and_software', 'concrete_work_services', 'construction_materials', 'consulting_public_relations', 'correspondence_schools', 'cosmetic_stores', 'counseling_services', 'country_clubs', 'courier_services', 'court_costs', 'credit_reporting_agencies', 'cruise_lines', 'dairy_products_stores', 'dance_hall_studios_schools', 'dating_escort_services', 'dentists_orthodontists', 'department_stores', 'detective_agencies', 'digital_goods_applications', 'digital_goods_games', 'digital_goods_large_volume', 'digital_goods_media', 'direct_marketing_catalog_merchant', 'direct_marketing_combination_catalog_and_retail_merchant', 'direct_marketing_inbound_telemarketing', 'direct_marketing_insurance_services', 'direct_marketing_other', 'direct_marketing_outbound_telemarketing', 'direct_marketing_subscription', 'direct_marketing_travel', 'discount_stores', 'doctors', 'door_to_door_sales', 'drapery_window_covering_and_upholstery_stores', 'drinking_places', 'drug_stores_and_pharmacies', 'drugs_drug_proprietaries_and_druggist_sundries', 'dry_cleaners', 'durable_goods', 'duty_free_stores', 'eating_places_restaurants', 'educational_services', 'electric_razor_stores', 'electric_vehicle_charging', 'electrical_parts_and_equipment', 'electrical_services', 'electronics_repair_shops', 'electronics_stores', 'elementary_secondary_schools', 'emergency_services_gcas_visa_use_only', 'employment_temp_agencies', 'equipment_rental', 'exterminating_services', 'family_clothing_stores', 'fast_food_restaurants', 'financial_institutions', 'fines_government_administrative_entities', 'fireplace_fireplace_screens_and_accessories_stores', 'floor_covering_stores', 'florists', 'florists_supplies_nursery_stock_and_flowers', 'freezer_and_locker_meat_provisioners', 'fuel_dealers_non_automotive', 'funeral_services_crematories', 'furniture_home_furnishings_and_equipment_stores_except_appliances', 'furniture_repair_refinishing', 'furriers_and_fur_shops', 'general_services', 'gift_card_novelty_and_souvenir_shops', 'glass_paint_and_wallpaper_stores', 'glassware_crystal_stores', 'golf_courses_public', 'government_licensed_horse_dog_racing_us_region_only', 'government_licensed_online_casions_online_gambling_us_region_only', 'government_owned_lotteries_non_us_region', 'government_owned_lotteries_us_region_only', 'government_services', 'grocery_stores_supermarkets', 'hardware_equipment_and_supplies', 'hardware_stores', 'health_and_beauty_spas', 'hearing_aids_sales_and_supplies', 'heating_plumbing_a_c', 'hobby_toy_and_game_shops', 'home_supply_warehouse_stores', 'hospitals', 'hotels_motels_and_resorts', 'household_appliance_stores', 'industrial_supplies', 'information_retrieval_services', 'insurance_default', 'insurance_underwriting_premiums', 'intra_company_purchases', 'jewelry_stores_watches_clocks_and_silverware_stores', 'landscaping_services', 'laundries', 'laundry_cleaning_services', 'legal_services_attorneys', 'luggage_and_leather_goods_stores', 'lumber_building_materials_stores', 'manual_cash_disburse', 'marinas_service_and_supplies', 'marketplaces', 'masonry_stonework_and_plaster', 'massage_parlors', 'medical_and_dental_labs', 'medical_dental_ophthalmic_and_hospital_equipment_and_supplies', 'medical_services', 'membership_organizations', 'mens_and_boys_clothing_and_accessories_stores', 'mens_womens_clothing_stores', 'metal_service_centers', 'miscellaneous', 'miscellaneous_apparel_and_accessory_shops', 'miscellaneous_auto_dealers', 'miscellaneous_business_services', 'miscellaneous_food_stores', 'miscellaneous_general_merchandise', 'miscellaneous_general_services', 'miscellaneous_home_furnishing_specialty_stores', 'miscellaneous_publishing_and_printing', 'miscellaneous_recreation_services', 'miscellaneous_repair_shops', 'miscellaneous_specialty_retail', 'mobile_home_dealers', 'motion_picture_theaters', 'motor_freight_carriers_and_trucking', 'motor_homes_dealers', 'motor_vehicle_supplies_and_new_parts', 'motorcycle_shops_and_dealers', 'motorcycle_shops_dealers', 'music_stores_musical_instruments_pianos_and_sheet_music', 'news_dealers_and_newsstands', 'non_fi_money_orders', 'non_fi_stored_value_card_purchase_load', 'nondurable_goods', 'nurseries_lawn_and_garden_supply_stores', 'nursing_personal_care', 'office_and_commercial_furniture', 'opticians_eyeglasses', 'optometrists_ophthalmologist', 'orthopedic_goods_prosthetic_devices', 'osteopaths', 'package_stores_beer_wine_and_liquor', 'paints_varnishes_and_supplies', 'parking_lots_garages', 'passenger_railways', 'pawn_shops', 'pet_shops_pet_food_and_supplies', 'petroleum_and_petroleum_products', 'photo_developing', 'photographic_photocopy_microfilm_equipment_and_supplies', 'photographic_studios', 'picture_video_production', 'piece_goods_notions_and_other_dry_goods', 'plumbing_heating_equipment_and_supplies', 'political_organizations', 'postal_services_government_only', 'precious_stones_and_metals_watches_and_jewelry', 'professional_services', 'public_warehousing_and_storage', 'quick_copy_repro_and_blueprint', 'railroads', 'real_estate_agents_and_managers_rentals', 'record_stores', 'recreational_vehicle_rentals', 'religious_goods_stores', 'religious_organizations', 'roofing_siding_sheet_metal', 'secretarial_support_services', 'security_brokers_dealers', 'service_stations', 'sewing_needlework_fabric_and_piece_goods_stores', 'shoe_repair_hat_cleaning', 'shoe_stores', 'small_appliance_repair', 'snowmobile_dealers', 'special_trade_services', 'specialty_cleaning', 'sporting_goods_stores', 'sporting_recreation_camps', 'sports_and_riding_apparel_stores', 'sports_clubs_fields', 'stamp_and_coin_stores', 'stationary_office_supplies_printing_and_writing_paper', 'stationery_stores_office_and_school_supply_stores', 'swimming_pools_sales', 't_ui_travel_germany', 'tailors_alterations', 'tax_payments_government_agencies', 'tax_preparation_services', 'taxicabs_limousines', 'telecommunication_equipment_and_telephone_sales', 'telecommunication_services', 'telegraph_services', 'tent_and_awning_shops', 'testing_laboratories', 'theatrical_ticket_agencies', 'timeshares', 'tire_retreading_and_repair', 'tolls_bridge_fees', 'tourist_attractions_and_exhibits', 'towing_services', 'trailer_parks_campgrounds', 'transportation_services', 'travel_agencies_tour_operators', 'truck_stop_iteration', 'truck_utility_trailer_rentals', 'typesetting_plate_making_and_related_services', 'typewriter_stores', 'u_s_federal_government_agencies_or_departments', 'uniforms_commercial_clothing', 'used_merchandise_and_secondhand_stores', 'utilities', 'variety_stores', 'veterinary_services', 'video_amusement_game_supplies', 'video_game_arcades', 'video_tape_rental_stores', 'vocational_trade_schools', 'watch_jewelry_repair', 'welding_repair', 'wholesale_clubs', 'wig_and_toupee_stores', 'wires_money_orders', 'womens_accessory_and_specialty_shops', 'womens_ready_to_wear_stores', 'wrecking_and_salvage_yards']]"
        ]
        """
        Array of strings containing [categories](https://stripe.com/docs/api#issuing_authorization_object-merchant_data-category) of authorizations to decline. All other categories will be allowed. Cannot be set with `allowed_categories`.
        """
        spending_limits: NotRequired[
            "List[Cardholder.ModifyParamsSpendingControlsSpendingLimit]"
        ]
        """
        Limit spending with amount-based rules that apply across this cardholder's cards.
        """
        spending_limits_currency: NotRequired["str"]
        """
        Currency of amounts within `spending_limits`. Defaults to your merchant country's currency.
        """

    class ModifyParamsSpendingControlsSpendingLimit(TypedDict):
        amount: int
        """
        Maximum amount allowed to spend per interval.
        """
        categories: NotRequired[
            "List[Literal['ac_refrigeration_repair', 'accounting_bookkeeping_services', 'advertising_services', 'agricultural_cooperative', 'airlines_air_carriers', 'airports_flying_fields', 'ambulance_services', 'amusement_parks_carnivals', 'antique_reproductions', 'antique_shops', 'aquariums', 'architectural_surveying_services', 'art_dealers_and_galleries', 'artists_supply_and_craft_shops', 'auto_and_home_supply_stores', 'auto_body_repair_shops', 'auto_paint_shops', 'auto_service_shops', 'automated_cash_disburse', 'automated_fuel_dispensers', 'automobile_associations', 'automotive_parts_and_accessories_stores', 'automotive_tire_stores', 'bail_and_bond_payments', 'bakeries', 'bands_orchestras', 'barber_and_beauty_shops', 'betting_casino_gambling', 'bicycle_shops', 'billiard_pool_establishments', 'boat_dealers', 'boat_rentals_and_leases', 'book_stores', 'books_periodicals_and_newspapers', 'bowling_alleys', 'bus_lines', 'business_secretarial_schools', 'buying_shopping_services', 'cable_satellite_and_other_pay_television_and_radio', 'camera_and_photographic_supply_stores', 'candy_nut_and_confectionery_stores', 'car_and_truck_dealers_new_used', 'car_and_truck_dealers_used_only', 'car_rental_agencies', 'car_washes', 'carpentry_services', 'carpet_upholstery_cleaning', 'caterers', 'charitable_and_social_service_organizations_fundraising', 'chemicals_and_allied_products', 'child_care_services', 'childrens_and_infants_wear_stores', 'chiropodists_podiatrists', 'chiropractors', 'cigar_stores_and_stands', 'civic_social_fraternal_associations', 'cleaning_and_maintenance', 'clothing_rental', 'colleges_universities', 'commercial_equipment', 'commercial_footwear', 'commercial_photography_art_and_graphics', 'commuter_transport_and_ferries', 'computer_network_services', 'computer_programming', 'computer_repair', 'computer_software_stores', 'computers_peripherals_and_software', 'concrete_work_services', 'construction_materials', 'consulting_public_relations', 'correspondence_schools', 'cosmetic_stores', 'counseling_services', 'country_clubs', 'courier_services', 'court_costs', 'credit_reporting_agencies', 'cruise_lines', 'dairy_products_stores', 'dance_hall_studios_schools', 'dating_escort_services', 'dentists_orthodontists', 'department_stores', 'detective_agencies', 'digital_goods_applications', 'digital_goods_games', 'digital_goods_large_volume', 'digital_goods_media', 'direct_marketing_catalog_merchant', 'direct_marketing_combination_catalog_and_retail_merchant', 'direct_marketing_inbound_telemarketing', 'direct_marketing_insurance_services', 'direct_marketing_other', 'direct_marketing_outbound_telemarketing', 'direct_marketing_subscription', 'direct_marketing_travel', 'discount_stores', 'doctors', 'door_to_door_sales', 'drapery_window_covering_and_upholstery_stores', 'drinking_places', 'drug_stores_and_pharmacies', 'drugs_drug_proprietaries_and_druggist_sundries', 'dry_cleaners', 'durable_goods', 'duty_free_stores', 'eating_places_restaurants', 'educational_services', 'electric_razor_stores', 'electric_vehicle_charging', 'electrical_parts_and_equipment', 'electrical_services', 'electronics_repair_shops', 'electronics_stores', 'elementary_secondary_schools', 'emergency_services_gcas_visa_use_only', 'employment_temp_agencies', 'equipment_rental', 'exterminating_services', 'family_clothing_stores', 'fast_food_restaurants', 'financial_institutions', 'fines_government_administrative_entities', 'fireplace_fireplace_screens_and_accessories_stores', 'floor_covering_stores', 'florists', 'florists_supplies_nursery_stock_and_flowers', 'freezer_and_locker_meat_provisioners', 'fuel_dealers_non_automotive', 'funeral_services_crematories', 'furniture_home_furnishings_and_equipment_stores_except_appliances', 'furniture_repair_refinishing', 'furriers_and_fur_shops', 'general_services', 'gift_card_novelty_and_souvenir_shops', 'glass_paint_and_wallpaper_stores', 'glassware_crystal_stores', 'golf_courses_public', 'government_licensed_horse_dog_racing_us_region_only', 'government_licensed_online_casions_online_gambling_us_region_only', 'government_owned_lotteries_non_us_region', 'government_owned_lotteries_us_region_only', 'government_services', 'grocery_stores_supermarkets', 'hardware_equipment_and_supplies', 'hardware_stores', 'health_and_beauty_spas', 'hearing_aids_sales_and_supplies', 'heating_plumbing_a_c', 'hobby_toy_and_game_shops', 'home_supply_warehouse_stores', 'hospitals', 'hotels_motels_and_resorts', 'household_appliance_stores', 'industrial_supplies', 'information_retrieval_services', 'insurance_default', 'insurance_underwriting_premiums', 'intra_company_purchases', 'jewelry_stores_watches_clocks_and_silverware_stores', 'landscaping_services', 'laundries', 'laundry_cleaning_services', 'legal_services_attorneys', 'luggage_and_leather_goods_stores', 'lumber_building_materials_stores', 'manual_cash_disburse', 'marinas_service_and_supplies', 'marketplaces', 'masonry_stonework_and_plaster', 'massage_parlors', 'medical_and_dental_labs', 'medical_dental_ophthalmic_and_hospital_equipment_and_supplies', 'medical_services', 'membership_organizations', 'mens_and_boys_clothing_and_accessories_stores', 'mens_womens_clothing_stores', 'metal_service_centers', 'miscellaneous', 'miscellaneous_apparel_and_accessory_shops', 'miscellaneous_auto_dealers', 'miscellaneous_business_services', 'miscellaneous_food_stores', 'miscellaneous_general_merchandise', 'miscellaneous_general_services', 'miscellaneous_home_furnishing_specialty_stores', 'miscellaneous_publishing_and_printing', 'miscellaneous_recreation_services', 'miscellaneous_repair_shops', 'miscellaneous_specialty_retail', 'mobile_home_dealers', 'motion_picture_theaters', 'motor_freight_carriers_and_trucking', 'motor_homes_dealers', 'motor_vehicle_supplies_and_new_parts', 'motorcycle_shops_and_dealers', 'motorcycle_shops_dealers', 'music_stores_musical_instruments_pianos_and_sheet_music', 'news_dealers_and_newsstands', 'non_fi_money_orders', 'non_fi_stored_value_card_purchase_load', 'nondurable_goods', 'nurseries_lawn_and_garden_supply_stores', 'nursing_personal_care', 'office_and_commercial_furniture', 'opticians_eyeglasses', 'optometrists_ophthalmologist', 'orthopedic_goods_prosthetic_devices', 'osteopaths', 'package_stores_beer_wine_and_liquor', 'paints_varnishes_and_supplies', 'parking_lots_garages', 'passenger_railways', 'pawn_shops', 'pet_shops_pet_food_and_supplies', 'petroleum_and_petroleum_products', 'photo_developing', 'photographic_photocopy_microfilm_equipment_and_supplies', 'photographic_studios', 'picture_video_production', 'piece_goods_notions_and_other_dry_goods', 'plumbing_heating_equipment_and_supplies', 'political_organizations', 'postal_services_government_only', 'precious_stones_and_metals_watches_and_jewelry', 'professional_services', 'public_warehousing_and_storage', 'quick_copy_repro_and_blueprint', 'railroads', 'real_estate_agents_and_managers_rentals', 'record_stores', 'recreational_vehicle_rentals', 'religious_goods_stores', 'religious_organizations', 'roofing_siding_sheet_metal', 'secretarial_support_services', 'security_brokers_dealers', 'service_stations', 'sewing_needlework_fabric_and_piece_goods_stores', 'shoe_repair_hat_cleaning', 'shoe_stores', 'small_appliance_repair', 'snowmobile_dealers', 'special_trade_services', 'specialty_cleaning', 'sporting_goods_stores', 'sporting_recreation_camps', 'sports_and_riding_apparel_stores', 'sports_clubs_fields', 'stamp_and_coin_stores', 'stationary_office_supplies_printing_and_writing_paper', 'stationery_stores_office_and_school_supply_stores', 'swimming_pools_sales', 't_ui_travel_germany', 'tailors_alterations', 'tax_payments_government_agencies', 'tax_preparation_services', 'taxicabs_limousines', 'telecommunication_equipment_and_telephone_sales', 'telecommunication_services', 'telegraph_services', 'tent_and_awning_shops', 'testing_laboratories', 'theatrical_ticket_agencies', 'timeshares', 'tire_retreading_and_repair', 'tolls_bridge_fees', 'tourist_attractions_and_exhibits', 'towing_services', 'trailer_parks_campgrounds', 'transportation_services', 'travel_agencies_tour_operators', 'truck_stop_iteration', 'truck_utility_trailer_rentals', 'typesetting_plate_making_and_related_services', 'typewriter_stores', 'u_s_federal_government_agencies_or_departments', 'uniforms_commercial_clothing', 'used_merchandise_and_secondhand_stores', 'utilities', 'variety_stores', 'veterinary_services', 'video_amusement_game_supplies', 'video_game_arcades', 'video_tape_rental_stores', 'vocational_trade_schools', 'watch_jewelry_repair', 'welding_repair', 'wholesale_clubs', 'wig_and_toupee_stores', 'wires_money_orders', 'womens_accessory_and_specialty_shops', 'womens_ready_to_wear_stores', 'wrecking_and_salvage_yards']]"
        ]
        """
        Array of strings containing [categories](https://stripe.com/docs/api#issuing_authorization_object-merchant_data-category) this limit applies to. Omitting this field will apply the limit to all categories.
        """
        interval: Literal[
            "all_time",
            "daily",
            "monthly",
            "per_authorization",
            "weekly",
            "yearly",
        ]
        """
        Interval (or event) to which the amount applies.
        """

    class ModifyParamsIndividual(TypedDict):
        card_issuing: NotRequired[
            "Cardholder.ModifyParamsIndividualCardIssuing"
        ]
        """
        Information related to the card_issuing program for this cardholder.
        """
        dob: NotRequired["Cardholder.ModifyParamsIndividualDob"]
        """
        The date of birth of this cardholder. Cardholders must be older than 13 years old.
        """
        first_name: NotRequired["str"]
        """
        The first name of this cardholder. Required before activating Cards. This field cannot contain any numbers, special characters (except periods, commas, hyphens, spaces and apostrophes) or non-latin letters.
        """
        last_name: NotRequired["str"]
        """
        The last name of this cardholder. Required before activating Cards. This field cannot contain any numbers, special characters (except periods, commas, hyphens, spaces and apostrophes) or non-latin letters.
        """
        verification: NotRequired[
            "Cardholder.ModifyParamsIndividualVerification"
        ]
        """
        Government-issued ID document for this cardholder.
        """

    class ModifyParamsIndividualVerification(TypedDict):
        document: NotRequired[
            "Cardholder.ModifyParamsIndividualVerificationDocument"
        ]
        """
        An identifying document, either a passport or local ID card.
        """

    class ModifyParamsIndividualVerificationDocument(TypedDict):
        back: NotRequired["str"]
        """
        The back of an ID returned by a [file upload](https://stripe.com/docs/api#create_file) with a `purpose` value of `identity_document`.
        """
        front: NotRequired["str"]
        """
        The front of an ID returned by a [file upload](https://stripe.com/docs/api#create_file) with a `purpose` value of `identity_document`.
        """

    class ModifyParamsIndividualDob(TypedDict):
        day: int
        """
        The day of birth, between 1 and 31.
        """
        month: int
        """
        The month of birth, between 1 and 12.
        """
        year: int
        """
        The four-digit year of birth.
        """

    class ModifyParamsIndividualCardIssuing(TypedDict):
        user_terms_acceptance: NotRequired[
            "Cardholder.ModifyParamsIndividualCardIssuingUserTermsAcceptance"
        ]
        """
        Information about cardholder acceptance of [Authorized User Terms](https://stripe.com/docs/issuing/cards).
        """

    class ModifyParamsIndividualCardIssuingUserTermsAcceptance(TypedDict):
        date: NotRequired["int"]
        """
        The Unix timestamp marking when the cardholder accepted the Authorized User Terms. Required for Celtic Spend Card users.
        """
        ip: NotRequired["str"]
        """
        The IP address from which the cardholder accepted the Authorized User Terms. Required for Celtic Spend Card users.
        """
        user_agent: NotRequired["Literal['']|str"]
        """
        The user agent of the browser from which the cardholder accepted the Authorized User Terms.
        """

    class ModifyParamsCompany(TypedDict):
        tax_id: NotRequired["str"]
        """
        The entity's business ID number.
        """

    class ModifyParamsBilling(TypedDict):
        address: "Cardholder.ModifyParamsBillingAddress"
        """
        The cardholder's billing address.
        """

    class ModifyParamsBillingAddress(TypedDict):
        city: str
        """
        City, district, suburb, town, or village.
        """
        country: str
        """
        Two-letter country code ([ISO 3166-1 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2)).
        """
        line1: str
        """
        Address line 1 (e.g., street, PO Box, or company name).
        """
        line2: NotRequired["str"]
        """
        Address line 2 (e.g., apartment, suite, unit, or building).
        """
        postal_code: str
        """
        ZIP or postal code.
        """
        state: NotRequired["str"]
        """
        State, county, province, or region.
        """

    class RetrieveParams(RequestOptions):
        expand: NotRequired["List[str]"]
        """
        Specifies which fields in the response should be expanded.
        """

    billing: Billing
    company: Optional[Company]
    """
    Additional information about a `company` cardholder.
    """
    created: int
    """
    Time at which the object was created. Measured in seconds since the Unix epoch.
    """
    email: Optional[str]
    """
    The cardholder's email address.
    """
    id: str
    """
    Unique identifier for the object.
    """
    individual: Optional[Individual]
    """
    Additional information about an `individual` cardholder.
    """
    livemode: bool
    """
    Has the value `true` if the object exists in live mode or the value `false` if the object exists in test mode.
    """
    metadata: Dict[str, str]
    """
    Set of [key-value pairs](https://stripe.com/docs/api/metadata) that you can attach to an object. This can be useful for storing additional information about the object in a structured format.
    """
    name: str
    """
    The cardholder's name. This will be printed on cards issued to them.
    """
    object: Literal["issuing.cardholder"]
    """
    String representing the object's type. Objects of the same type share the same value.
    """
    phone_number: Optional[str]
    """
    The cardholder's phone number. This is required for all cardholders who will be creating EU cards. See the [3D Secure documentation](https://stripe.com/docs/issuing/3d-secure#when-is-3d-secure-applied) for more details.
    """
    preferred_locales: Optional[List[Literal["de", "en", "es", "fr", "it"]]]
    """
    The cardholder's preferred locales (languages), ordered by preference. Locales can be `de`, `en`, `es`, `fr`, or `it`.
     This changes the language of the [3D Secure flow](https://stripe.com/docs/issuing/3d-secure) and one-time password messages sent to the cardholder.
    """
    requirements: Requirements
    spending_controls: Optional[SpendingControls]
    """
    Rules that control spending across this cardholder's cards. Refer to our [documentation](https://stripe.com/docs/issuing/controls/spending-controls) for more details.
    """
    status: Literal["active", "blocked", "inactive"]
    """
    Specifies whether to permit authorizations on this cardholder's cards.
    """
    type: Literal["company", "individual"]
    """
    One of `individual` or `company`. See [Choose a cardholder type](https://stripe.com/docs/issuing/other/choose-cardholder) for more details.
    """

    @classmethod
    def create(
        cls,
        api_key: Optional[str] = None,
        idempotency_key: Optional[str] = None,
        stripe_version: Optional[str] = None,
        stripe_account: Optional[str] = None,
        **params: Unpack[
            "Cardholder.CreateParams"
        ]  # pyright: ignore[reportGeneralTypeIssues]
    ) -> "Cardholder":
        """
        Creates a new Issuing Cardholder object that can be issued cards.
        """
        return cast(
            "Cardholder",
            cls._static_request(
                "post",
                cls.class_url(),
                api_key,
                idempotency_key,
                stripe_version,
                stripe_account,
                params,
            ),
        )

    @classmethod
    def list(
        cls,
        api_key: Optional[str] = None,
        stripe_version: Optional[str] = None,
        stripe_account: Optional[str] = None,
        **params: Unpack[
            "Cardholder.ListParams"
        ]  # pyright: ignore[reportGeneralTypeIssues]
    ) -> ListObject["Cardholder"]:
        """
        Returns a list of Issuing Cardholder objects. The objects are sorted in descending order by creation date, with the most recently created object appearing first.
        """
        result = cls._static_request(
            "get",
            cls.class_url(),
            api_key=api_key,
            stripe_version=stripe_version,
            stripe_account=stripe_account,
            params=params,
        )
        if not isinstance(result, ListObject):

            raise TypeError(
                "Expected list object from API, got %s"
                % (type(result).__name__)
            )

        return result

    @classmethod
    def modify(
        cls, id: str, **params: Unpack["Cardholder.ModifyParams"]
    ) -> "Cardholder":
        """
        Updates the specified Issuing Cardholder object by setting the values of the parameters passed. Any parameters not provided will be left unchanged.
        """
        url = "%s/%s" % (cls.class_url(), quote_plus(id))
        return cast(
            "Cardholder",
            cls._static_request("post", url, params=params),
        )

    @classmethod
    def retrieve(
        cls, id: str, **params: Unpack["Cardholder.RetrieveParams"]
    ) -> "Cardholder":
        """
        Retrieves an Issuing Cardholder object.
        """
        instance = cls(id, **params)
        instance.refresh()
        return instance

    _inner_class_types = {
        "billing": Billing,
        "company": Company,
        "individual": Individual,
        "requirements": Requirements,
        "spending_controls": SpendingControls,
    }
