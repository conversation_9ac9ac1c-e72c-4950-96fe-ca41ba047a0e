import React, { useState, useRef, useEffect } from 'react';
import {
  Send,
  Paperclip,
  Mic,
  Smile,
  MoreVertical,
  Reply,
  Edit3,
  Trash2,
  Pin,
  Heart,
  ThumbsUp,
  Laugh,
  Angry,
  Frown,
  Phone,
  Video,
  Share2,
  Users,
  Settings,
  Search,
  Download,
  Eye,
  EyeOff,
  Bot,
  MessageSquare,
  MessageCircle,
  Languages
} from 'lucide-react';
import ChatBot from './ChatBot';
import CannedResponses from './CannedResponses';
import TranslationService from './TranslationService';

const ChatWindow = ({ 
  room, 
  messages, 
  onSendMessage, 
  onTyping,
  onFileUpload,
  onVoiceMessage,
  typingUsers,
  roomMembers,
  pinnedMessages,
  replyToMessage,
  onReply,
  editingMessage,
  onEdit,
  user,
  isConnected,
  messagesEndRef,
  messageInputRef
}) => {
  const [messageText, setMessageText] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showMembersList, setShowMembersList] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState(null);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [showChatBot, setShowChatBot] = useState(false);
  const [showCannedResponses, setShowCannedResponses] = useState(false);
  const [showTranslation, setShowTranslation] = useState(false);
  
  const fileInputRef = useRef(null);
  const recordingIntervalRef = useRef(null);

  useEffect(() => {
    if (editingMessage) {
      setMessageText(editingMessage.content);
      messageInputRef.current?.focus();
    }
  }, [editingMessage]);

  const handleSendMessage = (e) => {
    e.preventDefault();
    
    if (!messageText.trim()) return;

    if (editingMessage) {
      // Handle message editing
      onEdit(null);
      // API call to edit message would go here
    } else {
      // Send new message
      onSendMessage(messageText, {
        reply_to: replyToMessage?.id
      });
    }
    
    setMessageText('');
    onReply(null);
  };

  const handleTyping = (e) => {
    setMessageText(e.target.value);
    onTyping();
  };

  const handleDeleteMessage = (messageId) => {
    if (window.confirm('Are you sure you want to delete this message?')) {
      // Send delete message request
      onSendMessage('', messageId, 'delete');
    }
  };

  const handlePinMessage = (messageId) => {
    // Toggle pin status
    onSendMessage('', messageId, 'pin');
  };

  const getMessageStatus = (message) => {
    if (message.sender?.id !== user?.id) return null;

    switch (message.status) {
      case 'sending':
        return <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse" title="Sending..." />;
      case 'sent':
        return <div className="text-gray-400 text-xs" title="Sent">✓</div>;
      case 'delivered':
        return <div className="text-gray-400 text-xs" title="Delivered">✓✓</div>;
      case 'read':
        return <div className="text-blue-400 text-xs" title="Read">✓✓</div>;
      default:
        return <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse" title="Sending..." />;
    }
  };

  const handleFileSelect = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      onFileUpload(files);
    }
    e.target.value = '';
  };

  const startVoiceRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      const chunks = [];

      mediaRecorder.ondataavailable = (e) => chunks.push(e.data);
      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'audio/webm' });
        onVoiceMessage(blob);
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);
      setRecordingTime(0);

      recordingIntervalRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);

      // Auto-stop after 5 minutes
      setTimeout(() => {
        if (mediaRecorder.state === 'recording') {
          mediaRecorder.stop();
          stopVoiceRecording();
        }
      }, 300000);

    } catch (error) {
      console.error('Failed to start recording:', error);
    }
  };

  const stopVoiceRecording = () => {
    setIsRecording(false);
    setRecordingTime(0);
    if (recordingIntervalRef.current) {
      clearInterval(recordingIntervalRef.current);
    }
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const MessageItem = ({ message }) => {
    const isOwn = message.sender?.id === user?.id;
    const [showReactions, setShowReactions] = useState(false);

    const handleReaction = (emoji) => {
      // API call to add/remove reaction
      console.log('React with:', emoji, 'to message:', message.id);
      setShowReactions(false);
    };

    return (
      <div 
        id={`message-${message.id}`}
        className={`flex gap-3 p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 group ${
          isOwn ? 'flex-row-reverse' : ''
        }`}
      >
        {/* Avatar */}
        {!isOwn && (
          <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {message.sender?.display_name?.[0] || message.sender?.username?.[0] || 'U'}
            </span>
          </div>
        )}

        {/* Message Content */}
        <div className={`flex-1 min-w-0 ${isOwn ? 'text-right' : ''}`}>
          {/* Header */}
          <div className={`flex items-center gap-2 mb-1 ${isOwn ? 'justify-end' : ''}`}>
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {message.sender?.display_name || message.sender?.username}
            </span>
            {message.sender?.is_vip && (
              <span className="text-xs bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 px-2 py-1 rounded-full">
                VIP
              </span>
            )}
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {formatTime(message.created_at)}
            </span>
            {message.is_edited && (
              <span className="text-xs text-gray-400">(edited)</span>
            )}
            {getMessageStatus(message)}
          </div>

          {/* Reply Context */}
          {message.reply_to && (
            <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-2 mb-2 border-l-4 border-blue-500">
              <p className="text-xs text-gray-600 dark:text-gray-400">
                Replying to {message.reply_to.sender?.username}
              </p>
              <p className="text-sm text-gray-800 dark:text-gray-200 truncate">
                {message.reply_to.content}
              </p>
            </div>
          )}

          {/* Message Body */}
          <div className={`${isOwn ? 'bg-blue-500 text-white' : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'} rounded-lg p-3 max-w-md ${isOwn ? 'ml-auto' : ''}`}>
            {message.message_type === 'text' && (
              <p className="text-sm whitespace-pre-wrap break-words">
                {message.content}
              </p>
            )}
            
            {message.message_type === 'file' && (
              <div className="flex items-center gap-2">
                <Paperclip className="w-4 h-4" />
                <a 
                  href={message.file_url} 
                  download
                  className="text-sm underline hover:no-underline"
                >
                  {message.file_name}
                </a>
              </div>
            )}
            
            {message.message_type === 'image' && (
              <div>
                <img 
                  src={message.file_url} 
                  alt="Shared image"
                  className="max-w-full h-auto rounded-lg"
                />
                {message.content && (
                  <p className="text-sm mt-2">{message.content}</p>
                )}
              </div>
            )}
            
            {message.message_type === 'voice' && (
              <div className="flex items-center gap-2">
                <Mic className="w-4 h-4" />
                <audio controls className="max-w-full">
                  <source src={message.file_url} type="audio/webm" />
                </audio>
              </div>
            )}
          </div>

          {/* Reactions */}
          {message.reactions && message.reactions.length > 0 && (
            <div className="flex gap-1 mt-2">
              {message.reactions.map((reaction, index) => (
                <button
                  key={index}
                  onClick={() => handleReaction(reaction.emoji)}
                  className="bg-gray-100 dark:bg-gray-700 rounded-full px-2 py-1 text-xs hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  {reaction.emoji} {reaction.count}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Message Actions */}
        <div className={`opacity-0 group-hover:opacity-100 transition-opacity flex items-start gap-1 ${isOwn ? 'order-first' : ''}`}>
          <button
            onClick={() => setShowReactions(!showReactions)}
            className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            title="React"
          >
            <Smile className="w-4 h-4" />
          </button>
          
          <button
            onClick={() => onReply(message)}
            className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            title="Reply"
          >
            <Reply className="w-4 h-4" />
          </button>
          
          {isOwn && (
            <>
              <button
                onClick={() => onEdit(message)}
                className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                title="Edit"
              >
                <Edit3 className="w-4 h-4" />
              </button>
              
              <button
                onClick={() => handleDeleteMessage(message.id)}
                className="p-1 text-gray-400 hover:text-red-500 transition-colors"
                title="Delete"
              >
                <Trash2 className="w-4 h-4" />
              </button>

              <button
                onClick={() => handlePinMessage(message.id)}
                className={`p-1 transition-colors ${message.is_pinned ? 'text-yellow-500' : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'}`}
                title={message.is_pinned ? 'Unpin' : 'Pin'}
              >
                <Pin className="w-4 h-4" />
              </button>
            </>
          )}
          
          <TranslationService
            message={message}
            onTranslate={(messageId, lang, translation) => {
              console.log('Translated message:', messageId, lang, translation);
            }}
            user={user}
          />

          <button
            onClick={() => setSelectedMessage(selectedMessage === message.id ? null : message.id)}
            className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <MoreVertical className="w-4 h-4" />
          </button>
        </div>

        {/* Quick Reactions */}
        {showReactions && (
          <div className="absolute bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-2 flex gap-1 z-10">
            {['👍', '❤️', '😂', '😮', '😢', '😡'].map((emoji) => (
              <button
                key={emoji}
                onClick={() => handleReaction(emoji)}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
              >
                {emoji}
              </button>
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="flex flex-col h-full bg-white dark:bg-gray-800">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              {room.name}
            </h2>
            {room.is_vip_only && (
              <span className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 px-2 py-1 rounded-full text-xs font-medium">
                VIP
              </span>
            )}
          </div>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {roomMembers.length} members
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => {/* Start voice call */}}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            title="Voice Call"
          >
            <Phone className="w-5 h-5" />
          </button>
          
          <button
            onClick={() => {/* Start video call */}}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            title="Video Call"
          >
            <Video className="w-5 h-5" />
          </button>
          
          <button
            onClick={() => setShowMembersList(!showMembersList)}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            title="Members"
          >
            <Users className="w-5 h-5" />
          </button>
          
          <button
            onClick={() => {/* Show room settings */}}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            title="Room Settings"
          >
            <Settings className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Pinned Messages */}
      {pinnedMessages.length > 0 && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border-b border-yellow-200 dark:border-yellow-800 p-3">
          <div className="flex items-center gap-2 text-yellow-800 dark:text-yellow-200">
            <Pin className="w-4 h-4" />
            <span className="text-sm font-medium">Pinned Messages</span>
          </div>
          <div className="mt-2 space-y-1">
            {pinnedMessages.slice(0, 2).map((message) => (
              <p key={message.id} className="text-sm text-yellow-700 dark:text-yellow-300 truncate">
                {message.sender?.username}: {message.content}
              </p>
            ))}
          </div>
        </div>
      )}

      {/* Messages */}
      <div className="flex-1 overflow-y-auto">
        {messages.length > 0 ? (
          <div>
            {messages.map((message) => (
              <MessageItem key={message.id} message={message} />
            ))}
            
            {/* Typing Indicator */}
            {typingUsers.length > 0 && (
              <div className="p-4 text-sm text-gray-500 dark:text-gray-400">
                {typingUsers.map(u => u.username).join(', ')} {typingUsers.length === 1 ? 'is' : 'are'} typing...
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <MessageCircle className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No messages yet
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                Be the first to send a message in this room!
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Reply Context */}
      {replyToMessage && (
        <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border-t border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Reply className="w-4 h-4 text-blue-600 dark:text-blue-400" />
              <span className="text-sm text-blue-600 dark:text-blue-400">
                Replying to {replyToMessage.sender?.username}
              </span>
            </div>
            <button
              onClick={() => onReply(null)}
              className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
            >
              ×
            </button>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 truncate">
            {replyToMessage.content}
          </p>
        </div>
      )}

      {/* Message Input */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <form onSubmit={handleSendMessage} className="flex items-end gap-2">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                title="Attach File"
              >
                <Paperclip className="w-5 h-5" />
              </button>
              
              <button
                type="button"
                onClick={isRecording ? stopVoiceRecording : startVoiceRecording}
                className={`p-2 transition-colors ${
                  isRecording
                    ? 'text-red-500 hover:text-red-600'
                    : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
                }`}
                title={isRecording ? 'Stop Recording' : 'Voice Message'}
              >
                <Mic className="w-5 h-5" />
              </button>

              <button
                type="button"
                onClick={() => setShowCannedResponses(true)}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                title="Canned Responses"
              >
                <MessageSquare className="w-5 h-5" />
              </button>

              <button
                type="button"
                onClick={() => setShowChatBot(!showChatBot)}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                title="AI Assistant"
              >
                <Bot className="w-5 h-5" />
              </button>
              
              {isRecording && (
                <span className="text-sm text-red-500">
                  Recording... {Math.floor(recordingTime / 60)}:{(recordingTime % 60).toString().padStart(2, '0')}
                </span>
              )}
            </div>
            
            <div className="relative">
              <textarea
                ref={messageInputRef}
                value={messageText}
                onChange={handleTyping}
                placeholder={isConnected ? "Type a message..." : "Connecting..."}
                disabled={!isConnected}
                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                rows={1}
                style={{ minHeight: '44px', maxHeight: '120px' }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage(e);
                  }
                }}
              />
              
              <button
                type="button"
                onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              >
                <Smile className="w-5 h-5" />
              </button>
            </div>
          </div>
          
          <button
            type="submit"
            disabled={!messageText.trim() || !isConnected}
            className="p-3 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
          >
            <Send className="w-5 h-5" />
          </button>
        </form>
        
        <input
          ref={fileInputRef}
          type="file"
          multiple
          onChange={handleFileSelect}
          className="hidden"
          accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt"
        />
      </div>

      {/* ChatBot Component */}
      <ChatBot
        isOpen={showChatBot}
        onToggle={() => setShowChatBot(!showChatBot)}
        user={user}
        room={room}
      />

      {/* Canned Responses Modal */}
      <CannedResponses
        isOpen={showCannedResponses}
        onClose={() => setShowCannedResponses(false)}
        onSelectResponse={(response) => {
          setMessageText(response);
          messageInputRef.current?.focus();
        }}
        user={user}
      />
    </div>
  );
};

export default ChatWindow;
