import React, { useState, useEffect } from 'react';
import { Users, Clock, MessageCircle, Phone, Video, UserCheck, UserX, AlertCircle, CheckCircle, Timer } from 'lucide-react';

const AgentQueue = ({ user }) => {
  const [queue, setQueue] = useState([]);
  const [agents, setAgents] = useState([]);
  const [selectedChat, setSelectedChat] = useState(null);
  const [queueStats, setQueueStats] = useState({
    totalWaiting: 0,
    averageWaitTime: 0,
    activeChats: 0,
    resolvedToday: 0
  });

  // Mock data for demonstration
  const mockQueue = [
    {
      id: 1,
      customer: {
        name: '<PERSON>',
        email: '<EMAIL>',
        avatar: null,
        isVip: false,
        location: 'New York, US',
        device: 'Desktop'
      },
      waitTime: 180, // seconds
      priority: 'normal',
      department: 'support',
      lastMessage: 'I need help with my account',
      timestamp: new Date(Date.now() - 180000),
      tags: ['account', 'login'],
      assignedAgent: null,
      status: 'waiting'
    },
    {
      id: 2,
      customer: {
        name: '<PERSON>',
        email: '<EMAIL>',
        avatar: null,
        isVip: true,
        location: 'London, UK',
        device: 'Mobile'
      },
      waitTime: 45,
      priority: 'high',
      department: 'vip',
      lastMessage: 'VIP support needed for billing issue',
      timestamp: new Date(Date.now() - 45000),
      tags: ['billing', 'vip'],
      assignedAgent: null,
      status: 'waiting'
    },
    {
      id: 3,
      customer: {
        name: 'Mike Johnson',
        email: '<EMAIL>',
        avatar: null,
        isVip: false,
        location: 'Toronto, CA',
        device: 'Tablet'
      },
      waitTime: 320,
      priority: 'normal',
      department: 'technical',
      lastMessage: 'Video call not working properly',
      timestamp: new Date(Date.now() - 320000),
      tags: ['technical', 'video'],
      assignedAgent: 'agent_1',
      status: 'active'
    }
  ];

  const mockAgents = [
    {
      id: 'agent_1',
      name: 'Alice Smith',
      email: '<EMAIL>',
      avatar: null,
      status: 'online',
      department: 'support',
      activeChats: 2,
      maxChats: 5,
      skills: ['general', 'technical'],
      responseTime: 45, // seconds
      satisfaction: 4.8,
      isVipAgent: false
    },
    {
      id: 'agent_2',
      name: 'Bob Wilson',
      email: '<EMAIL>',
      avatar: null,
      status: 'busy',
      department: 'vip',
      activeChats: 3,
      maxChats: 3,
      skills: ['vip', 'billing'],
      responseTime: 30,
      satisfaction: 4.9,
      isVipAgent: true
    },
    {
      id: 'agent_3',
      name: 'Carol Davis',
      email: '<EMAIL>',
      avatar: null,
      status: 'away',
      department: 'technical',
      activeChats: 1,
      maxChats: 4,
      skills: ['technical', 'advanced'],
      responseTime: 60,
      satisfaction: 4.7,
      isVipAgent: false
    }
  ];

  useEffect(() => {
    setQueue(mockQueue);
    setAgents(mockAgents);
    
    // Calculate stats
    const waiting = mockQueue.filter(chat => chat.status === 'waiting').length;
    const active = mockQueue.filter(chat => chat.status === 'active').length;
    const avgWait = mockQueue.reduce((sum, chat) => sum + chat.waitTime, 0) / mockQueue.length;
    
    setQueueStats({
      totalWaiting: waiting,
      averageWaitTime: Math.round(avgWait),
      activeChats: active,
      resolvedToday: 24
    });
  }, []);

  const formatWaitTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      case 'normal':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-300';
      case 'low':
        return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'online':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      case 'busy':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
      case 'away':
        return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
      case 'offline':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const assignAgent = (chatId, agentId) => {
    setQueue(prev => prev.map(chat =>
      chat.id === chatId
        ? { ...chat, assignedAgent: agentId, status: 'active' }
        : chat
    ));
    
    setAgents(prev => prev.map(agent =>
      agent.id === agentId
        ? { ...agent, activeChats: agent.activeChats + 1 }
        : agent
    ));
  };

  const autoAssign = (chatId) => {
    const chat = queue.find(c => c.id === chatId);
    if (!chat) return;

    // Find best available agent
    const availableAgents = agents.filter(agent => 
      agent.status === 'online' && 
      agent.activeChats < agent.maxChats &&
      (chat.department === 'vip' ? agent.isVipAgent : true) &&
      agent.skills.some(skill => chat.tags.includes(skill) || skill === 'general')
    );

    if (availableAgents.length > 0) {
      // Sort by workload and response time
      const bestAgent = availableAgents.sort((a, b) => {
        const aLoad = a.activeChats / a.maxChats;
        const bLoad = b.activeChats / b.maxChats;
        if (aLoad !== bLoad) return aLoad - bLoad;
        return a.responseTime - b.responseTime;
      })[0];

      assignAgent(chatId, bestAgent.id);
    }
  };

  const transferChat = (chatId, newAgentId) => {
    const chat = queue.find(c => c.id === chatId);
    if (!chat || !chat.assignedAgent) return;

    // Remove from old agent
    setAgents(prev => prev.map(agent =>
      agent.id === chat.assignedAgent
        ? { ...agent, activeChats: agent.activeChats - 1 }
        : agent
    ));

    // Assign to new agent
    assignAgent(chatId, newAgentId);
  };

  return (
    <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Agent Queue Management
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Manage chat assignments and monitor queue performance
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
          <div className="flex items-center">
            <Clock className="w-8 h-8 text-blue-600 dark:text-blue-400" />
            <div className="ml-3">
              <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Waiting</p>
              <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">{queueStats.totalWaiting}</p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
          <div className="flex items-center">
            <MessageCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
            <div className="ml-3">
              <p className="text-sm font-medium text-green-600 dark:text-green-400">Active</p>
              <p className="text-2xl font-bold text-green-900 dark:text-green-100">{queueStats.activeChats}</p>
            </div>
          </div>
        </div>

        <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
          <div className="flex items-center">
            <Timer className="w-8 h-8 text-yellow-600 dark:text-yellow-400" />
            <div className="ml-3">
              <p className="text-sm font-medium text-yellow-600 dark:text-yellow-400">Avg Wait</p>
              <p className="text-2xl font-bold text-yellow-900 dark:text-yellow-100">
                {formatWaitTime(queueStats.averageWaitTime)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
          <div className="flex items-center">
            <CheckCircle className="w-8 h-8 text-purple-600 dark:text-purple-400" />
            <div className="ml-3">
              <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Resolved Today</p>
              <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">{queueStats.resolvedToday}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Queue */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Chat Queue
          </h3>
          <div className="space-y-3">
            {queue.map((chat) => (
              <div
                key={chat.id}
                className={`border rounded-lg p-4 transition-colors ${
                  selectedChat === chat.id
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : 'border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50'
                }`}
                onClick={() => setSelectedChat(chat.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {chat.customer.name}
                      </h4>
                      {chat.customer.isVip && (
                        <span className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 px-2 py-1 rounded-full text-xs font-medium">
                          VIP
                        </span>
                      )}
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(chat.priority)}`}>
                        {chat.priority}
                      </span>
                    </div>
                    
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      {chat.lastMessage}
                    </p>
                    
                    <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                      <div className="flex items-center space-x-1">
                        <Clock className="w-3 h-3" />
                        <span>Wait: {formatWaitTime(chat.waitTime)}</span>
                      </div>
                      <span>{chat.customer.location}</span>
                      <span>{chat.customer.device}</span>
                    </div>
                    
                    <div className="flex flex-wrap gap-1 mt-2">
                      {chat.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded-full text-xs"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  <div className="flex flex-col items-end space-y-2">
                    {chat.status === 'waiting' ? (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          autoAssign(chat.id);
                        }}
                        className="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm"
                      >
                        Auto Assign
                      </button>
                    ) : (
                      <span className="text-sm text-green-600 dark:text-green-400">
                        Assigned
                      </span>
                    )}
                    
                    <div className="flex space-x-1">
                      <button className="p-1 text-gray-400 hover:text-blue-600">
                        <MessageCircle className="w-4 h-4" />
                      </button>
                      <button className="p-1 text-gray-400 hover:text-green-600">
                        <Phone className="w-4 h-4" />
                      </button>
                      <button className="p-1 text-gray-400 hover:text-purple-600">
                        <Video className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Agents */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Available Agents
          </h3>
          <div className="space-y-3">
            {agents.map((agent) => (
              <div
                key={agent.id}
                className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {agent.name}
                      </h4>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(agent.status)}`}>
                        {agent.status}
                      </span>
                      {agent.isVipAgent && (
                        <span className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 px-2 py-1 rounded-full text-xs font-medium">
                          VIP Agent
                        </span>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400 mb-2">
                      <span>Department: {agent.department}</span>
                      <span>Chats: {agent.activeChats}/{agent.maxChats}</span>
                    </div>
                    
                    <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400 mb-2">
                      <span>Response: {agent.responseTime}s</span>
                      <span>Rating: {agent.satisfaction}/5.0</span>
                    </div>
                    
                    <div className="flex flex-wrap gap-1">
                      {agent.skills.map((skill, index) => (
                        <span
                          key={index}
                          className="bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full text-xs"
                        >
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  <div className="flex flex-col items-end space-y-2">
                    {selectedChat && (
                      <button
                        onClick={() => {
                          const chat = queue.find(c => c.id === selectedChat);
                          if (chat && chat.status === 'waiting') {
                            assignAgent(selectedChat, agent.id);
                          } else if (chat && chat.assignedAgent && chat.assignedAgent !== agent.id) {
                            transferChat(selectedChat, agent.id);
                          }
                        }}
                        disabled={agent.status !== 'online' || agent.activeChats >= agent.maxChats}
                        className="px-3 py-1 bg-green-500 hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded text-sm"
                      >
                        {(() => {
                          const chat = queue.find(c => c.id === selectedChat);
                          if (!chat) return 'Assign';
                          if (chat.status === 'waiting') return 'Assign';
                          if (chat.assignedAgent === agent.id) return 'Assigned';
                          return 'Transfer';
                        })()}
                      </button>
                    )}
                    
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${(agent.activeChats / agent.maxChats) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgentQueue;
