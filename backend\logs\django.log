INFO 2025-05-31 13:34:38,961 autoreload 5448 14828 Watching for file changes with StatReloader
INFO 2025-05-31 13:34:42,192 basehttp 5448 14076 "GET / HTTP/1.1" 200 10664
WARNING 2025-05-31 13:34:42,358 log 5448 14076 Not Found: /favicon.ico
WARNING 2025-05-31 13:34:42,358 basehttp 5448 14076 "GET /favicon.ico HTTP/1.1" 404 2112
INFO 2025-05-31 13:40:42,561 basehttp 5448 22560 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-05-31 13:40:42,609 basehttp 5448 22560 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4178
INFO 2025-05-31 13:40:42,720 basehttp 5448 11876 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2929
INFO 2025-05-31 13:40:42,721 basehttp 5448 22560 "GET /static/admin/css/base.css HTTP/1.1" 200 21207
INFO 2025-05-31 13:40:42,751 basehttp 5448 11876 "GET /static/admin/js/theme.js HTTP/1.1" 200 1943
INFO 2025-05-31 13:40:42,751 basehttp 5448 22560 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2694
INFO 2025-05-31 13:40:42,759 basehttp 5448 11876 "GET /static/admin/css/login.css HTTP/1.1" 200 958
INFO 2025-05-31 13:40:42,761 basehttp 5448 760 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
INFO 2025-05-31 13:40:42,764 basehttp 5448 17096 "GET /static/admin/css/responsive.css HTTP/1.1" 200 18533
INFO 2025-05-31 13:40:53,205 basehttp 5448 22560 "POST /admin/login/?next=/admin/ HTTP/1.1" 200 4336
INFO 2025-05-31 13:49:20,768 autoreload 5448 14828 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\chat\models.py changed, reloading.
INFO 2025-05-31 13:49:22,985 autoreload 25652 11952 Watching for file changes with StatReloader
INFO 2025-05-31 13:50:09,889 autoreload 25652 11952 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\chat\models.py changed, reloading.
INFO 2025-05-31 13:50:11,220 autoreload 22544 8024 Watching for file changes with StatReloader
INFO 2025-05-31 13:51:00,189 autoreload 22544 8024 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\vip\models.py changed, reloading.
INFO 2025-05-31 13:51:01,605 autoreload 6292 25024 Watching for file changes with StatReloader
INFO 2025-05-31 13:52:03,623 autoreload 6292 25024 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\live\models.py changed, reloading.
INFO 2025-05-31 13:52:05,002 autoreload 22768 17740 Watching for file changes with StatReloader
INFO 2025-05-31 13:53:08,508 autoreload 22768 17740 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\analytics\models.py changed, reloading.
INFO 2025-05-31 13:53:09,604 autoreload 24748 17360 Watching for file changes with StatReloader
INFO 2025-05-31 14:04:42,064 autoreload 23708 5948 Watching for file changes with StatReloader
INFO 2025-05-31 14:04:43,878 basehttp 23708 23820 "GET / HTTP/1.1" 200 10664
WARNING 2025-05-31 14:04:44,252 log 23708 23820 Not Found: /favicon.ico
INFO 2025-05-31 14:04:44,254 basehttp 23708 23820 - Broken pipe from ('127.0.0.1', 27783)
WARNING 2025-05-31 14:07:54,645 log 23708 11452 Not Found: /chat
WARNING 2025-05-31 14:07:54,652 basehttp 23708 11452 "GET /chat HTTP/1.1" 404 2091
WARNING 2025-05-31 14:07:59,641 log 23708 11452 Not Found: /ls
WARNING 2025-05-31 14:07:59,642 basehttp 23708 11452 "GET /ls HTTP/1.1" 404 2085
INFO 2025-05-31 14:08:03,909 basehttp 23708 11452 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-05-31 14:08:03,963 basehttp 23708 11452 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4178
INFO 2025-05-31 14:08:04,004 basehttp 23708 11452 "GET /static/admin/css/base.css HTTP/1.1" 304 0
INFO 2025-05-31 14:08:04,012 basehttp 23708 11476 "GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
INFO 2025-05-31 14:08:04,016 basehttp 23708 17056 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
INFO 2025-05-31 14:08:04,117 basehttp 23708 17056 "GET /static/admin/css/responsive.css HTTP/1.1" 304 0
INFO 2025-05-31 14:08:04,117 basehttp 23708 11476 "GET /static/admin/css/login.css HTTP/1.1" 304 0
INFO 2025-05-31 14:08:04,117 basehttp 23708 11452 "GET /static/admin/js/theme.js HTTP/1.1" 304 0
INFO 2025-05-31 14:08:04,119 basehttp 23708 21188 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
WARNING 2025-05-31 14:08:04,163 log 23708 17056 Not Found: /favicon.ico
INFO 2025-05-31 14:08:04,164 basehttp 23708 17056 - Broken pipe from ('127.0.0.1', 27849)
INFO 2025-05-31 14:08:07,758 basehttp 23708 11452 "POST /admin/login/?next=/admin/ HTTP/1.1" 200 4336
INFO 2025-05-31 14:09:17,058 autoreload 23924 22164 Watching for file changes with StatReloader
INFO 2025-05-31 14:09:25,747 basehttp 23924 13020 "POST /admin/login/?next=/admin/ HTTP/1.1" 200 4336
ERROR 2025-05-31 14:09:51,524 log 23924 13020 Internal Server Error: /admin/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django_redis\cache.py", line 29, in _decorator
    return method(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django_redis\cache.py", line 138, in has_key
    return self.client.has_key(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django_redis\client\default.py", line 670, in has_key
    raise ConnectionInterrupted(connection=client) from e
django_redis.exceptions.ConnectionInterrupted: Redis ConnectionError: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\contrib\admin\sites.py", line 441, in login
    return LoginView.as_view(**defaults)(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\debug.py", line 92, in sensitive_post_parameters_wrapper
    return view(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\contrib\auth\views.py", line 90, in dispatch
    return super().dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\generic\base.py", line 143, in dispatch
    return handler(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\generic\edit.py", line 153, in post
    return self.form_valid(form)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\contrib\auth\views.py", line 109, in form_valid
    auth_login(self.request, form.get_user())
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\contrib\auth\__init__.py", line 118, in login
    request.session.cycle_key()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\contrib\sessions\backends\base.py", line 304, in cycle_key
    self.create()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\contrib\sessions\backends\cache.py", line 42, in create
    self._session_key = self._get_new_session_key()
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\contrib\sessions\backends\base.py", line 150, in _get_new_session_key
    if not self.exists(session_key):
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\contrib\sessions\backends\cache.py", line 73, in exists
    bool(session_key) and (self.cache_key_prefix + session_key) in self._cache
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\cache\backends\base.py", line 299, in __contains__
    return self.has_key(key)
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django_redis\cache.py", line 36, in _decorator
    raise e.__cause__
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django_redis\client\default.py", line 668, in has_key
    return client.exists(key) == 1
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\redis\commands\core.py", line 1736, in exists
    return self.execute_command("EXISTS", *names)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\redis\client.py", line 533, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\redis\connection.py", line 1086, in get_connection
    connection.connect()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\redis\connection.py", line 270, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
ERROR 2025-05-31 14:09:51,544 basehttp 23924 13020 "POST /admin/login/?next=/admin/ HTTP/1.1" 500 207605
INFO 2025-05-31 14:10:33,095 autoreload 13372 8364 Watching for file changes with StatReloader
INFO 2025-05-31 14:11:12,122 basehttp 13372 19504 "GET / HTTP/1.1" 200 10664
WARNING 2025-05-31 14:11:12,255 log 13372 19504 Not Found: /favicon.ico
INFO 2025-05-31 14:11:12,256 basehttp 13372 19504 - Broken pipe from ('127.0.0.1', 27912)
INFO 2025-05-31 14:11:16,316 basehttp 13372 8836 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-05-31 14:11:16,349 basehttp 13372 8836 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4178
ERROR 2025-05-31 14:11:30,191 log 13372 8836 Internal Server Error: /admin/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django_redis\cache.py", line 29, in _decorator
    return method(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django_redis\cache.py", line 138, in has_key
    return self.client.has_key(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django_redis\client\default.py", line 670, in has_key
    raise ConnectionInterrupted(connection=client) from e
django_redis.exceptions.ConnectionInterrupted: Redis ConnectionError: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\contrib\admin\sites.py", line 441, in login
    return LoginView.as_view(**defaults)(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\debug.py", line 92, in sensitive_post_parameters_wrapper
    return view(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\contrib\auth\views.py", line 90, in dispatch
    return super().dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\generic\base.py", line 143, in dispatch
    return handler(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\generic\edit.py", line 153, in post
    return self.form_valid(form)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\contrib\auth\views.py", line 109, in form_valid
    auth_login(self.request, form.get_user())
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\contrib\auth\__init__.py", line 118, in login
    request.session.cycle_key()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\contrib\sessions\backends\base.py", line 304, in cycle_key
    self.create()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\contrib\sessions\backends\cache.py", line 42, in create
    self._session_key = self._get_new_session_key()
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\contrib\sessions\backends\base.py", line 150, in _get_new_session_key
    if not self.exists(session_key):
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\contrib\sessions\backends\cache.py", line 73, in exists
    bool(session_key) and (self.cache_key_prefix + session_key) in self._cache
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\cache\backends\base.py", line 299, in __contains__
    return self.has_key(key)
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django_redis\cache.py", line 36, in _decorator
    raise e.__cause__
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django_redis\client\default.py", line 668, in has_key
    return client.exists(key) == 1
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\redis\commands\core.py", line 1736, in exists
    return self.execute_command("EXISTS", *names)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\redis\client.py", line 533, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\redis\connection.py", line 1086, in get_connection
    connection.connect()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\redis\connection.py", line 270, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
ERROR 2025-05-31 14:11:30,211 basehttp 13372 8836 "POST /admin/login/?next=/admin/ HTTP/1.1" 500 207605
INFO 2025-05-31 14:27:17,151 autoreload 7800 6352 Watching for file changes with StatReloader
INFO 2025-05-31 14:28:10,540 autoreload 9792 7540 Watching for file changes with StatReloader
INFO 2025-05-31 14:38:43,381 autoreload 17904 3588 Watching for file changes with StatReloader
INFO 2025-05-31 14:44:47,563 autoreload 17904 3588 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\chat\apps.py changed, reloading.
INFO 2025-05-31 14:44:48,961 autoreload 13616 7572 Watching for file changes with StatReloader
INFO 2025-05-31 14:45:04,046 autoreload 13616 7572 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\authentication\apps.py changed, reloading.
INFO 2025-05-31 14:45:05,082 autoreload 4008 8300 Watching for file changes with StatReloader
INFO 2025-05-31 14:45:18,729 autoreload 4008 8300 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\vip\apps.py changed, reloading.
INFO 2025-05-31 14:45:19,842 autoreload 12304 17580 Watching for file changes with StatReloader
INFO 2025-05-31 14:45:32,242 autoreload 12304 17580 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\live\apps.py changed, reloading.
INFO 2025-05-31 14:45:33,441 autoreload 7540 16032 Watching for file changes with StatReloader
INFO 2025-05-31 14:45:43,757 autoreload 7540 16032 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\analytics\apps.py changed, reloading.
INFO 2025-05-31 14:45:44,992 autoreload 9628 15852 Watching for file changes with StatReloader
INFO 2025-05-31 14:45:56,615 autoreload 9628 15852 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\support\apps.py changed, reloading.
INFO 2025-05-31 14:45:58,334 autoreload 16524 16528 Watching for file changes with StatReloader
INFO 2025-05-31 14:47:03,831 autoreload 16524 16528 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\chat\views.py changed, reloading.
INFO 2025-05-31 14:47:05,139 autoreload 9072 4476 Watching for file changes with StatReloader
INFO 2025-05-31 14:47:17,567 autoreload 9072 4476 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\chat\views.py changed, reloading.
INFO 2025-05-31 14:47:18,904 autoreload 18304 10688 Watching for file changes with StatReloader
INFO 2025-05-31 14:47:31,432 autoreload 18304 10688 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\chat\views.py changed, reloading.
INFO 2025-05-31 14:47:32,814 autoreload 5396 6996 Watching for file changes with StatReloader
INFO 2025-05-31 14:47:45,254 autoreload 5396 6996 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\chat\views.py changed, reloading.
INFO 2025-05-31 14:47:46,648 autoreload 17432 9832 Watching for file changes with StatReloader
INFO 2025-05-31 14:47:59,158 autoreload 17432 9832 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\chat\views.py changed, reloading.
INFO 2025-05-31 14:48:00,614 autoreload 6132 16508 Watching for file changes with StatReloader
INFO 2025-05-31 14:48:14,303 autoreload 6132 16508 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\chat\views.py changed, reloading.
INFO 2025-05-31 14:48:15,799 autoreload 14720 18288 Watching for file changes with StatReloader
INFO 2025-05-31 14:48:29,402 autoreload 14720 18288 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\chat\views.py changed, reloading.
INFO 2025-05-31 14:48:30,801 autoreload 5740 2496 Watching for file changes with StatReloader
INFO 2025-05-31 14:48:43,407 autoreload 5740 2496 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\chat\views.py changed, reloading.
INFO 2025-05-31 14:48:44,764 autoreload 5372 9408 Watching for file changes with StatReloader
INFO 2025-05-31 14:48:57,211 autoreload 5372 9408 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\chat\views.py changed, reloading.
INFO 2025-05-31 14:48:58,637 autoreload 6272 15652 Watching for file changes with StatReloader
INFO 2025-05-31 14:49:35,806 autoreload 6272 15652 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\vip\views.py changed, reloading.
INFO 2025-05-31 14:49:37,233 autoreload 17080 6412 Watching for file changes with StatReloader
INFO 2025-05-31 15:00:24,754 autoreload 17472 3244 Watching for file changes with StatReloader
WARNING 2025-05-31 15:00:28,995 log 17472 14288 Not Found: /
WARNING 2025-05-31 15:00:28,995 basehttp 17472 14288 "GET / HTTP/1.1" 404 2863
INFO 2025-05-31 15:32:42,107 autoreload 17052 11880 Watching for file changes with StatReloader
WARNING 2025-05-31 15:32:47,875 log 17472 18888 Not Found: /
WARNING 2025-05-31 15:32:47,885 basehttp 17472 18888 "GET / HTTP/1.1" 404 2863
INFO 2025-05-31 15:33:03,429 basehttp 17472 18888 "GET /api/chat HTTP/1.1" 301 0
WARNING 2025-05-31 15:33:03,438 log 17472 19424 Unauthorized: /api/chat/
WARNING 2025-05-31 15:33:03,438 basehttp 17472 19424 "GET /api/chat/ HTTP/1.1" 401 58
WARNING 2025-05-31 15:33:03,503 log 17472 19400 Not Found: /favicon.ico
WARNING 2025-05-31 15:33:03,503 basehttp 17472 19400 "GET /favicon.ico HTTP/1.1" 404 2914
INFO 2025-05-31 15:35:42,445 autoreload 17052 11880 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\chatflow\settings.py changed, reloading.
INFO 2025-05-31 15:35:42,460 autoreload 17472 3244 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\chatflow\settings.py changed, reloading.
INFO 2025-05-31 15:35:43,913 autoreload 13644 11144 Watching for file changes with StatReloader
INFO 2025-05-31 15:35:43,920 autoreload 3116 2480 Watching for file changes with StatReloader
INFO 2025-05-31 15:36:05,115 autoreload 2644 19152 Watching for file changes with StatReloader
INFO 2025-05-31 15:38:02,637 basehttp 13644 11596 "POST /api/auth/register/ HTTP/1.1" 201 790
WARNING 2025-05-31 15:38:02,675 log 13644 1552 Not Found: /ws/chat/
WARNING 2025-05-31 15:38:02,675 basehttp 13644 1552 "GET /ws/chat/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ4NzA1ODgyLCJpYXQiOjE3NDg3MDIyODIsImp0aSI6IjYwODBjNDEzMWVmNDQxYjFiNDdkYTA1YzE1NTMwMmFiIiwidXNlcl9pZCI6IjIzZGU5NmQ5LTMwYzEtNDljZS1hMzQ2LTNlOTZiZGI0YzcyNyJ9.j1QIEaF3mSWxdvzlwheZhLMgKV4ITJ3S6QRpXmmw_is HTTP/1.1" 404 3189
INFO 2025-05-31 15:56:33,355 autoreload 7252 17468 Watching for file changes with StatReloader
WARNING 2025-05-31 15:57:30,539 log 7252 14040 Not Found: /
WARNING 2025-05-31 15:57:30,539 basehttp 7252 14040 "GET / HTTP/1.1" 404 2863
WARNING 2025-05-31 15:57:41,198 log 7252 15368 Unauthorized: /api/auth/login/
WARNING 2025-05-31 15:57:41,199 basehttp 7252 15368 "POST /api/auth/login/ HTTP/1.1" 401 31
WARNING 2025-05-31 16:00:12,701 log 7252 18024 Not Found: /ws/chat/
WARNING 2025-05-31 16:00:12,701 basehttp 7252 18024 "GET /ws/chat/?token=mock_token_1748703461201 HTTP/1.1" 404 2936
WARNING 2025-05-31 16:00:12,987 log 7252 1620 Not Found: /ws/chat/
WARNING 2025-05-31 16:00:12,987 basehttp 7252 1620 "GET /ws/chat/?token=mock_token_1748703461201 HTTP/1.1" 404 2936
WARNING 2025-05-31 16:00:45,879 log 7252 17632 Not Found: /ws/chat/
WARNING 2025-05-31 16:00:45,879 basehttp 7252 17632 "GET /ws/chat/?token=mock_token_1748703461201 HTTP/1.1" 404 2936
WARNING 2025-05-31 16:00:46,459 log 7252 2480 Not Found: /ws/chat/
WARNING 2025-05-31 16:00:46,459 basehttp 7252 2480 "GET /ws/chat/?token=mock_token_1748703461201 HTTP/1.1" 404 2936
WARNING 2025-05-31 16:01:12,660 log 7252 13644 Not Found: /ws/chat/
WARNING 2025-05-31 16:01:12,660 basehttp 7252 13644 "GET /ws/chat/?token=mock_token_1748703461201 HTTP/1.1" 404 2936
WARNING 2025-05-31 16:01:13,921 log 7252 7112 Not Found: /ws/chat/
WARNING 2025-05-31 16:01:13,921 basehttp 7252 7112 "GET /ws/chat/?token=mock_token_1748703461201 HTTP/1.1" 404 2936
WARNING 2025-05-31 16:01:47,616 log 7252 1148 Not Found: /ws/chat/
WARNING 2025-05-31 16:01:47,616 basehttp 7252 1148 "GET /ws/chat/?token=mock_token_1748703461201 HTTP/1.1" 404 2936
WARNING 2025-05-31 16:01:50,461 log 7252 11536 Not Found: /ws/chat/
WARNING 2025-05-31 16:01:50,461 basehttp 7252 11536 "GET /ws/chat/?token=mock_token_1748703461201 HTTP/1.1" 404 2936
WARNING 2025-05-31 16:05:36,815 log 7252 10280 Not Found: /ws/chat/
WARNING 2025-05-31 16:05:36,817 basehttp 7252 10280 "GET /ws/chat/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ4NzA1ODgyLCJpYXQiOjE3NDg3MDIyODIsImp0aSI6IjYwODBjNDEzMWVmNDQxYjFiNDdkYTA1YzE1NTMwMmFiIiwidXNlcl9pZCI6IjIzZGU5NmQ5LTMwYzEtNDljZS1hMzQ2LTNlOTZiZGI0YzcyNyJ9.j1QIEaF3mSWxdvzlwheZhLMgKV4ITJ3S6QRpXmmw_is HTTP/1.1" 404 3189
WARNING 2025-05-31 16:05:37,121 log 7252 17864 Not Found: /ws/chat/
WARNING 2025-05-31 16:05:37,121 basehttp 7252 17864 "GET /ws/chat/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ4NzA1ODgyLCJpYXQiOjE3NDg3MDIyODIsImp0aSI6IjYwODBjNDEzMWVmNDQxYjFiNDdkYTA1YzE1NTMwMmFiIiwidXNlcl9pZCI6IjIzZGU5NmQ5LTMwYzEtNDljZS1hMzQ2LTNlOTZiZGI0YzcyNyJ9.j1QIEaF3mSWxdvzlwheZhLMgKV4ITJ3S6QRpXmmw_is HTTP/1.1" 404 3189
WARNING 2025-05-31 16:05:37,595 log 7252 324 Not Found: /ws/chat/
WARNING 2025-05-31 16:05:37,595 basehttp 7252 324 "GET /ws/chat/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ4NzA1ODgyLCJpYXQiOjE3NDg3MDIyODIsImp0aSI6IjYwODBjNDEzMWVmNDQxYjFiNDdkYTA1YzE1NTMwMmFiIiwidXNlcl9pZCI6IjIzZGU5NmQ5LTMwYzEtNDljZS1hMzQ2LTNlOTZiZGI0YzcyNyJ9.j1QIEaF3mSWxdvzlwheZhLMgKV4ITJ3S6QRpXmmw_is HTTP/1.1" 404 3189
WARNING 2025-05-31 16:05:55,836 log 7252 7884 Not Found: /ws/chat/
WARNING 2025-05-31 16:05:55,836 basehttp 7252 7884 "GET /ws/chat/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ4NzA1ODgyLCJpYXQiOjE3NDg3MDIyODIsImp0aSI6IjYwODBjNDEzMWVmNDQxYjFiNDdkYTA1YzE1NTMwMmFiIiwidXNlcl9pZCI6IjIzZGU5NmQ5LTMwYzEtNDljZS1hMzQ2LTNlOTZiZGI0YzcyNyJ9.j1QIEaF3mSWxdvzlwheZhLMgKV4ITJ3S6QRpXmmw_is HTTP/1.1" 404 3189
WARNING 2025-05-31 16:05:56,863 log 7252 1928 Not Found: /ws/chat/
WARNING 2025-05-31 16:05:56,865 basehttp 7252 1928 "GET /ws/chat/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ4NzA1ODgyLCJpYXQiOjE3NDg3MDIyODIsImp0aSI6IjYwODBjNDEzMWVmNDQxYjFiNDdkYTA1YzE1NTMwMmFiIiwidXNlcl9pZCI6IjIzZGU5NmQ5LTMwYzEtNDljZS1hMzQ2LTNlOTZiZGI0YzcyNyJ9.j1QIEaF3mSWxdvzlwheZhLMgKV4ITJ3S6QRpXmmw_is HTTP/1.1" 404 3189
WARNING 2025-05-31 16:08:26,536 log 7252 7760 Not Found: /ws/chat/
WARNING 2025-05-31 16:08:26,536 basehttp 7252 7760 "GET /ws/chat/?token=mock_token_1748703461201 HTTP/1.1" 404 2936
WARNING 2025-05-31 16:08:26,541 log 7252 3860 Not Found: /ws/chat/
WARNING 2025-05-31 16:08:26,542 basehttp 7252 3860 "GET /ws/chat/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ4NzA1ODgyLCJpYXQiOjE3NDg3MDIyODIsImp0aSI6IjYwODBjNDEzMWVmNDQxYjFiNDdkYTA1YzE1NTMwMmFiIiwidXNlcl9pZCI6IjIzZGU5NmQ5LTMwYzEtNDljZS1hMzQ2LTNlOTZiZGI0YzcyNyJ9.j1QIEaF3mSWxdvzlwheZhLMgKV4ITJ3S6QRpXmmw_is HTTP/1.1" 404 3189
WARNING 2025-05-31 16:08:26,847 log 7252 12512 Not Found: /ws/chat/
WARNING 2025-05-31 16:08:26,847 basehttp 7252 12512 "GET /ws/chat/?token=mock_token_1748703461201 HTTP/1.1" 404 2936
WARNING 2025-05-31 16:08:27,003 log 7252 7308 Not Found: /ws/chat/
WARNING 2025-05-31 16:08:27,003 basehttp 7252 7308 "GET /ws/chat/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ4NzA1ODgyLCJpYXQiOjE3NDg3MDIyODIsImp0aSI6IjYwODBjNDEzMWVmNDQxYjFiNDdkYTA1YzE1NTMwMmFiIiwidXNlcl9pZCI6IjIzZGU5NmQ5LTMwYzEtNDljZS1hMzQ2LTNlOTZiZGI0YzcyNyJ9.j1QIEaF3mSWxdvzlwheZhLMgKV4ITJ3S6QRpXmmw_is HTTP/1.1" 404 3189
WARNING 2025-05-31 16:08:48,533 log 7252 2204 Not Found: /ws/chat/
WARNING 2025-05-31 16:08:48,533 basehttp 7252 2204 "GET /ws/chat/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ4NzA1ODgyLCJpYXQiOjE3NDg3MDIyODIsImp0aSI6IjYwODBjNDEzMWVmNDQxYjFiNDdkYTA1YzE1NTMwMmFiIiwidXNlcl9pZCI6IjIzZGU5NmQ5LTMwYzEtNDljZS1hMzQ2LTNlOTZiZGI0YzcyNyJ9.j1QIEaF3mSWxdvzlwheZhLMgKV4ITJ3S6QRpXmmw_is HTTP/1.1" 404 3189
WARNING 2025-05-31 16:08:48,748 log 7252 9624 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:08:48,750 log 7252 11396 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:08:48,750 basehttp 7252 9624 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 16:08:48,750 basehttp 7252 11396 "GET /api/chat/rooms/ HTTP/1.1" 401 183
ERROR 2025-05-31 16:08:48,944 log 7252 10188 Internal Server Error: /api/chat/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 171, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\pagination.py", line 216, in paginate_queryset
    return list(self.page)
           ^^^^^^^^^^^^^^^
  File "<frozen _collections_abc>", line 993, in __iter__
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\paginator.py", line 185, in __getitem__
    self.object_list = list(self.object_list)
                       ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2321, in prefetch_related_objects
    obj_list, additional_lookups = prefetch_one_level(
                                   ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2528, in prefetch_one_level
    qs = manager._apply_rel_filters(lookup.queryset)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 677, in _apply_rel_filters
    queryset = queryset.filter(**self.core_filters)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1448, in _filter_or_exclude
    raise TypeError("Cannot filter a query once a slice has been taken.")
TypeError: Cannot filter a query once a slice has been taken.
ERROR 2025-05-31 16:08:48,944 log 7252 4932 Internal Server Error: /api/chat/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 171, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\pagination.py", line 216, in paginate_queryset
    return list(self.page)
           ^^^^^^^^^^^^^^^
  File "<frozen _collections_abc>", line 993, in __iter__
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\paginator.py", line 185, in __getitem__
    self.object_list = list(self.object_list)
                       ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2321, in prefetch_related_objects
    obj_list, additional_lookups = prefetch_one_level(
                                   ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2528, in prefetch_one_level
    qs = manager._apply_rel_filters(lookup.queryset)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 677, in _apply_rel_filters
    queryset = queryset.filter(**self.core_filters)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1448, in _filter_or_exclude
    raise TypeError("Cannot filter a query once a slice has been taken.")
TypeError: Cannot filter a query once a slice has been taken.
ERROR 2025-05-31 16:08:48,944 basehttp 7252 10188 "GET /api/chat/rooms/ HTTP/1.1" 500 162918
ERROR 2025-05-31 16:08:48,944 basehttp 7252 4932 "GET /api/chat/rooms/ HTTP/1.1" 500 162918
WARNING 2025-05-31 16:08:50,027 log 7252 6948 Not Found: /ws/chat/
WARNING 2025-05-31 16:08:50,027 basehttp 7252 6948 "GET /ws/chat/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ4NzA1ODgyLCJpYXQiOjE3NDg3MDIyODIsImp0aSI6IjYwODBjNDEzMWVmNDQxYjFiNDdkYTA1YzE1NTMwMmFiIiwidXNlcl9pZCI6IjIzZGU5NmQ5LTMwYzEtNDljZS1hMzQ2LTNlOTZiZGI0YzcyNyJ9.j1QIEaF3mSWxdvzlwheZhLMgKV4ITJ3S6QRpXmmw_is HTTP/1.1" 404 3189
WARNING 2025-05-31 16:08:50,027 log 7252 10280 Not Found: /ws/chat/
WARNING 2025-05-31 16:08:50,027 basehttp 7252 10280 "GET /ws/chat/?token=mock_token_1748703461201 HTTP/1.1" 404 2936
WARNING 2025-05-31 16:08:53,295 log 7252 5688 Not Found: /ws/chat/
WARNING 2025-05-31 16:08:53,295 basehttp 7252 5688 "GET /ws/chat/?token=mock_token_1748703461201 HTTP/1.1" 404 2936
WARNING 2025-05-31 16:09:02,954 log 7252 9624 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:09:02,954 basehttp 7252 9624 "GET /api/chat/rooms/ HTTP/1.1" 401 183
ERROR 2025-05-31 16:09:03,141 log 7252 10188 Internal Server Error: /api/chat/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 171, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\pagination.py", line 216, in paginate_queryset
    return list(self.page)
           ^^^^^^^^^^^^^^^
  File "<frozen _collections_abc>", line 993, in __iter__
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\paginator.py", line 185, in __getitem__
    self.object_list = list(self.object_list)
                       ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2321, in prefetch_related_objects
    obj_list, additional_lookups = prefetch_one_level(
                                   ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2528, in prefetch_one_level
    qs = manager._apply_rel_filters(lookup.queryset)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 677, in _apply_rel_filters
    queryset = queryset.filter(**self.core_filters)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1448, in _filter_or_exclude
    raise TypeError("Cannot filter a query once a slice has been taken.")
TypeError: Cannot filter a query once a slice has been taken.
ERROR 2025-05-31 16:09:03,145 basehttp 7252 10188 "GET /api/chat/rooms/ HTTP/1.1" 500 162918
WARNING 2025-05-31 16:09:34,754 log 7252 11396 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:09:34,754 basehttp 7252 11396 "GET /api/chat/rooms/ HTTP/1.1" 401 183
ERROR 2025-05-31 16:09:34,898 log 7252 4932 Internal Server Error: /api/chat/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 171, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\pagination.py", line 216, in paginate_queryset
    return list(self.page)
           ^^^^^^^^^^^^^^^
  File "<frozen _collections_abc>", line 993, in __iter__
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\paginator.py", line 185, in __getitem__
    self.object_list = list(self.object_list)
                       ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2321, in prefetch_related_objects
    obj_list, additional_lookups = prefetch_one_level(
                                   ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2528, in prefetch_one_level
    qs = manager._apply_rel_filters(lookup.queryset)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 677, in _apply_rel_filters
    queryset = queryset.filter(**self.core_filters)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1448, in _filter_or_exclude
    raise TypeError("Cannot filter a query once a slice has been taken.")
TypeError: Cannot filter a query once a slice has been taken.
ERROR 2025-05-31 16:09:34,898 basehttp 7252 4932 "GET /api/chat/rooms/ HTTP/1.1" 500 162918
WARNING 2025-05-31 16:12:42,497 log 7252 18948 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:12:42,498 basehttp 7252 18948 "GET /api/chat/rooms/ HTTP/1.1" 401 183
ERROR 2025-05-31 16:12:42,858 log 7252 11352 Internal Server Error: /api/chat/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 171, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\pagination.py", line 216, in paginate_queryset
    return list(self.page)
           ^^^^^^^^^^^^^^^
  File "<frozen _collections_abc>", line 993, in __iter__
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\paginator.py", line 185, in __getitem__
    self.object_list = list(self.object_list)
                       ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2321, in prefetch_related_objects
    obj_list, additional_lookups = prefetch_one_level(
                                   ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2528, in prefetch_one_level
    qs = manager._apply_rel_filters(lookup.queryset)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 677, in _apply_rel_filters
    queryset = queryset.filter(**self.core_filters)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1448, in _filter_or_exclude
    raise TypeError("Cannot filter a query once a slice has been taken.")
TypeError: Cannot filter a query once a slice has been taken.
ERROR 2025-05-31 16:12:42,885 basehttp 7252 11352 "GET /api/chat/rooms/ HTTP/1.1" 500 162918
WARNING 2025-05-31 16:12:57,643 log 7252 18948 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:12:57,645 basehttp 7252 18948 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 16:12:57,656 log 7252 16720 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:12:57,657 basehttp 7252 16720 "GET /api/chat/rooms/ HTTP/1.1" 401 183
ERROR 2025-05-31 16:12:57,720 log 7252 11352 Internal Server Error: /api/chat/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 171, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\pagination.py", line 216, in paginate_queryset
    return list(self.page)
           ^^^^^^^^^^^^^^^
  File "<frozen _collections_abc>", line 993, in __iter__
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\paginator.py", line 185, in __getitem__
    self.object_list = list(self.object_list)
                       ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2321, in prefetch_related_objects
    obj_list, additional_lookups = prefetch_one_level(
                                   ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2528, in prefetch_one_level
    qs = manager._apply_rel_filters(lookup.queryset)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 677, in _apply_rel_filters
    queryset = queryset.filter(**self.core_filters)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1448, in _filter_or_exclude
    raise TypeError("Cannot filter a query once a slice has been taken.")
TypeError: Cannot filter a query once a slice has been taken.
ERROR 2025-05-31 16:12:57,743 log 7252 5080 Internal Server Error: /api/chat/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 171, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\pagination.py", line 216, in paginate_queryset
    return list(self.page)
           ^^^^^^^^^^^^^^^
  File "<frozen _collections_abc>", line 993, in __iter__
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\paginator.py", line 185, in __getitem__
    self.object_list = list(self.object_list)
                       ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2321, in prefetch_related_objects
    obj_list, additional_lookups = prefetch_one_level(
                                   ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2528, in prefetch_one_level
    qs = manager._apply_rel_filters(lookup.queryset)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 677, in _apply_rel_filters
    queryset = queryset.filter(**self.core_filters)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1448, in _filter_or_exclude
    raise TypeError("Cannot filter a query once a slice has been taken.")
TypeError: Cannot filter a query once a slice has been taken.
ERROR 2025-05-31 16:12:57,744 basehttp 7252 11352 "GET /api/chat/rooms/ HTTP/1.1" 500 162918
ERROR 2025-05-31 16:12:57,747 basehttp 7252 5080 "GET /api/chat/rooms/ HTTP/1.1" 500 162918
WARNING 2025-05-31 16:13:13,073 log 7252 18948 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:13:13,076 basehttp 7252 18948 "GET /api/chat/rooms/ HTTP/1.1" 401 183
ERROR 2025-05-31 16:13:13,203 log 7252 11352 Internal Server Error: /api/chat/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 171, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\pagination.py", line 216, in paginate_queryset
    return list(self.page)
           ^^^^^^^^^^^^^^^
  File "<frozen _collections_abc>", line 993, in __iter__
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\paginator.py", line 185, in __getitem__
    self.object_list = list(self.object_list)
                       ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2321, in prefetch_related_objects
    obj_list, additional_lookups = prefetch_one_level(
                                   ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2528, in prefetch_one_level
    qs = manager._apply_rel_filters(lookup.queryset)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 677, in _apply_rel_filters
    queryset = queryset.filter(**self.core_filters)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1448, in _filter_or_exclude
    raise TypeError("Cannot filter a query once a slice has been taken.")
TypeError: Cannot filter a query once a slice has been taken.
ERROR 2025-05-31 16:13:13,203 basehttp 7252 11352 "GET /api/chat/rooms/ HTTP/1.1" 500 162918
WARNING 2025-05-31 16:13:27,786 log 7252 16720 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:13:27,786 basehttp 7252 16720 "GET /api/chat/rooms/ HTTP/1.1" 401 183
ERROR 2025-05-31 16:13:27,896 log 7252 5080 Internal Server Error: /api/chat/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 171, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\pagination.py", line 216, in paginate_queryset
    return list(self.page)
           ^^^^^^^^^^^^^^^
  File "<frozen _collections_abc>", line 993, in __iter__
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\paginator.py", line 185, in __getitem__
    self.object_list = list(self.object_list)
                       ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2321, in prefetch_related_objects
    obj_list, additional_lookups = prefetch_one_level(
                                   ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2528, in prefetch_one_level
    qs = manager._apply_rel_filters(lookup.queryset)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 677, in _apply_rel_filters
    queryset = queryset.filter(**self.core_filters)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1448, in _filter_or_exclude
    raise TypeError("Cannot filter a query once a slice has been taken.")
TypeError: Cannot filter a query once a slice has been taken.
ERROR 2025-05-31 16:13:27,902 basehttp 7252 5080 "GET /api/chat/rooms/ HTTP/1.1" 500 162918
WARNING 2025-05-31 16:13:40,257 log 7252 18948 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:13:40,257 basehttp 7252 18948 "GET /api/chat/rooms/ HTTP/1.1" 401 183
ERROR 2025-05-31 16:13:40,366 log 7252 11352 Internal Server Error: /api/chat/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 171, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\pagination.py", line 216, in paginate_queryset
    return list(self.page)
           ^^^^^^^^^^^^^^^
  File "<frozen _collections_abc>", line 993, in __iter__
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\paginator.py", line 185, in __getitem__
    self.object_list = list(self.object_list)
                       ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2321, in prefetch_related_objects
    obj_list, additional_lookups = prefetch_one_level(
                                   ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2528, in prefetch_one_level
    qs = manager._apply_rel_filters(lookup.queryset)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 677, in _apply_rel_filters
    queryset = queryset.filter(**self.core_filters)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1448, in _filter_or_exclude
    raise TypeError("Cannot filter a query once a slice has been taken.")
TypeError: Cannot filter a query once a slice has been taken.
ERROR 2025-05-31 16:13:40,368 basehttp 7252 11352 "GET /api/chat/rooms/ HTTP/1.1" 500 162918
ERROR 2025-05-31 16:13:55,080 log 7252 5080 Internal Server Error: /api/chat/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 171, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\pagination.py", line 216, in paginate_queryset
    return list(self.page)
           ^^^^^^^^^^^^^^^
  File "<frozen _collections_abc>", line 993, in __iter__
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\paginator.py", line 185, in __getitem__
    self.object_list = list(self.object_list)
                       ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2321, in prefetch_related_objects
    obj_list, additional_lookups = prefetch_one_level(
                                   ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2528, in prefetch_one_level
    qs = manager._apply_rel_filters(lookup.queryset)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 677, in _apply_rel_filters
    queryset = queryset.filter(**self.core_filters)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1448, in _filter_or_exclude
    raise TypeError("Cannot filter a query once a slice has been taken.")
TypeError: Cannot filter a query once a slice has been taken.
ERROR 2025-05-31 16:13:55,080 basehttp 7252 5080 "GET /api/chat/rooms/ HTTP/1.1" 500 162918
WARNING 2025-05-31 16:13:55,117 log 7252 16720 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:13:55,118 basehttp 7252 16720 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 16:16:00,262 log 7252 11820 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:16:00,278 basehttp 7252 11820 "GET /api/chat/rooms/ HTTP/1.1" 401 183
ERROR 2025-05-31 16:16:00,362 log 7252 18148 Internal Server Error: /api/chat/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 171, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\pagination.py", line 216, in paginate_queryset
    return list(self.page)
           ^^^^^^^^^^^^^^^
  File "<frozen _collections_abc>", line 993, in __iter__
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\paginator.py", line 185, in __getitem__
    self.object_list = list(self.object_list)
                       ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2321, in prefetch_related_objects
    obj_list, additional_lookups = prefetch_one_level(
                                   ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2528, in prefetch_one_level
    qs = manager._apply_rel_filters(lookup.queryset)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 677, in _apply_rel_filters
    queryset = queryset.filter(**self.core_filters)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1448, in _filter_or_exclude
    raise TypeError("Cannot filter a query once a slice has been taken.")
TypeError: Cannot filter a query once a slice has been taken.
ERROR 2025-05-31 16:16:00,363 basehttp 7252 18148 "GET /api/chat/rooms/ HTTP/1.1" 500 162918
WARNING 2025-05-31 16:16:22,634 log 7252 11820 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:16:22,635 basehttp 7252 11820 "GET /api/chat/rooms/ HTTP/1.1" 401 183
ERROR 2025-05-31 16:16:22,689 log 7252 18148 Internal Server Error: /api/chat/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 171, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\pagination.py", line 216, in paginate_queryset
    return list(self.page)
           ^^^^^^^^^^^^^^^
  File "<frozen _collections_abc>", line 993, in __iter__
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\paginator.py", line 185, in __getitem__
    self.object_list = list(self.object_list)
                       ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2321, in prefetch_related_objects
    obj_list, additional_lookups = prefetch_one_level(
                                   ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2528, in prefetch_one_level
    qs = manager._apply_rel_filters(lookup.queryset)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 677, in _apply_rel_filters
    queryset = queryset.filter(**self.core_filters)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1448, in _filter_or_exclude
    raise TypeError("Cannot filter a query once a slice has been taken.")
TypeError: Cannot filter a query once a slice has been taken.
ERROR 2025-05-31 16:16:22,691 basehttp 7252 18148 "GET /api/chat/rooms/ HTTP/1.1" 500 162918
ERROR 2025-05-31 16:17:07,772 log 7252 18148 Internal Server Error: /api/chat/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 171, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\pagination.py", line 216, in paginate_queryset
    return list(self.page)
           ^^^^^^^^^^^^^^^
  File "<frozen _collections_abc>", line 993, in __iter__
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\paginator.py", line 185, in __getitem__
    self.object_list = list(self.object_list)
                       ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2321, in prefetch_related_objects
    obj_list, additional_lookups = prefetch_one_level(
                                   ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2528, in prefetch_one_level
    qs = manager._apply_rel_filters(lookup.queryset)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 677, in _apply_rel_filters
    queryset = queryset.filter(**self.core_filters)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1448, in _filter_or_exclude
    raise TypeError("Cannot filter a query once a slice has been taken.")
TypeError: Cannot filter a query once a slice has been taken.
WARNING 2025-05-31 16:17:07,772 log 7252 11820 Unauthorized: /api/chat/rooms/
ERROR 2025-05-31 16:17:07,772 basehttp 7252 18148 "GET /api/chat/rooms/ HTTP/1.1" 500 162918
WARNING 2025-05-31 16:17:07,772 basehttp 7252 11820 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 16:17:20,962 log 7252 11820 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:17:20,962 basehttp 7252 11820 "GET /api/chat/rooms/ HTTP/1.1" 401 183
ERROR 2025-05-31 16:17:21,031 log 7252 18148 Internal Server Error: /api/chat/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 171, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\pagination.py", line 216, in paginate_queryset
    return list(self.page)
           ^^^^^^^^^^^^^^^
  File "<frozen _collections_abc>", line 993, in __iter__
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\paginator.py", line 185, in __getitem__
    self.object_list = list(self.object_list)
                       ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2321, in prefetch_related_objects
    obj_list, additional_lookups = prefetch_one_level(
                                   ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2528, in prefetch_one_level
    qs = manager._apply_rel_filters(lookup.queryset)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 677, in _apply_rel_filters
    queryset = queryset.filter(**self.core_filters)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1448, in _filter_or_exclude
    raise TypeError("Cannot filter a query once a slice has been taken.")
TypeError: Cannot filter a query once a slice has been taken.
ERROR 2025-05-31 16:17:21,031 basehttp 7252 18148 "GET /api/chat/rooms/ HTTP/1.1" 500 162918
WARNING 2025-05-31 16:18:17,684 log 7252 13056 Not Found: /ws/chat/
WARNING 2025-05-31 16:18:17,688 basehttp 7252 13056 "GET /ws/chat/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ4NzA1ODgyLCJpYXQiOjE3NDg3MDIyODIsImp0aSI6IjYwODBjNDEzMWVmNDQxYjFiNDdkYTA1YzE1NTMwMmFiIiwidXNlcl9pZCI6IjIzZGU5NmQ5LTMwYzEtNDljZS1hMzQ2LTNlOTZiZGI0YzcyNyJ9.j1QIEaF3mSWxdvzlwheZhLMgKV4ITJ3S6QRpXmmw_is HTTP/1.1" 404 3189
ERROR 2025-05-31 16:18:17,954 log 7252 18148 Internal Server Error: /api/chat/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 171, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\pagination.py", line 216, in paginate_queryset
    return list(self.page)
           ^^^^^^^^^^^^^^^
  File "<frozen _collections_abc>", line 993, in __iter__
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\paginator.py", line 185, in __getitem__
    self.object_list = list(self.object_list)
                       ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2321, in prefetch_related_objects
    obj_list, additional_lookups = prefetch_one_level(
                                   ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2528, in prefetch_one_level
    qs = manager._apply_rel_filters(lookup.queryset)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 677, in _apply_rel_filters
    queryset = queryset.filter(**self.core_filters)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1448, in _filter_or_exclude
    raise TypeError("Cannot filter a query once a slice has been taken.")
TypeError: Cannot filter a query once a slice has been taken.
ERROR 2025-05-31 16:18:17,974 basehttp 7252 18148 "GET /api/chat/rooms/ HTTP/1.1" 500 162918
WARNING 2025-05-31 16:18:17,978 log 7252 11440 Not Found: /ws/chat/
ERROR 2025-05-31 16:18:17,999 log 7252 10272 Internal Server Error: /api/chat/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 171, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\pagination.py", line 216, in paginate_queryset
    return list(self.page)
           ^^^^^^^^^^^^^^^
  File "<frozen _collections_abc>", line 993, in __iter__
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\paginator.py", line 185, in __getitem__
    self.object_list = list(self.object_list)
                       ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2321, in prefetch_related_objects
    obj_list, additional_lookups = prefetch_one_level(
                                   ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2528, in prefetch_one_level
    qs = manager._apply_rel_filters(lookup.queryset)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 677, in _apply_rel_filters
    queryset = queryset.filter(**self.core_filters)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1448, in _filter_or_exclude
    raise TypeError("Cannot filter a query once a slice has been taken.")
TypeError: Cannot filter a query once a slice has been taken.
WARNING 2025-05-31 16:18:18,001 basehttp 7252 11440 "GET /ws/chat/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ4NzA1ODgyLCJpYXQiOjE3NDg3MDIyODIsImp0aSI6IjYwODBjNDEzMWVmNDQxYjFiNDdkYTA1YzE1NTMwMmFiIiwidXNlcl9pZCI6IjIzZGU5NmQ5LTMwYzEtNDljZS1hMzQ2LTNlOTZiZGI0YzcyNyJ9.j1QIEaF3mSWxdvzlwheZhLMgKV4ITJ3S6QRpXmmw_is HTTP/1.1" 404 3189
ERROR 2025-05-31 16:18:18,004 basehttp 7252 10272 "GET /api/chat/rooms/ HTTP/1.1" 500 162918
WARNING 2025-05-31 16:18:55,809 log 7252 18604 Not Found: /ws/chat/
WARNING 2025-05-31 16:18:55,809 basehttp 7252 18604 "GET /ws/chat/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ4NzA1ODgyLCJpYXQiOjE3NDg3MDIyODIsImp0aSI6IjYwODBjNDEzMWVmNDQxYjFiNDdkYTA1YzE1NTMwMmFiIiwidXNlcl9pZCI6IjIzZGU5NmQ5LTMwYzEtNDljZS1hMzQ2LTNlOTZiZGI0YzcyNyJ9.j1QIEaF3mSWxdvzlwheZhLMgKV4ITJ3S6QRpXmmw_is HTTP/1.1" 404 3189
ERROR 2025-05-31 16:18:56,070 log 7252 18148 Internal Server Error: /api/chat/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 171, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\pagination.py", line 216, in paginate_queryset
    return list(self.page)
           ^^^^^^^^^^^^^^^
  File "<frozen _collections_abc>", line 993, in __iter__
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\paginator.py", line 185, in __getitem__
    self.object_list = list(self.object_list)
                       ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2321, in prefetch_related_objects
    obj_list, additional_lookups = prefetch_one_level(
                                   ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2528, in prefetch_one_level
    qs = manager._apply_rel_filters(lookup.queryset)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 677, in _apply_rel_filters
    queryset = queryset.filter(**self.core_filters)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1448, in _filter_or_exclude
    raise TypeError("Cannot filter a query once a slice has been taken.")
TypeError: Cannot filter a query once a slice has been taken.
ERROR 2025-05-31 16:18:56,089 log 7252 10272 Internal Server Error: /api/chat/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 171, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\pagination.py", line 216, in paginate_queryset
    return list(self.page)
           ^^^^^^^^^^^^^^^
  File "<frozen _collections_abc>", line 993, in __iter__
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\paginator.py", line 185, in __getitem__
    self.object_list = list(self.object_list)
                       ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2321, in prefetch_related_objects
    obj_list, additional_lookups = prefetch_one_level(
                                   ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2528, in prefetch_one_level
    qs = manager._apply_rel_filters(lookup.queryset)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 677, in _apply_rel_filters
    queryset = queryset.filter(**self.core_filters)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1448, in _filter_or_exclude
    raise TypeError("Cannot filter a query once a slice has been taken.")
TypeError: Cannot filter a query once a slice has been taken.
ERROR 2025-05-31 16:18:56,098 basehttp 7252 18148 "GET /api/chat/rooms/ HTTP/1.1" 500 162918
ERROR 2025-05-31 16:18:56,105 basehttp 7252 10272 "GET /api/chat/rooms/ HTTP/1.1" 500 162918
WARNING 2025-05-31 16:18:56,336 log 7252 12512 Not Found: /ws/chat/
WARNING 2025-05-31 16:18:56,336 basehttp 7252 12512 "GET /ws/chat/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ4NzA1ODgyLCJpYXQiOjE3NDg3MDIyODIsImp0aSI6IjYwODBjNDEzMWVmNDQxYjFiNDdkYTA1YzE1NTMwMmFiIiwidXNlcl9pZCI6IjIzZGU5NmQ5LTMwYzEtNDljZS1hMzQ2LTNlOTZiZGI0YzcyNyJ9.j1QIEaF3mSWxdvzlwheZhLMgKV4ITJ3S6QRpXmmw_is HTTP/1.1" 404 3189
WARNING 2025-05-31 16:19:00,726 log 7252 12308 Not Found: /ws/chat/
WARNING 2025-05-31 16:19:00,726 basehttp 7252 12308 "GET /ws/chat/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ4NzA1ODgyLCJpYXQiOjE3NDg3MDIyODIsImp0aSI6IjYwODBjNDEzMWVmNDQxYjFiNDdkYTA1YzE1NTMwMmFiIiwidXNlcl9pZCI6IjIzZGU5NmQ5LTMwYzEtNDljZS1hMzQ2LTNlOTZiZGI0YzcyNyJ9.j1QIEaF3mSWxdvzlwheZhLMgKV4ITJ3S6QRpXmmw_is HTTP/1.1" 404 3189
ERROR 2025-05-31 16:19:01,002 log 7252 10272 Internal Server Error: /api/chat/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 171, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\pagination.py", line 216, in paginate_queryset
    return list(self.page)
           ^^^^^^^^^^^^^^^
  File "<frozen _collections_abc>", line 993, in __iter__
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\paginator.py", line 185, in __getitem__
    self.object_list = list(self.object_list)
                       ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2321, in prefetch_related_objects
    obj_list, additional_lookups = prefetch_one_level(
                                   ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2528, in prefetch_one_level
    qs = manager._apply_rel_filters(lookup.queryset)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 677, in _apply_rel_filters
    queryset = queryset.filter(**self.core_filters)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1448, in _filter_or_exclude
    raise TypeError("Cannot filter a query once a slice has been taken.")
TypeError: Cannot filter a query once a slice has been taken.
ERROR 2025-05-31 16:19:01,045 log 7252 18148 Internal Server Error: /api/chat/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 171, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\pagination.py", line 216, in paginate_queryset
    return list(self.page)
           ^^^^^^^^^^^^^^^
  File "<frozen _collections_abc>", line 993, in __iter__
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\paginator.py", line 185, in __getitem__
    self.object_list = list(self.object_list)
                       ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2321, in prefetch_related_objects
    obj_list, additional_lookups = prefetch_one_level(
                                   ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2528, in prefetch_one_level
    qs = manager._apply_rel_filters(lookup.queryset)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 677, in _apply_rel_filters
    queryset = queryset.filter(**self.core_filters)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1448, in _filter_or_exclude
    raise TypeError("Cannot filter a query once a slice has been taken.")
TypeError: Cannot filter a query once a slice has been taken.
ERROR 2025-05-31 16:19:01,047 basehttp 7252 10272 "GET /api/chat/rooms/ HTTP/1.1" 500 162918
ERROR 2025-05-31 16:19:01,052 basehttp 7252 18148 "GET /api/chat/rooms/ HTTP/1.1" 500 162918
WARNING 2025-05-31 16:19:01,849 log 7252 19356 Not Found: /ws/chat/
WARNING 2025-05-31 16:19:01,851 basehttp 7252 19356 "GET /ws/chat/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ4NzA1ODgyLCJpYXQiOjE3NDg3MDIyODIsImp0aSI6IjYwODBjNDEzMWVmNDQxYjFiNDdkYTA1YzE1NTMwMmFiIiwidXNlcl9pZCI6IjIzZGU5NmQ5LTMwYzEtNDljZS1hMzQ2LTNlOTZiZGI0YzcyNyJ9.j1QIEaF3mSWxdvzlwheZhLMgKV4ITJ3S6QRpXmmw_is HTTP/1.1" 404 3189
WARNING 2025-05-31 16:19:11,766 log 7252 6712 Not Found: /ws/chat/
WARNING 2025-05-31 16:19:11,766 basehttp 7252 6712 "GET /ws/chat/?token=mock_token_1748703461201 HTTP/1.1" 404 2936
WARNING 2025-05-31 16:19:11,777 log 7252 11820 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:19:11,780 log 7252 6788 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:19:11,781 basehttp 7252 11820 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 16:19:11,782 basehttp 7252 6788 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 16:19:15,018 log 7252 19144 Not Found: /ws/chat/
WARNING 2025-05-31 16:19:15,019 basehttp 7252 19144 "GET /ws/chat/?token=mock_token_1748703461201 HTTP/1.1" 404 2936
WARNING 2025-05-31 16:19:15,056 log 7252 11820 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:19:15,057 log 7252 6788 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:19:15,058 basehttp 7252 11820 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 16:19:15,058 basehttp 7252 6788 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 16:19:18,907 log 7252 14228 Not Found: /ws/chat/
WARNING 2025-05-31 16:19:18,907 basehttp 7252 14228 "GET /ws/chat/?token=mock_token_1748703461201 HTTP/1.1" 404 2936
WARNING 2025-05-31 16:20:03,009 log 7252 11820 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:20:03,010 basehttp 7252 11820 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 16:21:26,958 log 7252 11820 Unauthorized: /api/auth/login/
WARNING 2025-05-31 16:21:26,958 basehttp 7252 11820 "POST /api/auth/login/ HTTP/1.1" 401 31
WARNING 2025-05-31 16:21:26,980 log 7252 11820 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:21:26,982 log 7252 19292 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:21:26,982 basehttp 7252 11820 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 16:21:26,982 basehttp 7252 19292 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 16:23:14,298 log 7252 11820 Unauthorized: /api/auth/login/
WARNING 2025-05-31 16:23:14,298 basehttp 7252 11820 "POST /api/auth/login/ HTTP/1.1" 401 31
WARNING 2025-05-31 16:23:14,319 log 7252 19292 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:23:14,321 log 7252 11820 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:23:14,321 basehttp 7252 19292 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 16:23:14,322 basehttp 7252 11820 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 16:24:11,872 log 7252 19292 Unauthorized: /api/auth/login/
WARNING 2025-05-31 16:24:11,872 basehttp 7252 19292 "POST /api/auth/login/ HTTP/1.1" 401 31
WARNING 2025-05-31 16:24:11,894 log 7252 11820 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:24:11,898 log 7252 19292 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:24:11,898 basehttp 7252 11820 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 16:24:11,898 basehttp 7252 19292 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 16:28:42,899 log 7252 12512 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:28:42,899 basehttp 7252 12512 "POST /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 16:28:43,941 log 7252 12512 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:28:43,944 basehttp 7252 12512 "POST /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 16:28:44,256 log 7252 12512 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:28:44,256 basehttp 7252 12512 "POST /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 16:28:44,434 log 7252 12512 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:28:44,434 basehttp 7252 12512 "POST /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 16:28:49,068 log 7252 12512 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:28:49,071 basehttp 7252 12512 "POST /api/chat/rooms/ HTTP/1.1" 401 183
INFO 2025-05-31 16:29:13,690 autoreload 17984 9212 Watching for file changes with StatReloader
WARNING 2025-05-31 16:29:17,611 log 17984 17760 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:29:17,611 basehttp 17984 17760 "POST /api/chat/rooms/ HTTP/1.1" 401 183
INFO 2025-05-31 16:30:17,393 autoreload 19232 688 Watching for file changes with StatReloader
WARNING 2025-05-31 16:30:23,052 log 17984 17760 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:30:23,052 basehttp 17984 17760 "POST /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 16:30:30,824 log 17984 17760 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:30:30,824 basehttp 17984 17760 "POST /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 16:30:39,190 log 19232 8288 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 16:30:39,191 basehttp 19232 8288 "POST /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 16:57:12,732 log 19232 4924 Unauthorized: /api/auth/login/
WARNING 2025-05-31 16:57:12,733 basehttp 19232 4924 "POST /api/auth/login/ HTTP/1.1" 401 31
WARNING 2025-05-31 16:57:40,871 log 19232 3556 Unauthorized: /api/auth/login/
WARNING 2025-05-31 16:57:40,871 basehttp 19232 3556 "POST /api/auth/login/ HTTP/1.1" 401 31
WARNING 2025-05-31 17:01:03,469 log 19232 18696 Unauthorized: /api/auth/login/
WARNING 2025-05-31 17:01:03,469 basehttp 19232 18696 "POST /api/auth/login/ HTTP/1.1" 401 31
WARNING 2025-05-31 17:01:03,500 log 19232 18696 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 17:01:03,502 log 19232 15624 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 17:01:03,502 basehttp 19232 18696 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 17:01:03,502 basehttp 19232 15624 "GET /api/chat/rooms/ HTTP/1.1" 401 183
INFO 2025-05-31 17:21:56,457 autoreload 2544 8792 Watching for file changes with StatReloader
WARNING 2025-05-31 17:22:41,192 log 2544 1392 Unauthorized: /api/auth/login/
WARNING 2025-05-31 17:22:41,192 basehttp 2544 1392 "POST /api/auth/login/ HTTP/1.1" 401 31
WARNING 2025-05-31 17:22:41,296 log 2544 1392 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 17:22:41,299 log 2544 9716 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 17:22:41,299 basehttp 2544 1392 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 17:22:41,299 basehttp 2544 9716 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 17:23:31,723 log 2544 1392 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 17:23:31,725 basehttp 2544 1392 "POST /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 17:23:58,413 log 2544 9716 Unauthorized: /api/auth/login/
WARNING 2025-05-31 17:23:58,413 basehttp 2544 9716 "POST /api/auth/login/ HTTP/1.1" 401 31
WARNING 2025-05-31 17:23:58,447 log 2544 9716 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 17:23:58,447 basehttp 2544 9716 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 17:23:58,451 log 2544 9716 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 17:23:58,453 basehttp 2544 9716 "GET /api/chat/rooms/ HTTP/1.1" 401 183
INFO 2025-05-31 17:28:02,859 autoreload 2544 8792 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\authentication\views.py changed, reloading.
INFO 2025-05-31 17:28:04,097 autoreload 17932 5040 Watching for file changes with StatReloader
INFO 2025-05-31 17:28:23,207 autoreload 17932 5040 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\authentication\views.py changed, reloading.
INFO 2025-05-31 17:28:24,493 autoreload 14652 4488 Watching for file changes with StatReloader
WARNING 2025-05-31 17:29:54,906 log 14652 4484 Bad Request: /api/auth/register/
WARNING 2025-05-31 17:29:54,906 basehttp 14652 4484 "POST /api/auth/register/ HTTP/1.1" 400 35
INFO 2025-05-31 17:29:57,489 basehttp 14652 9600 "POST /api/auth/login/ HTTP/1.1" 200 805
WARNING 2025-05-31 17:29:59,509 log 14652 14224 Unauthorized: /api/auth/logout/
WARNING 2025-05-31 17:29:59,509 basehttp 14652 14224 "POST /api/auth/logout/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:30:01,589 log 14652 17316 Bad Request: /api/auth/token/refresh/
WARNING 2025-05-31 17:30:01,591 basehttp 14652 17316 "POST /api/auth/token/refresh/ HTTP/1.1" 400 39
WARNING 2025-05-31 17:30:03,615 log 14652 6880 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 17:30:03,615 basehttp 14652 6880 "GET /api/chat/rooms/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:30:05,646 log 14652 7848 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 17:30:05,647 basehttp 14652 7848 "POST /api/chat/rooms/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:30:07,719 log 14652 11596 Not Found: /api/admin/users/
WARNING 2025-05-31 17:30:07,719 basehttp 14652 11596 "GET /api/admin/users/ HTTP/1.1" 404 5208
WARNING 2025-05-31 17:30:09,779 log 14652 7908 Not Found: /api/admin/rooms/
WARNING 2025-05-31 17:30:09,779 basehttp 14652 7908 "GET /api/admin/rooms/ HTTP/1.1" 404 5208
WARNING 2025-05-31 17:30:11,831 log 14652 2336 Not Found: /api/admin/bots/
WARNING 2025-05-31 17:30:11,831 basehttp 14652 2336 "GET /api/admin/bots/ HTTP/1.1" 404 5205
WARNING 2025-05-31 17:30:13,860 log 14652 19088 Not Found: /api/vip/subscription/
WARNING 2025-05-31 17:30:13,875 basehttp 14652 19088 "GET /api/vip/subscription/ HTTP/1.1" 404 13883
WARNING 2025-05-31 17:30:15,906 log 14652 10412 Unauthorized: /api/vip/subscribe/
WARNING 2025-05-31 17:30:15,906 basehttp 14652 10412 "POST /api/vip/subscribe/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:30:17,961 log 14652 9596 Unauthorized: /api/live/events/
WARNING 2025-05-31 17:30:17,961 basehttp 14652 9596 "GET /api/live/events/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:30:20,013 log 14652 18268 Unauthorized: /api/analytics/dashboard/
WARNING 2025-05-31 17:30:20,013 basehttp 14652 18268 "GET /api/analytics/dashboard/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:30:22,064 log 14652 6716 Unauthorized: /api/support/tickets/
WARNING 2025-05-31 17:30:22,064 basehttp 14652 6716 "GET /api/support/tickets/ HTTP/1.1" 401 58
INFO 2025-05-31 17:30:24,127 basehttp 14652 5080 "GET /api/support/faq/ HTTP/1.1" 200 48
INFO 2025-05-31 17:30:36,158 autoreload 14652 4488 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\chatflow\urls.py changed, reloading.
INFO 2025-05-31 17:30:37,217 autoreload 18552 18320 Watching for file changes with StatReloader
INFO 2025-05-31 17:30:50,003 autoreload 18552 18320 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\chatflow\urls.py changed, reloading.
INFO 2025-05-31 17:30:50,994 autoreload 13672 17636 Watching for file changes with StatReloader
INFO 2025-05-31 17:31:41,402 autoreload 13672 17636 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\authentication\urls.py changed, reloading.
INFO 2025-05-31 17:31:42,504 autoreload 13668 17212 Watching for file changes with StatReloader
INFO 2025-05-31 17:31:52,238 autoreload 13668 17212 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\chat\urls.py changed, reloading.
INFO 2025-05-31 17:31:53,364 autoreload 17136 7956 Watching for file changes with StatReloader
WARNING 2025-05-31 17:32:05,462 log 17136 15276 Bad Request: /api/auth/register/
WARNING 2025-05-31 17:32:05,462 basehttp 17136 15276 "POST /api/auth/register/ HTTP/1.1" 400 35
INFO 2025-05-31 17:32:08,088 basehttp 17136 17652 "POST /api/auth/login/ HTTP/1.1" 200 805
WARNING 2025-05-31 17:32:10,122 log 17136 18036 Unauthorized: /api/auth/logout/
WARNING 2025-05-31 17:32:10,122 basehttp 17136 18036 "POST /api/auth/logout/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:32:12,155 log 17136 12020 Bad Request: /api/auth/token/refresh/
WARNING 2025-05-31 17:32:12,160 basehttp 17136 12020 "POST /api/auth/token/refresh/ HTTP/1.1" 400 39
WARNING 2025-05-31 17:32:14,196 log 17136 18492 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 17:32:14,196 basehttp 17136 18492 "GET /api/chat/rooms/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:32:16,227 log 17136 18616 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 17:32:16,227 basehttp 17136 18616 "POST /api/chat/rooms/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:32:18,267 log 17136 16784 Unauthorized: /api/admin/users/
WARNING 2025-05-31 17:32:18,267 basehttp 17136 16784 "GET /api/admin/users/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:32:20,301 log 17136 11152 Unauthorized: /api/admin/rooms/
WARNING 2025-05-31 17:32:20,301 basehttp 17136 11152 "GET /api/admin/rooms/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:32:22,363 log 17136 6280 Unauthorized: /api/admin/bots/
WARNING 2025-05-31 17:32:22,364 basehttp 17136 6280 "GET /api/admin/bots/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:32:24,444 log 17136 5700 Not Found: /api/vip/subscription/
WARNING 2025-05-31 17:32:24,444 basehttp 17136 5700 "GET /api/vip/subscription/ HTTP/1.1" 404 13883
WARNING 2025-05-31 17:32:26,500 log 17136 18368 Unauthorized: /api/vip/subscribe/
WARNING 2025-05-31 17:32:26,500 basehttp 17136 18368 "POST /api/vip/subscribe/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:32:28,558 log 17136 6524 Unauthorized: /api/live/events/
WARNING 2025-05-31 17:32:28,560 basehttp 17136 6524 "GET /api/live/events/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:32:30,584 log 17136 18884 Unauthorized: /api/analytics/dashboard/
WARNING 2025-05-31 17:32:30,584 basehttp 17136 18884 "GET /api/analytics/dashboard/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:32:32,634 log 17136 2644 Unauthorized: /api/support/tickets/
WARNING 2025-05-31 17:32:32,634 basehttp 17136 2644 "GET /api/support/tickets/ HTTP/1.1" 401 58
INFO 2025-05-31 17:32:34,683 basehttp 17136 6172 "GET /api/support/faq/ HTTP/1.1" 200 48
INFO 2025-05-31 17:33:01,754 autoreload 17136 7956 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\vip\urls.py changed, reloading.
INFO 2025-05-31 17:33:03,931 autoreload 2268 12196 Watching for file changes with StatReloader
WARNING 2025-05-31 17:40:58,559 log 2268 17896 Unauthorized: /api/auth/login/
WARNING 2025-05-31 17:40:58,561 basehttp 2268 17896 "POST /api/auth/login/ HTTP/1.1" 401 31
WARNING 2025-05-31 17:40:58,723 log 2268 17896 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 17:40:58,724 log 2268 17984 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 17:40:58,726 basehttp 2268 17896 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 17:40:58,727 basehttp 2268 17984 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 17:41:26,117 log 2268 17896 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 17:41:26,117 basehttp 2268 17896 "POST /api/chat/rooms/ HTTP/1.1" 401 183
INFO 2025-05-31 17:43:42,400 autoreload 9596 10608 Watching for file changes with StatReloader
INFO 2025-05-31 17:44:08,768 autoreload 18332 3652 Watching for file changes with StatReloader
WARNING 2025-05-31 17:44:13,654 log 18332 4856 Not Found: /
WARNING 2025-05-31 17:44:13,655 basehttp 18332 4856 "GET / HTTP/1.1" 404 3085
INFO 2025-05-31 17:44:18,086 basehttp 18332 4856 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-05-31 17:44:18,191 basehttp 18332 4856 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4178
INFO 2025-05-31 17:44:18,306 basehttp 18332 4856 "GET /static/admin/css/base.css HTTP/1.1" 304 0
INFO 2025-05-31 17:44:18,308 basehttp 18332 4012 "GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
INFO 2025-05-31 17:44:18,313 basehttp 18332 8604 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
INFO 2025-05-31 17:44:18,315 basehttp 18332 7628 "GET /static/admin/css/login.css HTTP/1.1" 304 0
INFO 2025-05-31 17:44:18,315 basehttp 18332 4856 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
INFO 2025-05-31 17:44:18,316 basehttp 18332 3372 "GET /static/admin/css/responsive.css HTTP/1.1" 304 0
INFO 2025-05-31 17:44:18,317 basehttp 18332 16648 "GET /static/admin/js/theme.js HTTP/1.1" 304 0
WARNING 2025-05-31 17:44:18,357 log 18332 4012 Not Found: /favicon.ico
INFO 2025-05-31 17:44:18,357 basehttp 18332 4012 - Broken pipe from ('127.0.0.1', 55867)
INFO 2025-05-31 17:44:28,979 basehttp 18332 4856 "POST /admin/login/?next=/admin/ HTTP/1.1" 200 4347
WARNING 2025-05-31 17:44:29,101 log 18332 8604 Not Found: /favicon.ico
INFO 2025-05-31 17:44:29,101 basehttp 18332 8604 - Broken pipe from ('127.0.0.1', 55868)
INFO 2025-05-31 17:45:10,786 autoreload 18684 6780 Watching for file changes with StatReloader
INFO 2025-05-31 17:45:14,504 basehttp 18684 6856 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
INFO 2025-05-31 17:45:14,587 basehttp 18684 6856 "GET /admin/ HTTP/1.1" 200 5163
INFO 2025-05-31 17:45:14,668 basehttp 18684 6856 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
INFO 2025-05-31 17:45:14,676 basehttp 18684 18688 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
INFO 2025-05-31 17:45:14,676 basehttp 18684 6856 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
WARNING 2025-05-31 17:47:22,503 log 18684 2144 Unauthorized: /api/auth/login/
WARNING 2025-05-31 17:47:22,503 basehttp 18684 2144 "POST /api/auth/login/ HTTP/1.1" 401 31
WARNING 2025-05-31 17:47:22,583 log 18684 2144 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 17:47:22,587 log 18684 3500 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 17:47:22,587 basehttp 18684 2144 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 17:47:22,590 basehttp 18684 3500 "GET /api/chat/rooms/ HTTP/1.1" 401 183
INFO 2025-05-31 17:48:15,844 basehttp 18684 2144 "POST /api/auth/register/ HTTP/1.1" 201 789
WARNING 2025-05-31 17:48:15,874 log 18684 9620 Not Found: /ws/chat/
WARNING 2025-05-31 17:48:15,874 basehttp 18684 9620 "GET /ws/chat/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ4NzEzNjk1LCJpYXQiOjE3NDg3MTAwOTUsImp0aSI6ImQwZjVjNzMyODQ3MzRhMDhhYjFlOGQ1NmVkOGQwNGRkIiwidXNlcl9pZCI6ImNmYTk1NmQyLWZmNmYtNGEwMy1iZDU2LWViMmM4NWMzOGU4NSJ9.liEmQzzpR1oqQQL3pQB32obhSW6tS1WRL5WDOkm7aeE HTTP/1.1" 404 3411
INFO 2025-05-31 17:48:15,892 basehttp 18684 2144 "GET /api/chat/rooms/ HTTP/1.1" 200 52
INFO 2025-05-31 17:48:15,903 basehttp 18684 3500 "GET /api/chat/rooms/ HTTP/1.1" 200 52
ERROR 2025-05-31 17:48:45,817 log 18684 2144 Internal Server Error: /api/chat/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 18, in create
    serializer.is_valid(raise_exception=True)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 227, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 426, in run_validation
    value = self.to_internal_value(data)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 479, in to_internal_value
    for field in fields:
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 362, in _writable_fields
    for field in self.fields.values():
                 ^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 356, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1076, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1222, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1340, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `room_color` is not valid for model `ChatRoom`.
ERROR 2025-05-31 17:48:45,821 basehttp 18684 2144 "POST /api/chat/rooms/ HTTP/1.1" 500 162385
INFO 2025-05-31 17:50:20,848 basehttp 18684 2144 "GET /api/chat/rooms/ HTTP/1.1" 200 52
INFO 2025-05-31 17:50:20,860 basehttp 18684 15568 "GET /api/chat/rooms/ HTTP/1.1" 200 52
INFO 2025-05-31 17:51:18,585 autoreload 18684 6780 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\chat\serializers.py changed, reloading.
INFO 2025-05-31 17:51:19,989 autoreload 13268 11588 Watching for file changes with StatReloader
INFO 2025-05-31 17:51:31,191 basehttp 13268 8020 "POST /api/auth/register/ HTTP/1.1" 201 800
INFO 2025-05-31 17:51:33,793 basehttp 13268 18912 "POST /api/auth/login/ HTTP/1.1" 200 805
WARNING 2025-05-31 17:51:35,826 log 13268 11504 Unauthorized: /api/auth/logout/
WARNING 2025-05-31 17:51:35,826 basehttp 13268 11504 "POST /api/auth/logout/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:51:37,877 log 13268 19252 Bad Request: /api/auth/token/refresh/
WARNING 2025-05-31 17:51:37,893 basehttp 13268 19252 "POST /api/auth/token/refresh/ HTTP/1.1" 400 39
WARNING 2025-05-31 17:51:39,919 log 13268 9228 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 17:51:39,924 basehttp 13268 9228 "GET /api/chat/rooms/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:51:41,949 log 13268 18528 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 17:51:41,949 basehttp 13268 18528 "POST /api/chat/rooms/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:51:43,997 log 13268 19092 Unauthorized: /api/admin/users/
WARNING 2025-05-31 17:51:43,997 basehttp 13268 19092 "GET /api/admin/users/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:51:46,049 log 13268 16720 Unauthorized: /api/admin/rooms/
WARNING 2025-05-31 17:51:46,049 basehttp 13268 16720 "GET /api/admin/rooms/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:51:48,080 log 13268 7268 Unauthorized: /api/admin/bots/
WARNING 2025-05-31 17:51:48,080 basehttp 13268 7268 "GET /api/admin/bots/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:51:50,122 log 13268 14288 Unauthorized: /api/vip/subscription/
WARNING 2025-05-31 17:51:50,122 basehttp 13268 14288 "GET /api/vip/subscription/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:51:52,183 log 13268 14912 Unauthorized: /api/vip/subscribe/
WARNING 2025-05-31 17:51:52,183 basehttp 13268 14912 "POST /api/vip/subscribe/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:51:54,217 log 13268 9184 Unauthorized: /api/live/events/
WARNING 2025-05-31 17:51:54,220 basehttp 13268 9184 "GET /api/live/events/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:51:56,269 log 13268 17588 Unauthorized: /api/analytics/dashboard/
WARNING 2025-05-31 17:51:56,269 basehttp 13268 17588 "GET /api/analytics/dashboard/ HTTP/1.1" 401 58
WARNING 2025-05-31 17:51:58,316 log 13268 19140 Unauthorized: /api/support/tickets/
WARNING 2025-05-31 17:51:58,316 basehttp 13268 19140 "GET /api/support/tickets/ HTTP/1.1" 401 58
INFO 2025-05-31 17:52:00,362 basehttp 13268 5892 "GET /api/support/faq/ HTTP/1.1" 200 48
INFO 2025-05-31 17:52:31,742 basehttp 13268 19252 "POST /api/auth/login/ HTTP/1.1" 200 805
ERROR 2025-05-31 17:52:33,853 log 13268 2444 Internal Server Error: /api/chat/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 18, in create
    serializer.is_valid(raise_exception=True)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 227, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 426, in run_validation
    value = self.to_internal_value(data)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 479, in to_internal_value
    for field in fields:
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 362, in _writable_fields
    for field in self.fields.values():
                 ^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 356, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1076, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1222, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1340, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `room_color` is not valid for model `ChatRoom`.
ERROR 2025-05-31 17:52:33,857 basehttp 13268 2444 "POST /api/chat/rooms/ HTTP/1.1" 500 161835
INFO 2025-05-31 17:52:35,968 basehttp 13268 19292 "GET /api/vip/subscription/ HTTP/1.1" 200 50
WARNING 2025-05-31 17:52:38,003 log 13268 7268 Forbidden: /api/admin/users/
WARNING 2025-05-31 17:52:38,004 basehttp 13268 7268 "GET /api/admin/users/ HTTP/1.1" 403 63
WARNING 2025-05-31 17:52:40,053 log 13268 4788 Bad Request: /api/auth/logout/
WARNING 2025-05-31 17:52:40,054 basehttp 13268 4788 "POST /api/auth/logout/ HTTP/1.1" 400 25
INFO 2025-05-31 17:52:55,528 autoreload 13268 11588 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\chat\serializers.py changed, reloading.
INFO 2025-05-31 17:52:56,781 autoreload 9576 15444 Watching for file changes with StatReloader
INFO 2025-05-31 17:53:08,434 basehttp 9576 4928 "POST /api/auth/login/ HTTP/1.1" 200 805
WARNING 2025-05-31 17:53:10,489 log 9576 7540 Bad Request: /api/chat/rooms/
WARNING 2025-05-31 17:53:10,489 basehttp 9576 7540 "POST /api/chat/rooms/ HTTP/1.1" 400 51
INFO 2025-05-31 17:53:12,554 basehttp 9576 17936 "GET /api/vip/subscription/ HTTP/1.1" 200 50
WARNING 2025-05-31 17:53:14,603 log 9576 14292 Forbidden: /api/admin/users/
WARNING 2025-05-31 17:53:14,606 basehttp 9576 14292 "GET /api/admin/users/ HTTP/1.1" 403 63
WARNING 2025-05-31 17:53:16,649 log 9576 4376 Bad Request: /api/auth/logout/
WARNING 2025-05-31 17:53:16,651 basehttp 9576 4376 "POST /api/auth/logout/ HTTP/1.1" 400 25
INFO 2025-05-31 17:54:53,901 basehttp 9576 18316 "POST /api/auth/login/ HTTP/1.1" 200 805
INFO 2025-05-31 17:54:56,002 basehttp 9576 2628 "POST /api/chat/rooms/ HTTP/1.1" 201 569
ERROR 2025-05-31 17:54:58,452 log 9576 2376 Internal Server Error: /api/chat/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 171, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\pagination.py", line 216, in paginate_queryset
    return list(self.page)
           ^^^^^^^^^^^^^^^
  File "<frozen _collections_abc>", line 993, in __iter__
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\paginator.py", line 185, in __getitem__
    self.object_list = list(self.object_list)
                       ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2321, in prefetch_related_objects
    obj_list, additional_lookups = prefetch_one_level(
                                   ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 2528, in prefetch_one_level
    qs = manager._apply_rel_filters(lookup.queryset)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 677, in _apply_rel_filters
    queryset = queryset.filter(**self.core_filters)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1448, in _filter_or_exclude
    raise TypeError("Cannot filter a query once a slice has been taken.")
TypeError: Cannot filter a query once a slice has been taken.
ERROR 2025-05-31 17:54:58,469 basehttp 9576 2376 "GET /api/chat/rooms/ HTTP/1.1" 500 161621
INFO 2025-05-31 17:55:00,576 basehttp 9576 13504 "GET /api/vip/subscription/ HTTP/1.1" 200 50
WARNING 2025-05-31 17:55:02,619 log 9576 15616 Forbidden: /api/admin/users/
WARNING 2025-05-31 17:55:02,619 basehttp 9576 15616 "GET /api/admin/users/ HTTP/1.1" 403 63
WARNING 2025-05-31 17:55:04,679 log 9576 19344 Bad Request: /api/auth/logout/
WARNING 2025-05-31 17:55:04,684 basehttp 9576 19344 "POST /api/auth/logout/ HTTP/1.1" 400 25
INFO 2025-05-31 17:55:41,787 autoreload 9576 15444 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\chat\views.py changed, reloading.
INFO 2025-05-31 17:55:43,379 autoreload 7628 2392 Watching for file changes with StatReloader
INFO 2025-05-31 17:55:56,478 basehttp 7628 14296 "POST /api/auth/login/ HTTP/1.1" 200 805
INFO 2025-05-31 17:55:58,598 basehttp 7628 15688 "POST /api/chat/rooms/ HTTP/1.1" 201 569
INFO 2025-05-31 17:56:00,676 basehttp 7628 15616 "GET /api/chat/rooms/ HTTP/1.1" 200 1191
WARNING 2025-05-31 17:56:02,738 log 7628 4880 Method Not Allowed: /api/chat/rooms/a3f8fea2-339d-44d9-924f-c1cab911388a/messages/
WARNING 2025-05-31 17:56:02,740 basehttp 7628 4880 "POST /api/chat/rooms/a3f8fea2-339d-44d9-924f-c1cab911388a/messages/ HTTP/1.1" 405 41
INFO 2025-05-31 17:56:04,814 basehttp 7628 7868 "GET /api/vip/subscription/ HTTP/1.1" 200 50
WARNING 2025-05-31 17:56:06,875 log 7628 18004 Forbidden: /api/admin/users/
WARNING 2025-05-31 17:56:06,876 basehttp 7628 18004 "GET /api/admin/users/ HTTP/1.1" 403 63
WARNING 2025-05-31 17:56:08,928 log 7628 19056 Bad Request: /api/auth/logout/
WARNING 2025-05-31 17:56:08,929 basehttp 7628 19056 "POST /api/auth/logout/ HTTP/1.1" 400 25
INFO 2025-05-31 17:56:47,365 basehttp 7628 19404 "POST /api/auth/login/ HTTP/1.1" 200 805
INFO 2025-05-31 17:56:49,431 basehttp 7628 19112 "POST /api/chat/rooms/ HTTP/1.1" 201 569
INFO 2025-05-31 17:56:51,510 basehttp 7628 9540 "GET /api/chat/rooms/ HTTP/1.1" 200 1761
ERROR 2025-05-31 17:56:53,605 log 7628 7192 Internal Server Error: /api/chat/messages/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 18, in create
    serializer.is_valid(raise_exception=True)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 227, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 426, in run_validation
    value = self.to_internal_value(data)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 479, in to_internal_value
    for field in fields:
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 362, in _writable_fields
    for field in self.fields.values():
                 ^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 356, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1076, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1222, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1340, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `file_url` is not valid for model `Message`.
ERROR 2025-05-31 17:56:53,608 basehttp 7628 7192 "POST /api/chat/messages/ HTTP/1.1" 500 160844
INFO 2025-05-31 17:56:55,674 basehttp 7628 4484 "GET /api/vip/subscription/ HTTP/1.1" 200 50
WARNING 2025-05-31 17:56:57,716 log 7628 17932 Forbidden: /api/admin/users/
WARNING 2025-05-31 17:56:57,716 basehttp 7628 17932 "GET /api/admin/users/ HTTP/1.1" 403 63
WARNING 2025-05-31 17:56:59,754 log 7628 18316 Bad Request: /api/auth/logout/
WARNING 2025-05-31 17:56:59,754 basehttp 7628 18316 "POST /api/auth/logout/ HTTP/1.1" 400 25
INFO 2025-05-31 17:57:43,504 autoreload 7628 2392 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\chat\serializers.py changed, reloading.
INFO 2025-05-31 17:57:45,052 autoreload 18336 19120 Watching for file changes with StatReloader
INFO 2025-05-31 17:57:56,460 basehttp 18336 18496 "POST /api/auth/login/ HTTP/1.1" 200 805
INFO 2025-05-31 17:57:58,573 basehttp 18336 8428 "POST /api/chat/rooms/ HTTP/1.1" 201 569
INFO 2025-05-31 17:58:00,661 basehttp 18336 5348 "GET /api/chat/rooms/ HTTP/1.1" 200 2331
INFO 2025-05-31 17:58:02,737 basehttp 18336 8604 "POST /api/chat/messages/ HTTP/1.1" 201 652
INFO 2025-05-31 17:58:04,779 basehttp 18336 18264 "GET /api/vip/subscription/ HTTP/1.1" 200 50
WARNING 2025-05-31 17:58:06,833 log 18336 18224 Forbidden: /api/admin/users/
WARNING 2025-05-31 17:58:06,833 basehttp 18336 18224 "GET /api/admin/users/ HTTP/1.1" 403 63
WARNING 2025-05-31 17:58:08,887 log 18336 6172 Bad Request: /api/auth/logout/
WARNING 2025-05-31 17:58:08,891 basehttp 18336 6172 "POST /api/auth/logout/ HTTP/1.1" 400 25
INFO 2025-05-31 18:07:10,784 autoreload 18832 6084 Watching for file changes with StatReloader
INFO 2025-05-31 18:07:23,425 basehttp 18832 19008 "GET /api/chat/rooms/ HTTP/1.1" 200 2979
INFO 2025-05-31 18:07:23,428 basehttp 18832 18532 "GET /api/chat/rooms/ HTTP/1.1" 200 2979
INFO 2025-05-31 18:07:23,494 basehttp 18832 18532 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:07:23,498 basehttp 18832 19008 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:07:41,870 basehttp 18832 18532 "GET /api/chat/rooms/ HTTP/1.1" 200 2979
INFO 2025-05-31 18:07:41,889 basehttp 18832 19008 "GET /api/chat/rooms/ HTTP/1.1" 200 2979
INFO 2025-05-31 18:07:41,945 basehttp 18832 19008 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:07:41,959 basehttp 18832 18532 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:08:18,269 basehttp 18832 19008 "GET /api/chat/rooms/ HTTP/1.1" 200 2979
INFO 2025-05-31 18:08:18,297 basehttp 18832 18532 "GET /api/chat/rooms/ HTTP/1.1" 200 2979
INFO 2025-05-31 18:08:18,321 basehttp 18832 18532 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:08:18,333 basehttp 18832 19008 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:08:18,376 basehttp 18832 18532 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:08:18,376 basehttp 18832 19008 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:12:08,249 basehttp 18832 18864 "GET /api/chat/rooms/ HTTP/1.1" 200 2979
INFO 2025-05-31 18:12:08,348 basehttp 18832 7660 "GET /api/chat/rooms/ HTTP/1.1" 200 2979
INFO 2025-05-31 18:12:08,351 basehttp 18832 18864 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:12:08,353 basehttp 18832 1616 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:12:08,404 basehttp 18832 18864 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:12:08,414 basehttp 18832 7660 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:12:12,817 basehttp 18832 18864 "GET /api/chat/rooms/f31a8e4d-7c25-4291-a628-b629f5f1005d/messages/ HTTP/1.1" 200 52
INFO 2025-05-31 18:12:12,843 basehttp 18832 7660 "GET /api/chat/rooms/f31a8e4d-7c25-4291-a628-b629f5f1005d/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:12:12,862 basehttp 18832 18864 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:12:12,863 basehttp 18832 1616 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:12:12,898 basehttp 18832 7660 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:12:12,905 basehttp 18832 18864 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:12:13,619 basehttp 18832 7660 "GET /api/chat/rooms/f31a8e4d-7c25-4291-a628-b629f5f1005d/messages/ HTTP/1.1" 200 52
INFO 2025-05-31 18:12:13,649 basehttp 18832 18864 "GET /api/chat/rooms/f31a8e4d-7c25-4291-a628-b629f5f1005d/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:12:13,664 basehttp 18832 1616 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:12:13,684 basehttp 18832 7660 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:12:13,696 basehttp 18832 18864 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:12:13,699 basehttp 18832 7492 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:12:14,552 basehttp 18832 7660 "GET /api/chat/rooms/f31a8e4d-7c25-4291-a628-b629f5f1005d/messages/ HTTP/1.1" 200 52
INFO 2025-05-31 18:12:14,569 basehttp 18832 18864 "GET /api/chat/rooms/f31a8e4d-7c25-4291-a628-b629f5f1005d/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:12:14,602 basehttp 18832 1616 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:12:14,613 basehttp 18832 7660 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:12:14,617 basehttp 18832 7492 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:12:14,618 basehttp 18832 18864 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:12:15,099 basehttp 18832 7660 "GET /api/chat/rooms/a3f8fea2-339d-44d9-924f-c1cab911388a/messages/ HTTP/1.1" 200 52
INFO 2025-05-31 18:12:15,112 basehttp 18832 18864 "GET /api/chat/rooms/a3f8fea2-339d-44d9-924f-c1cab911388a/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:12:15,129 basehttp 18832 1616 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:12:15,136 basehttp 18832 7660 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:12:15,152 basehttp 18832 18864 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:12:15,167 basehttp 18832 7660 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:12:15,712 basehttp 18832 18864 "GET /api/chat/rooms/a3f8fea2-339d-44d9-924f-c1cab911388a/messages/ HTTP/1.1" 200 52
INFO 2025-05-31 18:12:15,718 basehttp 18832 7660 "GET /api/chat/rooms/a3f8fea2-339d-44d9-924f-c1cab911388a/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:12:15,760 basehttp 18832 7660 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:12:15,761 basehttp 18832 18864 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:12:15,810 basehttp 18832 18864 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:12:15,811 basehttp 18832 7660 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:12:16,191 basehttp 18832 18864 "GET /api/chat/rooms/a216b579-70a6-4757-902e-6f8a297a326d/messages/ HTTP/1.1" 200 52
INFO 2025-05-31 18:12:16,218 basehttp 18832 7660 "GET /api/chat/rooms/a216b579-70a6-4757-902e-6f8a297a326d/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:12:16,231 basehttp 18832 18864 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:12:16,238 basehttp 18832 1616 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:12:16,262 basehttp 18832 7660 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:12:16,267 basehttp 18832 18864 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:12:47,435 basehttp 18832 7660 "GET /api/chat/rooms/ HTTP/1.1" 200 2979
INFO 2025-05-31 18:12:47,451 basehttp 18832 18864 "GET /api/chat/rooms/ HTTP/1.1" 200 2979
INFO 2025-05-31 18:12:47,483 basehttp 18832 18864 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:12:47,494 basehttp 18832 7660 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:12:47,530 basehttp 18832 7660 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:12:47,538 basehttp 18832 18864 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
WARNING 2025-05-31 18:13:32,049 log 18832 7660 Bad Request: /api/chat/rooms/
WARNING 2025-05-31 18:13:32,049 basehttp 18832 7660 "POST /api/chat/rooms/ HTTP/1.1" 400 45
WARNING 2025-05-31 18:13:34,087 log 18832 18864 Bad Request: /api/chat/rooms/
WARNING 2025-05-31 18:13:34,087 basehttp 18832 18864 "POST /api/chat/rooms/ HTTP/1.1" 400 45
WARNING 2025-05-31 18:15:47,782 log 18832 16968 Unauthorized: /api/auth/login/
WARNING 2025-05-31 18:15:47,783 basehttp 18832 16968 "POST /api/auth/login/ HTTP/1.1" 401 31
INFO 2025-05-31 18:16:07,195 basehttp 18832 5836 "GET /api/chat/rooms/ HTTP/1.1" 200 2979
INFO 2025-05-31 18:16:07,256 basehttp 18832 11884 "GET /api/chat/rooms/ HTTP/1.1" 200 2979
INFO 2025-05-31 18:16:07,336 basehttp 18832 11884 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:16:07,341 basehttp 18832 5836 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:16:07,386 basehttp 18832 5836 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:16:07,391 basehttp 18832 11884 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
WARNING 2025-05-31 18:16:14,063 log 18832 5836 Bad Request: /api/chat/rooms/
WARNING 2025-05-31 18:16:14,065 basehttp 18832 5836 "POST /api/chat/rooms/ HTTP/1.1" 400 45
WARNING 2025-05-31 18:17:05,965 log 18832 4316 Unauthorized: /api/auth/login/
WARNING 2025-05-31 18:17:05,965 basehttp 18832 4316 "POST /api/auth/login/ HTTP/1.1" 401 31
INFO 2025-05-31 18:18:03,963 basehttp 18832 5836 "GET /api/chat/rooms/ HTTP/1.1" 200 2979
INFO 2025-05-31 18:18:03,967 basehttp 18832 13700 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:18:03,974 basehttp 18832 15980 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:18:04,026 basehttp 18832 15980 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:18:04,046 basehttp 18832 5836 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:18:15,606 basehttp 18832 13700 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:18:15,619 basehttp 18832 15980 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:18:15,645 basehttp 18832 5836 "GET /api/chat/rooms/ HTTP/1.1" 200 2979
INFO 2025-05-31 18:18:15,687 basehttp 18832 15980 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:18:15,694 basehttp 18832 5836 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:24:35,360 basehttp 18832 9168 "GET /api/chat/rooms/ HTTP/1.1" 200 2979
INFO 2025-05-31 18:24:35,385 basehttp 18832 5188 "GET /api/chat/rooms/ HTTP/1.1" 200 2979
INFO 2025-05-31 18:24:35,414 basehttp 18832 5188 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:24:35,426 basehttp 18832 9168 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
INFO 2025-05-31 18:24:35,455 basehttp 18832 9168 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:24:35,471 basehttp 18832 5188 "GET /api/chat/rooms/b75c5469-ce27-42f2-a88f-8c27928bf9fd/messages/ HTTP/1.1" 200 704
WARNING 2025-05-31 18:24:54,109 log 18832 9168 Bad Request: /api/chat/rooms/
WARNING 2025-05-31 18:24:54,113 basehttp 18832 9168 "POST /api/chat/rooms/ HTTP/1.1" 400 45
INFO 2025-05-31 18:25:31,291 autoreload 18832 6084 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\chat\serializers.py changed, reloading.
INFO 2025-05-31 18:25:32,459 autoreload 17828 4988 Watching for file changes with StatReloader
INFO 2025-05-31 18:25:44,412 autoreload 17828 4988 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\chat\serializers.py changed, reloading.
INFO 2025-05-31 18:25:45,679 autoreload 13100 17144 Watching for file changes with StatReloader
INFO 2025-05-31 18:26:49,355 basehttp 13100 6536 "POST /api/chat/rooms/ HTTP/1.1" 201 546
INFO 2025-05-31 18:26:49,420 basehttp 13100 6536 "GET /api/chat/rooms/0ac8bc5a-0170-42ff-bfeb-1374ffd34f52/messages/ HTTP/1.1" 200 52
INFO 2025-05-31 18:26:49,433 basehttp 13100 18124 "GET /api/chat/rooms/0ac8bc5a-0170-42ff-bfeb-1374ffd34f52/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:26:49,451 basehttp 13100 6536 "GET /api/chat/rooms/0ac8bc5a-0170-42ff-bfeb-1374ffd34f52/messages/ HTTP/1.1" 200 52
INFO 2025-05-31 18:26:49,491 basehttp 13100 18124 "GET /api/chat/rooms/0ac8bc5a-0170-42ff-bfeb-1374ffd34f52/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:26:52,693 basehttp 13100 6536 "GET /api/chat/rooms/0ac8bc5a-0170-42ff-bfeb-1374ffd34f52/messages/ HTTP/1.1" 200 52
INFO 2025-05-31 18:26:52,696 basehttp 13100 18124 "GET /api/chat/rooms/0ac8bc5a-0170-42ff-bfeb-1374ffd34f52/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:26:59,497 basehttp 13100 18124 "GET /api/chat/rooms/ HTTP/1.1" 200 3526
INFO 2025-05-31 18:26:59,565 basehttp 13100 18124 "GET /api/chat/rooms/0ac8bc5a-0170-42ff-bfeb-1374ffd34f52/messages/ HTTP/1.1" 200 52
INFO 2025-05-31 18:26:59,577 basehttp 13100 6536 "GET /api/chat/rooms/ HTTP/1.1" 200 3526
INFO 2025-05-31 18:26:59,583 basehttp 13100 13304 "GET /api/chat/rooms/0ac8bc5a-0170-42ff-bfeb-1374ffd34f52/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:26:59,641 basehttp 13100 18124 "GET /api/chat/rooms/0ac8bc5a-0170-42ff-bfeb-1374ffd34f52/messages/ HTTP/1.1" 200 52
INFO 2025-05-31 18:26:59,641 basehttp 13100 13304 "GET /api/chat/rooms/0ac8bc5a-0170-42ff-bfeb-1374ffd34f52/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:27:06,119 basehttp 13100 6536 "GET /api/chat/rooms/0ac8bc5a-0170-42ff-bfeb-1374ffd34f52/messages/ HTTP/1.1" 200 52
INFO 2025-05-31 18:27:06,119 basehttp 13100 18124 "GET /api/chat/rooms/0ac8bc5a-0170-42ff-bfeb-1374ffd34f52/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:32:26,225 autoreload 13100 17144 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\support\models.py changed, reloading.
INFO 2025-05-31 18:32:27,229 autoreload 3776 15728 Watching for file changes with StatReloader
INFO 2025-05-31 18:33:25,756 autoreload 3776 15728 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\support\views.py changed, reloading.
INFO 2025-05-31 18:33:27,290 autoreload 8280 8780 Watching for file changes with StatReloader
INFO 2025-05-31 18:33:51,202 autoreload 8280 8780 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\support\views.py changed, reloading.
INFO 2025-05-31 18:33:52,393 autoreload 6892 13100 Watching for file changes with StatReloader
INFO 2025-05-31 18:34:05,464 autoreload 6892 13100 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\support\urls.py changed, reloading.
INFO 2025-05-31 18:34:06,720 autoreload 15596 5060 Watching for file changes with StatReloader
INFO 2025-05-31 18:34:39,776 basehttp 15596 6712 "GET /api/chat/rooms/ HTTP/1.1" 200 3526
INFO 2025-05-31 18:34:39,778 basehttp 15596 7368 "GET /api/chat/rooms/ HTTP/1.1" 200 3526
INFO 2025-05-31 18:34:39,835 basehttp 15596 6712 "GET /api/chat/rooms/0ac8bc5a-0170-42ff-bfeb-1374ffd34f52/messages/ HTTP/1.1" 200 52
INFO 2025-05-31 18:34:39,836 basehttp 15596 7368 "GET /api/chat/rooms/0ac8bc5a-0170-42ff-bfeb-1374ffd34f52/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:34:55,461 basehttp 15596 6712 "GET /api/chat/rooms/ HTTP/1.1" 200 3526
INFO 2025-05-31 18:34:55,471 basehttp 15596 7368 "GET /api/chat/rooms/ HTTP/1.1" 200 3526
INFO 2025-05-31 18:34:55,537 basehttp 15596 6712 "GET /api/chat/rooms/0ac8bc5a-0170-42ff-bfeb-1374ffd34f52/messages/ HTTP/1.1" 200 52
INFO 2025-05-31 18:34:55,540 basehttp 15596 7368 "GET /api/chat/rooms/0ac8bc5a-0170-42ff-bfeb-1374ffd34f52/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:34:55,599 basehttp 15596 6712 "GET /api/chat/rooms/0ac8bc5a-0170-42ff-bfeb-1374ffd34f52/messages/ HTTP/1.1" 200 52
INFO 2025-05-31 18:34:55,599 basehttp 15596 7368 "GET /api/chat/rooms/0ac8bc5a-0170-42ff-bfeb-1374ffd34f52/members/ HTTP/1.1" 200 2
INFO 2025-05-31 18:36:05,394 autoreload 15596 5060 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\authentication\views.py changed, reloading.
INFO 2025-05-31 18:36:06,669 autoreload 5984 11416 Watching for file changes with StatReloader
INFO 2025-05-31 18:36:16,339 autoreload 5984 11416 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\authentication\views.py changed, reloading.
INFO 2025-05-31 18:36:17,801 autoreload 5200 12224 Watching for file changes with StatReloader
INFO 2025-05-31 18:36:31,593 autoreload 5200 12224 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\authentication\views.py changed, reloading.
INFO 2025-05-31 18:36:33,052 autoreload 3948 10280 Watching for file changes with StatReloader
WARNING 2025-05-31 18:49:20,686 log 3948 4888 Not Found: /
WARNING 2025-05-31 18:49:20,688 basehttp 3948 4888 "GET / HTTP/1.1" 404 3085
WARNING 2025-05-31 18:49:20,792 log 3948 4888 Not Found: /favicon.ico
INFO 2025-05-31 18:49:20,794 basehttp 3948 4888 - Broken pipe from ('127.0.0.1', 57839)
INFO 2025-05-31 18:49:30,087 basehttp 3948 18624 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-05-31 18:49:30,148 basehttp 3948 18624 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4178
INFO 2025-05-31 18:49:30,265 basehttp 3948 18624 "GET /static/admin/css/base.css HTTP/1.1" 304 0
INFO 2025-05-31 18:49:30,269 basehttp 3948 18172 "GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
INFO 2025-05-31 18:49:30,271 basehttp 3948 8332 "GET /static/admin/js/theme.js HTTP/1.1" 304 0
INFO 2025-05-31 18:49:30,271 basehttp 3948 18624 "GET /static/admin/css/responsive.css HTTP/1.1" 304 0
INFO 2025-05-31 18:49:30,273 basehttp 3948 14328 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
INFO 2025-05-31 18:49:30,275 basehttp 3948 8084 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
INFO 2025-05-31 18:49:30,275 basehttp 3948 7836 "GET /static/admin/css/login.css HTTP/1.1" 304 0
INFO 2025-05-31 18:49:39,937 basehttp 3948 18624 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
INFO 2025-05-31 18:49:39,969 basehttp 3948 18624 "GET /admin/ HTTP/1.1" 200 5163
INFO 2025-05-31 18:49:40,021 basehttp 3948 18172 "GET /static/admin/css/dashboard.css HTTP/1.1" 304 0
INFO 2025-05-31 18:49:40,100 basehttp 3948 8332 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
INFO 2025-05-31 18:49:40,100 basehttp 3948 14328 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
INFO 2025-05-31 18:50:18,462 autoreload 4988 17268 Watching for file changes with StatReloader
WARNING 2025-05-31 18:51:25,551 log 4988 7760 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 18:51:25,551 log 4988 4556 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 18:51:25,557 basehttp 4988 7760 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 18:51:25,557 basehttp 4988 4556 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 18:51:58,404 log 4988 7760 Unauthorized: /api/auth/login/
WARNING 2025-05-31 18:51:58,404 basehttp 4988 7760 "POST /api/auth/login/ HTTP/1.1" 401 31
WARNING 2025-05-31 18:51:58,476 log 4988 4556 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 18:51:58,476 log 4988 7760 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 18:51:58,478 basehttp 4988 4556 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 18:51:58,478 basehttp 4988 7760 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 18:52:24,373 log 4988 4556 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 18:52:24,376 basehttp 4988 4556 "POST /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 18:52:35,573 log 4988 7760 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 18:52:35,573 basehttp 4988 7760 "POST /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 18:53:45,090 log 4988 4556 Unauthorized: /api/auth/login/
WARNING 2025-05-31 18:53:45,093 basehttp 4988 4556 "POST /api/auth/login/ HTTP/1.1" 401 31
WARNING 2025-05-31 18:53:45,143 log 4988 7760 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 18:53:45,143 log 4988 4556 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 18:53:45,147 basehttp 4988 4556 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 18:53:45,147 basehttp 4988 7760 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 18:55:05,138 log 4988 7760 Unauthorized: /api/auth/login/
WARNING 2025-05-31 18:55:05,138 basehttp 4988 7760 "POST /api/auth/login/ HTTP/1.1" 401 31
WARNING 2025-05-31 18:55:05,208 log 4988 4556 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 18:55:05,210 log 4988 7760 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 18:55:05,210 basehttp 4988 4556 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 18:55:05,210 basehttp 4988 7760 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 18:55:18,016 log 4988 4556 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 18:55:18,017 basehttp 4988 4556 "POST /api/chat/rooms/ HTTP/1.1" 401 183
INFO 2025-05-31 18:56:34,290 autoreload 4988 17268 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\authentication\views.py changed, reloading.
INFO 2025-05-31 18:56:35,695 autoreload 7556 11396 Watching for file changes with StatReloader
INFO 2025-05-31 18:56:47,102 autoreload 7556 11396 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\authentication\views.py changed, reloading.
INFO 2025-05-31 18:56:48,539 autoreload 2896 17856 Watching for file changes with StatReloader
WARNING 2025-05-31 19:01:24,861 log 2896 5396 Unauthorized: /api/auth/login/
WARNING 2025-05-31 19:01:24,861 basehttp 2896 5396 "POST /api/auth/login/ HTTP/1.1" 401 31
WARNING 2025-05-31 19:02:53,689 log 2896 5396 Unauthorized: /api/auth/login/
WARNING 2025-05-31 19:02:53,689 basehttp 2896 5396 "POST /api/auth/login/ HTTP/1.1" 401 31
WARNING 2025-05-31 19:03:11,936 log 2896 5396 Unauthorized: /api/auth/login/
WARNING 2025-05-31 19:03:11,946 basehttp 2896 5396 "POST /api/auth/login/ HTTP/1.1" 401 31
WARNING 2025-05-31 19:03:27,236 log 2896 5396 Not Found: /api/
WARNING 2025-05-31 19:03:27,236 basehttp 2896 5396 "GET /api/ HTTP/1.1" 404 3115
WARNING 2025-05-31 19:03:33,419 log 2896 5396 Not Found: /api/auth/
WARNING 2025-05-31 19:03:33,421 basehttp 2896 5396 "GET /api/auth/ HTTP/1.1" 404 4324
WARNING 2025-05-31 19:03:41,264 log 2896 5396 Unauthorized: /api/auth/login/
WARNING 2025-05-31 19:03:41,264 basehttp 2896 5396 "POST /api/auth/login/ HTTP/1.1" 401 31
WARNING 2025-05-31 19:04:09,782 log 2896 5396 Unauthorized: /api/auth/login/
WARNING 2025-05-31 19:04:09,811 basehttp 2896 5396 "POST /api/auth/login/ HTTP/1.1" 401 31
INFO 2025-05-31 19:08:01,979 autoreload 2896 17856 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\authentication\views.py changed, reloading.
INFO 2025-05-31 19:08:03,324 autoreload 5212 16368 Watching for file changes with StatReloader
WARNING 2025-05-31 19:08:15,097 log 18028 18664 Unauthorized: /api/auth/login/
WARNING 2025-05-31 19:08:15,099 basehttp 18028 18664 "POST /api/auth/login/ HTTP/1.1" 401 31
INFO 2025-05-31 19:08:22,961 basehttp 18028 18664 "POST /api/auth/login/ HTTP/1.1" 200 815
INFO 2025-05-31 19:08:31,975 basehttp 18028 18664 "POST /api/auth/login/ HTTP/1.1" 200 816
WARNING 2025-05-31 19:08:40,373 log 18028 18664 Unauthorized: /api/auth/login/
WARNING 2025-05-31 19:08:40,373 basehttp 18028 18664 "POST /api/auth/login/ HTTP/1.1" 401 31
INFO 2025-05-31 19:09:06,655 autoreload 5212 16368 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\authentication\views.py changed, reloading.
INFO 2025-05-31 19:09:08,086 autoreload 18180 8780 Watching for file changes with StatReloader
WARNING 2025-05-31 19:09:16,480 log 18028 18664 Unauthorized: /api/auth/login/
WARNING 2025-05-31 19:09:16,481 basehttp 18028 18664 "POST /api/auth/login/ HTTP/1.1" 401 31
INFO 2025-05-31 19:09:31,110 autoreload 18180 8780 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\authentication\views.py changed, reloading.
INFO 2025-05-31 19:09:32,479 autoreload 18992 180 Watching for file changes with StatReloader
WARNING 2025-05-31 19:09:41,197 log 18028 18664 Unauthorized: /api/auth/login/
WARNING 2025-05-31 19:09:41,197 basehttp 18028 18664 "POST /api/auth/login/ HTTP/1.1" 401 31
INFO 2025-05-31 19:10:31,986 basehttp 18992 5028 "POST /api/auth/login/ HTTP/1.1" 200 816
INFO 2025-05-31 19:10:51,634 autoreload 18992 180 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\authentication\views.py changed, reloading.
INFO 2025-05-31 19:10:53,047 autoreload 17932 17144 Watching for file changes with StatReloader
INFO 2025-05-31 19:11:01,448 basehttp 10420 16180 "POST /api/auth/register/ HTTP/1.1" 201 826
INFO 2025-05-31 19:11:09,369 basehttp 10420 16180 "POST /api/auth/login/ HTTP/1.1" 200 831
INFO 2025-05-31 19:11:18,436 basehttp 10420 16180 "POST /api/auth/login/ HTTP/1.1" 200 831
INFO 2025-05-31 19:11:30,864 basehttp 10420 16180 "POST /api/auth/login/ HTTP/1.1" 200 831
INFO 2025-05-31 19:11:41,338 basehttp 10420 16180 "POST /api/auth/token/refresh/ HTTP/1.1" 200 582
INFO 2025-05-31 19:11:51,876 basehttp 10420 16180 "GET /api/auth/profile/ HTTP/1.1" 200 259
INFO 2025-05-31 19:12:02,004 basehttp 10420 16180 "GET /api/chat/rooms/ HTTP/1.1" 200 3526
INFO 2025-05-31 19:12:12,843 basehttp 10420 16180 "POST /api/chat/rooms/ HTTP/1.1" 201 565
INFO 2025-05-31 19:12:22,605 basehttp 10420 16180 "GET /api/support/tickets/ HTTP/1.1" 200 52
INFO 2025-05-31 19:12:33,717 basehttp 10420 16180 "POST /api/support/tickets/ HTTP/1.1" 201 586
WARNING 2025-05-31 19:12:41,591 log 10420 16180 Unauthorized: /api/support/faq/
WARNING 2025-05-31 19:12:41,591 basehttp 10420 16180 "GET /api/support/faq/ HTTP/1.1" 401 58
INFO 2025-05-31 19:13:29,317 autoreload 17932 17144 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\support\views.py changed, reloading.
INFO 2025-05-31 19:13:30,846 autoreload 3640 792 Watching for file changes with StatReloader
WARNING 2025-05-31 19:13:39,977 log 10420 16180 Unauthorized: /api/support/faq/
WARNING 2025-05-31 19:13:39,977 basehttp 10420 16180 "GET /api/support/faq/ HTTP/1.1" 401 58
INFO 2025-05-31 19:14:23,874 basehttp 3640 2112 "GET /api/support/faq/ HTTP/1.1" 200 52
INFO 2025-05-31 19:14:34,136 basehttp 3640 2112 "POST /api/support/contact/ HTTP/1.1" 201 128
INFO 2025-05-31 19:14:44,532 basehttp 3640 2112 "POST /api/auth/login/ HTTP/1.1" 200 816
WARNING 2025-05-31 19:15:03,678 log 3640 2112 Unauthorized: /api/admin/users/
WARNING 2025-05-31 19:15:03,678 basehttp 3640 2112 "GET /api/admin/users/ HTTP/1.1" 401 183
INFO 2025-05-31 19:15:13,025 basehttp 3640 2112 "POST /api/auth/login/ HTTP/1.1" 200 816
INFO 2025-05-31 19:15:24,633 basehttp 3640 2112 "GET /api/admin/users/ HTTP/1.1" 200 2114
INFO 2025-05-31 19:15:34,765 basehttp 3640 2112 "GET /api/admin/users/53bbc7bd-186d-44d1-beb1-26f6ec3665f4/ HTTP/1.1" 200 421
INFO 2025-05-31 19:15:43,759 basehttp 3640 2112 "GET /api/vip/ HTTP/1.1" 200 361
WARNING 2025-05-31 19:15:49,638 log 3640 2112 Unauthorized: /api/vip/tiers/
WARNING 2025-05-31 19:15:49,638 basehttp 3640 2112 "GET /api/vip/tiers/ HTTP/1.1" 401 58
INFO 2025-05-31 19:15:58,055 basehttp 3640 2112 "GET /api/vip/tiers/ HTTP/1.1" 200 52
INFO 2025-05-31 19:16:07,182 basehttp 3640 2112 "GET /api/live/ HTTP/1.1" 200 203
ERROR 2025-05-31 19:16:15,353 log 3640 2112 Internal Server Error: /api/live/events/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 150, in filter_queryset
    queryset = backend().filter_queryset(self.request, queryset, self)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django_filters\rest_framework\backends.py", line 66, in filter_queryset
    filterset = self.get_filterset(request, queryset, view)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django_filters\rest_framework\backends.py", line 18, in get_filterset
    filterset_class = self.get_filterset_class(view, queryset)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django_filters\rest_framework\backends.py", line 49, in get_filterset_class
    class AutoFilterSet(self.filterset_base):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django_filters\filterset.py", line 82, in __new__
    new_class.base_filters = new_class.get_filters()
                             ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django_filters\filterset.py", line 371, in get_filters
    raise TypeError(
TypeError: 'Meta.fields' must not contain non-model field names: is_vip_only
ERROR 2025-05-31 19:16:15,370 basehttp 3640 2112 "GET /api/live/events/ HTTP/1.1" 500 135596
INFO 2025-05-31 19:16:15,370 basehttp 3640 2112 - Broken pipe from ('127.0.0.1', 58470)
INFO 2025-05-31 19:16:48,895 autoreload 3640 792 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\live\views.py changed, reloading.
INFO 2025-05-31 19:16:50,287 autoreload 11564 1652 Watching for file changes with StatReloader
INFO 2025-05-31 19:17:15,972 autoreload 11564 1652 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\live\views.py changed, reloading.
INFO 2025-05-31 19:17:17,375 autoreload 7276 12656 Watching for file changes with StatReloader
INFO 2025-05-31 19:17:27,246 autoreload 7276 12656 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\live\views.py changed, reloading.
INFO 2025-05-31 19:17:29,021 autoreload 7400 4872 Watching for file changes with StatReloader
INFO 2025-05-31 19:17:38,256 autoreload 7400 4872 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\live\views.py changed, reloading.
INFO 2025-05-31 19:17:39,772 autoreload 9168 17688 Watching for file changes with StatReloader
INFO 2025-05-31 19:17:48,671 autoreload 9168 17688 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\live\views.py changed, reloading.
INFO 2025-05-31 19:17:50,134 autoreload 13608 5692 Watching for file changes with StatReloader
INFO 2025-05-31 19:18:06,099 autoreload 13608 5692 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\live\views.py changed, reloading.
INFO 2025-05-31 19:18:07,501 autoreload 1924 16720 Watching for file changes with StatReloader
ERROR 2025-05-31 19:18:24,436 log 8788 5404 Internal Server Error: /api/live/events/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 150, in filter_queryset
    queryset = backend().filter_queryset(self.request, queryset, self)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django_filters\rest_framework\backends.py", line 66, in filter_queryset
    filterset = self.get_filterset(request, queryset, view)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django_filters\rest_framework\backends.py", line 18, in get_filterset
    filterset_class = self.get_filterset_class(view, queryset)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django_filters\rest_framework\backends.py", line 49, in get_filterset_class
    class AutoFilterSet(self.filterset_base):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django_filters\filterset.py", line 82, in __new__
    new_class.base_filters = new_class.get_filters()
                             ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django_filters\filterset.py", line 371, in get_filters
    raise TypeError(
TypeError: 'Meta.fields' must not contain non-model field names: is_vip_only
ERROR 2025-05-31 19:18:24,436 basehttp 8788 5404 "GET /api/live/events/ HTTP/1.1" 500 133014
INFO 2025-05-31 19:18:24,443 basehttp 8788 5404 - Broken pipe from ('127.0.0.1', 58641)
ERROR 2025-05-31 19:19:09,784 log 1924 16568 Internal Server Error: /api/live/events/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 150, in filter_queryset
    queryset = backend().filter_queryset(self.request, queryset, self)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\filters.py", line 283, in filter_queryset
    return queryset.order_by(*ordering)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1659, in order_by
    obj.query.add_ordering(*field_names)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\sql\query.py", line 2221, in add_ordering
    self.names_to_path(item.split(LOOKUP_SEP), self.model._meta)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\sql\query.py", line 1724, in names_to_path
    raise FieldError(
django.core.exceptions.FieldError: Cannot resolve keyword 'start_time' into field. Choices are: actual_end, actual_start, allow_anonymous, allow_chat, allow_questions, allow_reactions, banner_image, co_hosts, created_at, description, event_type, host, host_id, id, is_public, max_participants, moderated_chat, moderators, peak_concurrent_viewers, polls, questions, record_session, recording_available_until, recording_url, registrations, requires_registration, scheduled_end, scheduled_start, status, theme_color, title, total_messages, total_questions, total_viewers, updated_at, viewers, vip_early_access_minutes, vip_only
ERROR 2025-05-31 19:19:09,789 basehttp 1924 16568 "GET /api/live/events/ HTTP/1.1" 500 133895
INFO 2025-05-31 19:19:09,796 basehttp 1924 16568 - Broken pipe from ('127.0.0.1', 58663)
INFO 2025-05-31 19:19:21,468 autoreload 1924 16720 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\live\views.py changed, reloading.
INFO 2025-05-31 19:19:22,944 autoreload 7400 7868 Watching for file changes with StatReloader
ERROR 2025-05-31 19:19:43,603 log 17556 4436 Internal Server Error: /api/live/events/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\generics.py", line 150, in filter_queryset
    queryset = backend().filter_queryset(self.request, queryset, self)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\filters.py", line 283, in filter_queryset
    return queryset.order_by(*ordering)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1659, in order_by
    obj.query.add_ordering(*field_names)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\sql\query.py", line 2221, in add_ordering
    self.names_to_path(item.split(LOOKUP_SEP), self.model._meta)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\sql\query.py", line 1724, in names_to_path
    raise FieldError(
django.core.exceptions.FieldError: Cannot resolve keyword 'start_time' into field. Choices are: actual_end, actual_start, allow_anonymous, allow_chat, allow_questions, allow_reactions, banner_image, co_hosts, created_at, description, event_type, host, host_id, id, is_public, max_participants, moderated_chat, moderators, peak_concurrent_viewers, polls, questions, record_session, recording_available_until, recording_url, registrations, requires_registration, scheduled_end, scheduled_start, status, theme_color, title, total_messages, total_questions, total_viewers, updated_at, viewers, vip_early_access_minutes, vip_only
ERROR 2025-05-31 19:19:43,605 basehttp 17556 4436 "GET /api/live/events/ HTTP/1.1" 500 131178
INFO 2025-05-31 19:19:43,615 basehttp 17556 4436 - Broken pipe from ('127.0.0.1', 58684)
INFO 2025-05-31 19:20:35,021 basehttp 7400 17428 "GET /api/live/events/ HTTP/1.1" 200 52
INFO 2025-05-31 19:20:45,390 basehttp 7400 17428 "GET /api/live/rooms/ HTTP/1.1" 200 52
INFO 2025-05-31 19:20:54,034 basehttp 7400 17428 "GET /api/analytics/ HTTP/1.1" 200 522
WARNING 2025-05-31 19:22:02,316 log 7400 5188 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 19:22:02,316 log 7400 1744 Unauthorized: /api/chat/rooms/
WARNING 2025-05-31 19:22:02,316 basehttp 7400 5188 "GET /api/chat/rooms/ HTTP/1.1" 401 183
WARNING 2025-05-31 19:22:02,316 basehttp 7400 1744 "GET /api/chat/rooms/ HTTP/1.1" 401 183
ERROR 2025-05-31 19:22:15,140 log 7400 17428 Internal Server Error: /api/live/events/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 18, in create
    serializer.is_valid(raise_exception=True)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 227, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 426, in run_validation
    value = self.to_internal_value(data)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 479, in to_internal_value
    for field in fields:
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 362, in _writable_fields
    for field in self.fields.values():
                 ^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 356, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1076, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1222, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1340, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `start_time` is not valid for model `LiveEvent`.
ERROR 2025-05-31 19:22:15,149 basehttp 7400 17428 "POST /api/live/events/ HTTP/1.1" 500 160171
INFO 2025-05-31 19:22:15,149 basehttp 7400 17428 - Broken pipe from ('127.0.0.1', 58711)
INFO 2025-05-31 19:22:41,688 autoreload 7400 7868 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\live\serializers.py changed, reloading.
INFO 2025-05-31 19:22:43,480 autoreload 7216 16528 Watching for file changes with StatReloader
INFO 2025-05-31 19:22:52,773 autoreload 7216 16528 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\live\serializers.py changed, reloading.
INFO 2025-05-31 19:22:54,369 autoreload 8664 15960 Watching for file changes with StatReloader
INFO 2025-05-31 19:23:08,578 autoreload 8664 15960 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\live\serializers.py changed, reloading.
INFO 2025-05-31 19:23:10,142 autoreload 19008 14724 Watching for file changes with StatReloader
ERROR 2025-05-31 19:23:29,502 log 18664 5244 Internal Server Error: /api/live/events/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 18, in create
    serializer.is_valid(raise_exception=True)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 227, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 426, in run_validation
    value = self.to_internal_value(data)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 479, in to_internal_value
    for field in fields:
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 362, in _writable_fields
    for field in self.fields.values():
                 ^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 356, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1076, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1222, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1340, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `start_time` is not valid for model `LiveEvent`.
ERROR 2025-05-31 19:23:29,510 basehttp 18664 5244 "POST /api/live/events/ HTTP/1.1" 500 157454
INFO 2025-05-31 19:23:29,520 basehttp 18664 5244 - Broken pipe from ('127.0.0.1', 58804)
WARNING 2025-06-01 10:28:01,166 log 19008 5092 Unauthorized: /api/live/events/
WARNING 2025-06-01 10:28:01,168 basehttp 19008 5092 "POST /api/live/events/ HTTP/1.1" 401 183
INFO 2025-06-01 10:28:13,187 basehttp 19008 5092 "POST /api/auth/login/ HTTP/1.1" 200 816
ERROR 2025-06-01 10:28:24,111 log 19008 5092 Internal Server Error: /api/live/events/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 20, in create
    headers = self.get_success_headers(serializer.data)
                                       ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 555, in data
    ret = super().data
          ^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 522, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\fields.py", line 1838, in to_representation
    return method(value)
           ^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\live\serializers.py", line 40, in get_registration_count
    return obj.registrations.filter(status='confirmed').count()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\sql\query.py", line 1545, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\sql\query.py", line 1576, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\sql\query.py", line 1426, in build_filter
    lookups, parts, reffed_expression = self.solve_lookup_type(arg, summarize)
                                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\sql\query.py", line 1236, in solve_lookup_type
    _, field, _, lookup_parts = self.names_to_path(lookup_splitted, self.get_meta())
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\sql\query.py", line 1724, in names_to_path
    raise FieldError(
django.core.exceptions.FieldError: Cannot resolve keyword 'status' into field. Choices are: attendance_duration, attended, event, event_id, follow_up_sent, guest_email, guest_name, id, registered_at, reminder_sent, user, user_id
ERROR 2025-06-01 10:28:24,111 basehttp 19008 5092 "POST /api/live/events/ HTTP/1.1" 500 192290
INFO 2025-06-01 10:28:24,120 basehttp 19008 5092 - Broken pipe from ('127.0.0.1', 59697)
INFO 2025-06-01 10:28:53,932 autoreload 19008 14724 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\live\serializers.py changed, reloading.
INFO 2025-06-01 10:28:55,243 autoreload 21352 21388 Watching for file changes with StatReloader
ERROR 2025-06-01 10:29:08,740 log 14128 16576 Internal Server Error: /api/live/events/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 20, in create
    headers = self.get_success_headers(serializer.data)
                                       ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 555, in data
    ret = super().data
          ^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 522, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\fields.py", line 1838, in to_representation
    return method(value)
           ^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\live\serializers.py", line 40, in get_registration_count
    return obj.registrations.count()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\sql\query.py", line 1545, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\sql\query.py", line 1576, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\sql\query.py", line 1426, in build_filter
    lookups, parts, reffed_expression = self.solve_lookup_type(arg, summarize)
                                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\sql\query.py", line 1236, in solve_lookup_type
    _, field, _, lookup_parts = self.names_to_path(lookup_splitted, self.get_meta())
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\sql\query.py", line 1724, in names_to_path
    raise FieldError(
django.core.exceptions.FieldError: Cannot resolve keyword 'status' into field. Choices are: attendance_duration, attended, event, event_id, follow_up_sent, guest_email, guest_name, id, registered_at, reminder_sent, user, user_id
ERROR 2025-06-01 10:29:08,759 basehttp 14128 16576 "POST /api/live/events/ HTTP/1.1" 500 189499
INFO 2025-06-01 10:29:08,762 basehttp 14128 16576 - Broken pipe from ('127.0.0.1', 59740)
INFO 2025-06-01 10:29:34,501 autoreload 21352 21388 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\live\serializers.py changed, reloading.
INFO 2025-06-01 10:29:36,005 autoreload 20660 20812 Watching for file changes with StatReloader
INFO 2025-06-01 10:29:46,083 autoreload 20660 20812 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\live\serializers.py changed, reloading.
INFO 2025-06-01 10:29:47,269 autoreload 16464 16876 Watching for file changes with StatReloader
ERROR 2025-06-01 10:30:05,810 log 14128 5124 Internal Server Error: /api/live/events/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 20, in create
    headers = self.get_success_headers(serializer.data)
                                       ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 555, in data
    ret = super().data
          ^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 522, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\fields.py", line 1838, in to_representation
    return method(value)
           ^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\live\serializers.py", line 40, in get_registration_count
    return obj.registrations.count()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\sql\query.py", line 1545, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\sql\query.py", line 1576, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\sql\query.py", line 1426, in build_filter
    lookups, parts, reffed_expression = self.solve_lookup_type(arg, summarize)
                                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\sql\query.py", line 1236, in solve_lookup_type
    _, field, _, lookup_parts = self.names_to_path(lookup_splitted, self.get_meta())
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\db\models\sql\query.py", line 1724, in names_to_path
    raise FieldError(
django.core.exceptions.FieldError: Cannot resolve keyword 'status' into field. Choices are: attendance_duration, attended, event, event_id, follow_up_sent, guest_email, guest_name, id, registered_at, reminder_sent, user, user_id
ERROR 2025-06-01 10:30:05,810 basehttp 14128 5124 "POST /api/live/events/ HTTP/1.1" 500 189499
INFO 2025-06-01 10:30:05,810 basehttp 14128 5124 - Broken pipe from ('127.0.0.1', 59780)
INFO 2025-06-01 10:31:11,015 basehttp 16464 20128 "POST /api/live/events/ HTTP/1.1" 201 728
INFO 2025-06-01 10:31:22,805 basehttp 16464 20128 "GET /api/live/events/ HTTP/1.1" 200 2967
ERROR 2025-06-01 10:31:30,910 log 16464 20128 Internal Server Error: /api/live/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 18, in create
    serializer.is_valid(raise_exception=True)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 227, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 426, in run_validation
    value = self.to_internal_value(data)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 479, in to_internal_value
    for field in fields:
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 362, in _writable_fields
    for field in self.fields.values():
                 ^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 356, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1076, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1222, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1340, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `room_type` is not valid for model `LiveRoom`.
ERROR 2025-06-01 10:31:30,910 basehttp 16464 20128 "POST /api/live/rooms/ HTTP/1.1" 500 158605
INFO 2025-06-01 10:31:30,910 basehttp 16464 20128 - Broken pipe from ('127.0.0.1', 59803)
INFO 2025-06-01 10:32:00,343 autoreload 16464 16876 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\live\serializers.py changed, reloading.
INFO 2025-06-01 10:32:01,656 autoreload 19620 19568 Watching for file changes with StatReloader
INFO 2025-06-01 10:32:15,599 autoreload 19620 19568 C:\Users\<USER>\Documents\augment-projects\diagramm\backend\apps\live\serializers.py changed, reloading.
INFO 2025-06-01 10:32:17,004 autoreload 19900 19580 Watching for file changes with StatReloader
ERROR 2025-06-01 10:32:31,430 log 13700 10828 Internal Server Error: /api/live/rooms/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 18, in create
    serializer.is_valid(raise_exception=True)
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 227, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 426, in run_validation
    value = self.to_internal_value(data)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 479, in to_internal_value
    for field in fields:
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 362, in _writable_fields
    for field in self.fields.values():
                 ^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 356, in fields
    for key, value in self.get_fields().items():
                      ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1076, in get_fields
    field_class, field_kwargs = self.build_field(
                                ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1222, in build_field
    return self.build_unknown_field(field_name, model_class)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\diagramm\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 1340, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `room_type` is not valid for model `LiveRoom`.
ERROR 2025-06-01 10:32:31,432 basehttp 13700 10828 "POST /api/live/rooms/ HTTP/1.1" 500 155888
INFO 2025-06-01 10:32:31,438 basehttp 13700 10828 - Broken pipe from ('127.0.0.1', 59852)
INFO 2025-06-01 10:33:13,031 basehttp 19900 7604 "POST /api/live/rooms/ HTTP/1.1" 201 736
