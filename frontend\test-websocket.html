<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatFlow Pro - WebSocket Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            font-weight: bold;
            margin: 10px 0;
        }
        input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 ChatFlow Pro - WebSocket Integration Test</h1>
        
        <div class="test-section info">
            <h3>📋 WebSocket Test Overview</h3>
            <p>This page tests the WebSocket connection between frontend and backend using JWT authentication.</p>
        </div>

        <div class="test-section">
            <h3>🔐 Step 1: Login & Get Token</h3>
            <button onclick="loginAndGetToken()">Login & Get JWT Token</button>
            <div id="auth-status" class="status"></div>
            <pre id="auth-result"></pre>
        </div>

        <div class="test-section">
            <h3>🔌 Step 2: Test WebSocket Connection</h3>
            <input type="text" id="room-id" placeholder="Enter Room ID (or leave empty for test room)" value="test-room-123">
            <button onclick="connectWebSocket()" id="connect-btn" disabled>Connect to WebSocket</button>
            <button onclick="disconnectWebSocket()" id="disconnect-btn" disabled>Disconnect</button>
            <div id="ws-status" class="status"></div>
            <pre id="ws-result"></pre>
        </div>

        <div class="test-section">
            <h3>💬 Step 3: Test Message Sending</h3>
            <input type="text" id="message-input" placeholder="Enter message to send" value="Hello from WebSocket test!">
            <button onclick="sendMessage()" id="send-btn" disabled>Send Message</button>
            <div id="msg-status" class="status"></div>
            <pre id="msg-result"></pre>
        </div>

        <div class="test-section">
            <h3>📊 Connection Status</h3>
            <div id="connection-status" class="status">Not connected</div>
        </div>
    </div>

    <script>
        let authToken = null;
        let websocket = null;
        let messageLog = [];

        function updateStatus(section, message, isSuccess = true) {
            const statusEl = document.getElementById(`${section}-status`);
            statusEl.textContent = message;
            statusEl.className = `status ${isSuccess ? 'success' : 'error'}`;
        }

        function updateResult(section, data) {
            const resultEl = document.getElementById(`${section}-result`);
            if (typeof data === 'object') {
                resultEl.textContent = JSON.stringify(data, null, 2);
            } else {
                resultEl.textContent = data;
            }
        }

        function updateConnectionStatus(status) {
            const statusEl = document.getElementById('connection-status');
            statusEl.textContent = status;
            statusEl.className = `status ${status.includes('Connected') ? 'success' : 'error'}`;
        }

        function updateButtons() {
            document.getElementById('connect-btn').disabled = !authToken || (websocket && websocket.readyState === WebSocket.OPEN);
            document.getElementById('disconnect-btn').disabled = !websocket || websocket.readyState !== WebSocket.OPEN;
            document.getElementById('send-btn').disabled = !websocket || websocket.readyState !== WebSocket.OPEN;
        }

        async function loginAndGetToken() {
            try {
                updateStatus('auth', 'Logging in...');
                
                const response = await fetch('http://localhost:8000/api/auth/login/', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        username: '<EMAIL>',
                        password: 'admin123'
                    })
                });

                const data = await response.json();
                
                if (response.ok && data.tokens) {
                    authToken = data.tokens.access;
                    updateStatus('auth', '✅ Login successful! JWT token obtained.');
                    updateResult('auth', { 
                        message: 'Login successful',
                        user: data.user,
                        token_preview: authToken.substring(0, 50) + '...'
                    });
                    updateButtons();
                } else {
                    throw new Error(data.error || 'Login failed');
                }
            } catch (error) {
                updateStatus('auth', `❌ Login failed: ${error.message}`, false);
                updateResult('auth', { error: error.message });
            }
        }

        function connectWebSocket() {
            if (!authToken) {
                updateStatus('ws', '❌ No auth token. Please login first.', false);
                return;
            }

            const roomId = document.getElementById('room-id').value || 'test-room-123';
            const wsUrl = `ws://localhost:8000/ws/chat/${roomId}/?token=${authToken}`;
            
            updateStatus('ws', 'Connecting to WebSocket...');
            updateResult('ws', `Connecting to: ${wsUrl}`);

            try {
                websocket = new WebSocket(wsUrl);

                websocket.onopen = function(event) {
                    updateStatus('ws', '✅ WebSocket connected successfully!');
                    updateConnectionStatus('Connected to room: ' + roomId);
                    updateButtons();
                    
                    messageLog.push({
                        timestamp: new Date().toISOString(),
                        type: 'connection',
                        message: 'WebSocket connection opened'
                    });
                    updateResult('ws', messageLog);
                };

                websocket.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    messageLog.push({
                        timestamp: new Date().toISOString(),
                        type: 'received',
                        data: data
                    });
                    updateResult('ws', messageLog);
                    updateStatus('msg', `📨 Received: ${data.type || 'message'}`);
                };

                websocket.onclose = function(event) {
                    updateStatus('ws', `🔌 WebSocket closed (Code: ${event.code})`, false);
                    updateConnectionStatus('Disconnected');
                    updateButtons();
                    
                    messageLog.push({
                        timestamp: new Date().toISOString(),
                        type: 'disconnection',
                        code: event.code,
                        reason: event.reason
                    });
                    updateResult('ws', messageLog);
                };

                websocket.onerror = function(error) {
                    updateStatus('ws', '❌ WebSocket error occurred', false);
                    updateConnectionStatus('Error');
                    updateButtons();
                    
                    messageLog.push({
                        timestamp: new Date().toISOString(),
                        type: 'error',
                        error: error.toString()
                    });
                    updateResult('ws', messageLog);
                };

            } catch (error) {
                updateStatus('ws', `❌ Failed to create WebSocket: ${error.message}`, false);
                updateResult('ws', { error: error.message });
            }
        }

        function disconnectWebSocket() {
            if (websocket) {
                websocket.close();
                websocket = null;
                updateButtons();
            }
        }

        function sendMessage() {
            if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                updateStatus('msg', '❌ WebSocket not connected', false);
                return;
            }

            const messageText = document.getElementById('message-input').value;
            if (!messageText.trim()) {
                updateStatus('msg', '❌ Please enter a message', false);
                return;
            }

            const message = {
                type: 'chat_message',
                content: messageText.trim()
            };

            try {
                websocket.send(JSON.stringify(message));
                updateStatus('msg', '✅ Message sent successfully!');
                
                messageLog.push({
                    timestamp: new Date().toISOString(),
                    type: 'sent',
                    message: message
                });
                updateResult('msg', messageLog);
                
                // Clear the input
                document.getElementById('message-input').value = '';
            } catch (error) {
                updateStatus('msg', `❌ Failed to send message: ${error.message}`, false);
                updateResult('msg', { error: error.message });
            }
        }

        // Initialize
        updateButtons();
        updateConnectionStatus('Not connected');
    </script>
</body>
</html>
